package com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo;

import com.gosun.zhjg.prison.room.terminal.modules.home.dto.BaseSystemRuleConfigKeyValueDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 防误放终端信息
 */
@Data
public class FwfDeviceInfoVO {
	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	private String id;
	/**
	 * 序列号
	 */
	@ApiModelProperty(value = "序列号")
	private String serialNumber;
	/**
	 * 设备名称
	 */
	@ApiModelProperty("设备名称")
	private String deviceName;
	/**
	 * 监所
	 */
	@ApiModelProperty(value = "监所")
	private String prisonId;

	@ApiModelProperty(value = "监所名称")
	private String prisonName;

	@ApiModelProperty(value = "系统配置")
	private List<BaseSystemRuleConfigKeyValueDto> ruleConfigList;

	@ApiModelProperty(value = "app编码")
	private String appCode;
}
