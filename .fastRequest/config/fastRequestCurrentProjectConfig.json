{"apiDocTemplate": "#if (${namingPolicy}=='byDoc')\n$H1 ${methodDescription}\n#else\n$H1 $!{methodName}\n\n$H3 Method description\n\n```\n$!{methodDescription}\n```\n#end\n\n> URL: $!{url}\n>\n> Origin Url: $!{originUrl}\n>\n> Type: $!{methodType}\n\n\n$H3 Request headers\n\n|Header Name| Header Value|\n|---------|------|\n#foreach( $h in ${headerList})\n|$h.type|$h.value|\n#end\n\n$H3 Parameters\n\n$H5 Path parameters\n\n| Parameter | Type | Value | Description |\n|---------|------|------|------------|\n#foreach( $node in ${pathKeyValueList})\n|$node.key|$!{node.type}|$!{node.value}|$!{node.comment}|\n#end\n\n\n$H5 URL parameters\n\n|Required| Parameter | Type | Value | Description |\n|---------|---------|------|------|------------|\n#foreach( $node in ${urlParamsKeyValueList})\n|$!{node.enabled}|$!{node.key}|$!{node.type}|$!{node.value}|$!{node.comment}|\n#end\n\n\n$H5 Body parameters\n\n$H6 JSON\n\n```\n${jsonParam}\n```\n\n$H6 JSON document\n\n```\n${jsonParamDocument}\n```\n\n\n$H5 Form URL-Encoded\n|Required| Parameter | Type | Value | Description |\n|---------|---------|------|------|------------|\n#foreach( $node in ${urlEncodedKeyValueList})\n|$!{node.enabled}|$!{node.key}|$!{node.type}|$!{node.value}|$!{node.comment}|\n#end\n\n\n$H5 Multipart\n|Required | Parameter | Type | Value | Description |\n|---------|---------|------|------|------------|\n#foreach( $node in ${multipartKeyValueList})\n|$!{node.enabled}|$!{node.key}|$!{node.type}|$!{node.value}|$!{node.comment}|\n#end\n\n\n$H3 Response\n\n$H5 Response example\n\n```\n$!{responseExample}\n```\n\n$H5 Response document\n```\n$!{returnDocument}\n```\n\n\n", "apifoxSetting": {"domain": "https://api.apifox.com", "syncAfterSave": false}, "dataList": [{"hostGroup": [{"env": "local", "url": "http://localhost:9111"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}, {"env": "conf,dev,sdk,as", "url": "http://localhost:${conf.server.port.bsp}"}], "name": "rs-adapter-zhjg-biz"}, {"hostGroup": [{"env": "local", "url": "http://localhost:8080"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}], "name": "rs-adapter-zhjg-api"}, {"hostGroup": [{"env": "local", "url": "http://localhost:9300"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}, {"env": "conf,dev,sdk,as", "url": "http://localhost:${conf.server.port.tem}"}], "name": "rs-module-tem-biz"}, {"hostGroup": [{"env": "local", "url": "http://localhost:8080"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}], "name": "rs-starter-oss"}, {"hostGroup": [{"env": "local", "url": "http://localhost:9100"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}, {"env": "conf,dev,sdk,as", "url": "http://localhost:${conf.server.port.acp}"}], "name": "rs-module-acp-biz"}, {"hostGroup": [{"env": "local", "url": "http://localhost:9200"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}], "name": "rs-module-base"}, {"hostGroup": [{"env": "local", "url": "http://localhost:9200"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}, {"env": "conf,dev,sdk,as", "url": "http://localhost:${conf.server.port.ihc}"}], "name": "rs-module-ihc-biz"}, {"hostGroup": [{"env": "local", "url": "http://localhost:9600"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}], "name": "rs-module-pam-biz"}, {"hostGroup": [{"env": "local", "url": "http://localhost:8080"}, {"env": "pro", "url": ""}, {"env": "ihc", "url": ""}], "name": "fast-common-module"}, {"hostGroup": [{"env": "pro", "url": ""}, {"env": "ihc", "url": ""}], "name": "rs-module-integ-biz"}, {"hostGroup": [{"env": "local", "url": "http://localhost:9919"}, {"env": "pro", "url": "http://*************:9919"}, {"env": "ihc", "url": "9200"}], "name": "rs-module-devops-biz"}], "envList": ["local", "pro", "ihc", "conf,dev,sdk,as"], "headerList": [{"enabled": true, "type": "", "value": ""}], "ignoreParseFieldList": [], "maxDescriptionLength": -1, "pmCollectionId": "", "postScript": "", "preScript": "import cn.hutool.core.util.CharsetUtil\nimport cn.hutool.core.util.StrUtil\nimport cn.hutool.crypto.digest.DigestUtil\nimport cn.hutool.http.HttpRequest\nimport cn.hutool.http.HttpResponse\nimport cn.hutool.http.HttpUtil\nimport com.alibaba.fastjson.JSON\n\nHttpRequest myRequest = HttpUtil.createPost(\"http://*************:999/bsp-uac/oauth/token\");\nmyRequest.form(\"username\", \"sk06\")\n        .form(\"password\", \"y93bjoQhsjSYSAVw19dTMFOKaIL139w7ZBFcZH8zKMQ=\")\n        .form(\"client_id\", \"user_client\")\n        .form(\"client_secret\", \"user_client\")\n        .form(\"scope\", \"trust\")\n        .form(\"grant_type\", \"password\")\n        .form(\"app_mark\", \"bsp\");\nHttpResponse myResponse = myRequest.execute();\nif (myResponse.isOk()) {\n    String token = JSON.parseObject(myResponse.body()).getString(\"access_token\");\n    console.print(token);\n    request.header(\"Authorization\", \"Bearer \"+token)\n\n}\n\n", "projectList": ["rs-adapter-zhjg-biz", "rs-adapter-zhjg-api", "rs-module-tem-biz", "rs-starter-oss", "rs-module-acp-biz", "rs-module-base", "rs-module-ihc-biz", "rs-module-pam-biz", "fast-common-module", "rs-module-integ-biz", "rs-module-devops-biz"], "syncModel": {"branch": "master", "domain": "https://github.com", "enabled": false, "gitToken": "", "namingPolicy": "byDoc", "owner": "", "repo": "", "repoUrl": "", "syncAfterRun": false, "type": "github"}, "syncPmAfterSave": false, "urlEncodedKeyValueList": [], "urlParamsKeyValueList": [], "urlSuffix": "", "workspaceId": ""}