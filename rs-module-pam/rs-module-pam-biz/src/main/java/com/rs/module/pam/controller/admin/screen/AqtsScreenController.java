package com.rs.module.pam.controller.admin.screen;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.screen.vo.FocusOnPersonnelRespVO;
import com.rs.module.pam.controller.admin.screen.vo.WgdjRespVO;
import com.rs.module.pam.entity.screen.FocusOnPersonnelDO;
import com.rs.module.pam.entity.screen.WgdjDO;
import com.rs.module.pam.service.screen.AqdtScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "大屏-数字孪生平台-安全态势")
@RestController
@RequestMapping("/pam/screen/aqts")
@Validated
public class AqtsScreenController {

    @Resource
    private AqdtScreenService aqdtScreenService;

    @GetMapping("/fxryfb")
    @ApiOperation(value = "风险人员分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    public CommonResult<JSONObject> fxryfb(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.fxryfb(code));
    }

    @GetMapping("/fxryqs")
    @ApiOperation(value = "风险人员趋势")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "1 近一周  2 近一月", required = true)
    })
    public CommonResult<JSONObject> fxryqs(@RequestParam("code") String code,
                                           @RequestParam(value = "type", defaultValue = "1") String type) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.fxryqs(code, type));
    }

    @GetMapping("/gzry-count")
    @ApiOperation(value = "关注人员-总数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
    })
    public CommonResult<JSONObject> gzryCount(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.gzryCount(code));
    }

    @GetMapping("/gzry-page")
    @ApiOperation(value = "关注人员-列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "bizType", value = "业务类型", required = false)
    })
    public CommonResult<PageResult<FocusOnPersonnelRespVO>> gzryPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                     @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                     @RequestParam("code") String code,
                                                                     @RequestParam(name = "bizType", required = false) String bizType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<FocusOnPersonnelDO> pageResult = aqdtScreenService.gzryPage(pageNo, pageSize, code, bizType);
        return success(BeanUtils.toBean(pageResult, FocusOnPersonnelRespVO.class));
    }

    @GetMapping("/wgqs")
    @ApiOperation(value = "违规趋势")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "1 近一周  2 近一月", required = true)
    })
    public CommonResult<JSONObject> wgqs(@RequestParam("code") String code,
                                         @RequestParam(value = "type", defaultValue = "1") String type) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.wgqs(code, type));
    }


    @GetMapping("/wggj-page")
    @ApiOperation(value = "违规告警分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "0 今日  1 近一周  2 近一月", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "wglb", value = "违规类别 1 全部， 2人工巡查， 3智能终端", required = true),
            @ApiImplicitParam(name = "handleStatus", value = "处理类型 1全部 2未处理 3已处理 ", required = true),
    })
    public CommonResult<PageResult<WgdjRespVO>> wggjPage(@RequestParam("code") String code,
                                                     @RequestParam(value = "type", defaultValue = "0") String type,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                     @RequestParam(name = "wglb", defaultValue = "1") String wglb,
                                                     @RequestParam(name = "handleStatus", defaultValue = "1") String handleStatus) {
        Assert.notBlank(code, "code不能为空");
        PageResult<WgdjDO> pageResult = aqdtScreenService.wggjPage(pageNo, pageSize, code, type, wglb, handleStatus);
        return success(BeanUtils.toBean(pageResult, WgdjRespVO.class));
    }

    @GetMapping("/wggj-count")
    @ApiOperation(value = "违规告警数字统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "0 今日  1 近一周  2 近一月", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "wglb", value = "违规类别 1 全部， 2人工巡查， 3智能终端", required = true)
    })
    public CommonResult<JSONObject> wggjCount(@RequestParam("code") String code,
                                                         @RequestParam(value = "type", defaultValue = "0") String type,
                                                         @RequestParam(name = "wglb", defaultValue = "1") String wglb) {
        Assert.notBlank(code, "code不能为空");
        return success(aqdtScreenService.wggjCount(code, type, wglb));
    }

}
