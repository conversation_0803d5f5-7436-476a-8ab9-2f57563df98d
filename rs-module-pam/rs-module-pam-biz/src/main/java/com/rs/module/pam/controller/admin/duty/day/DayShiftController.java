package com.rs.module.pam.controller.admin.duty.day;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutyShiftSaveReqVO;
import com.rs.module.pam.controller.admin.duty.day.vo.DayShiftRespVO;
import com.rs.module.pam.controller.admin.duty.vo.ShiftRespVO;
import com.rs.module.pam.entity.duty.ShiftDO;
import com.rs.module.pam.entity.duty.day.DayDutyShiftDO;
import com.rs.module.pam.service.duty.day.DayDutyShiftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值日管理-值日班次")
@RestController
@RequestMapping("/pam/day/duty/shift")
@Validated
public class DayShiftController {

    @Resource
    private DayDutyShiftService dayDutyShiftService;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @GetMapping("/getShift")
    @ApiOperation(value = "监室值日-获取班次信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomId", value = "监室id")
    })
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#reqVO.roomId}}\"}")
    public CommonResult<List<DayShiftRespVO>> getShift(@RequestParam("orgCode") String orgCode, @RequestParam("roomId") String roomId) {
        return success(dayDutyShiftService.getShift(orgCode, roomId));
    }

    /**
     * 监室值日-班次管理-保存编辑
     */
    @PostMapping("/shiftManageSave")
    @ApiOperation(value = "监室值日-班次管理-保存编辑")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "", value = "值日班次列表")
    })
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#reqVO.roomId}}\"}")
    public CommonResult<String> shiftManageSave(@RequestBody @Validated DayDutyShiftSaveReqVO reqVO) {
        dayDutyShiftService.shiftManageSave(reqVO);
        return success("保存成功");
    }

    @RequestMapping(value = "/initialize", method = RequestMethod.GET)
    @ApiOperation(value = "值日班次初始化")
    public CommonResult<?> initialize() {
        List<AreaPrisonRoomDO> roomList = areaPrisonRoomService.list();
        List<DayDutyShiftDO> shiftList = dayDutyShiftService.list();
        List<String> idList = shiftList.stream().map(config -> config.getRoomId()).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (AreaPrisonRoomDO prisonRoomDO : roomList) {
            if (idList.contains(prisonRoomDO.getId())) {
                continue;
            }
            try {
                dayDutyShiftService.createDefaultShift(prisonRoomDO.getOrgCode(), prisonRoomDO.getId());
                result.add(String.format("机构：%s，监室：%s值日班次初始化成功", prisonRoomDO.getOrgCode(), prisonRoomDO.getId()));
            } catch (Exception e) {
                result.add(String.format("机构：%s，监室：%s值日班次初始化失败：", prisonRoomDO.getOrgCode(), prisonRoomDO.getId(), e.getMessage()));
            }
        }
        return success(result);
    }

}
