package com.rs.module.pam.controller.admin.daily;

import cn.hutool.core.util.StrUtil;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.pam.controller.admin.daily.vo.CleanDailyRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanDaliySaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanPoliceRectificationVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanSaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanleaderInstructionVO;
import com.rs.module.pam.service.daily.CleanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "安全检查-安全大检查、日常清监检查")
@RestController
@RequestMapping("/pam/daily/clean")
@Validated
public class CleanController {

    @Resource
    private CleanService cleanService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-安全大检查")
    @BusTrace(busType = BusTypeEnum.YEWU_AQJC,
            content = "{\"民警\":\"{{#createReqVO.involvementUserName}}\",\"检查类型\":\"{{#createReqVO.checkType == '1'? '日常清监检查':'安全大检查'}}\",\"检查内容\":\"{{#createReqVO.checkContent}}\",\"检查时间\":\"{{#createReqVO.checkTime}}\",\"是否存在安全隐患\":\"{{#createReqVO.isHiddenDanger == '1'? '是':'否'}}\",\"是否存在违禁品\":\"{{#createReqVO.isViolation == '1'? '是':'否'}}\"}")
    public CommonResult<String> createClean(@Valid @RequestBody CleanSaveReqVO createReqVO) {
        return success(cleanService.createClean(createReqVO));
    }

    @PostMapping("/createCleanDaily")
    @ApiOperation(value = "创建-日常清监")
    @BusTrace(busType = BusTypeEnum.YEWU_AQJC,
            content = "{\"民警\":\"{{#createReqVO.involvementUserName}}\",\"检查类型\":\"日常清监检查\",\"检查内容\":\"{{#createReqVO.violationContent}}\",\"检查时间\":\"{{#createReqVO.checkTime}}\",\"是否存在违禁品\":\"{{#createReqVO.isViolation == '1'? '是':'否'}}\"}")
    public CommonResult<String> createCleanDaily(@Valid @RequestBody CleanDaliySaveReqVO createReqVO) {
        return success(cleanService.createCleanDaily(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-安全大检查")
    public CommonResult<Boolean> updateClean(@Valid @RequestBody CleanSaveReqVO updateReqVO) {
        cleanService.updateClean(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-安全大检查")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CleanRespVO> getClean(@RequestParam("id") String id) {
        CleanRespVO clean = cleanService.getCleanRespVO(id);
        return success(clean);
    }

    @GetMapping("/getCleanDaily")
    @ApiOperation(value = "获得-日常清监")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号"),
            @ApiImplicitParam(name = "pcId", value = "消息ID", required = false)
    })
    public CommonResult<CleanDailyRespVO> getCleanDaily(@RequestParam("id") String id,
                                                        @RequestParam(name = "pcId", required = false) String pcId) {
        if (StrUtil.isNotBlank(pcId)) {
            SendMessageUtil.ProcessAlertMsg(pcId, SessionUserUtil.getSessionUser().getIdCard(), "pc");
        }
        CleanDailyRespVO clean = cleanService.getCleanDaily(id);
        return success(clean);
    }

    @PostMapping("/policeRectification")
    @ApiOperation(value = "管教整改-安全大检查")
    public CommonResult<Boolean> policeRectification(@RequestBody CleanPoliceRectificationVO reqVO) {
        cleanService.policeRectification(reqVO);
        return success(true);
    }

    @PostMapping("/leaderInstruction")
    @ApiOperation(value = "领导批示-安全大检查")
    public CommonResult<Boolean> leaderInstruction(@RequestBody CleanleaderInstructionVO reqVO) {
        cleanService.leaderInstruction(reqVO);
        return success(true);
    }


}
