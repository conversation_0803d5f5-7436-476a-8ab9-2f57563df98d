package com.rs.module.pam.controller.admin.duty.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalTime;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-值班班次 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShiftRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("班次名称")
    private String shiftName;
    @ApiModelProperty("开始时间类型（1：当天，2：次日）")
    private String startTimeType;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间类型（1：当天，2：次日）")
    private String endTimeType;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("生效开始时间")
    private Date effectiveStartDate;
    @ApiModelProperty("生效结束时间")
    private Date effectiveEndDate;
    @ApiModelProperty("排序")
    private Integer sort;
    @ApiModelProperty("持续时间")
    private String continueTime;
}
