package com.rs.module.pam.controller.admin.represent.vo.extra;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.gosun.zhjg.common.context.PageSortMapping;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;


@Data
@ApiModel("监室点名信息vo")
@EqualsAndHashCode(callSuper = true)
public class RoomInfoRepresentVO extends BaseVO implements TransPojo {


    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "机构编号")
    private String orgCode;

    @ApiModelProperty(value = "监区id")
    private String areaId;

    @ApiModelProperty(value = "监区name")
    private String areaName;

    @ApiModelProperty(value = "监室id")
    private String roomId;

    @ApiModelProperty(value = "监室name")
    @PageSortMapping("roomName")
    private String roomName;

    @ApiModelProperty(value = "监室性别")
    private String roomSex;

    @ApiModelProperty(value = "点名批号")
    @PageSortMapping("presentNo")
    private String presentNo;

    @ApiModelProperty(value = "点名类型 0-临时点名 1-自动点名")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JSDM_DMLX")
    private Integer presentType;

    @ApiModelProperty(value = "点名类型中文")
    @PageSortMapping("presentType")
    private String presentTypeDisplayName;

    @ApiModelProperty(value = "点名状态（0：进行中，1：正常，2：异常 3:未开始")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JSDM_DMZT")
    private String presentStatus;

    @ApiModelProperty(value = "点名状态集合")
    private String presentStatusList;

    @ApiModelProperty(value = "点名状态中文")
    @PageSortMapping("presentStatus")
    private String presentStatusDisplayName;

    @ApiModelProperty(value = "监室总关押数")
    private Integer allInNum;

    @ApiModelProperty(value = "监室在押数")
    private Integer inNum;

    @ApiModelProperty(value = "点名正常数")
    private Integer presentNum;

    @ApiModelProperty(value = "点名异常数")
    private Integer errorNum;

    @ApiModelProperty(value = "监室外出数")
    private Integer outNum;

    @ApiModelProperty(value = "报备人数")
    private Integer reportNum;

    @ApiModelProperty(value = "点名日期")
    @PageSortMapping("startTime")
    private String representDate;

    @ApiModelProperty(value = "点名时间起")
    private String startTimeStr;

    @JsonIgnore
    @ApiModelProperty(value = "点名时间起",hidden = true)
    private Date startTime;

    @ApiModelProperty(value = "点名时间止")
    private String endTimeStr;

    @JsonIgnore
    @ApiModelProperty(value = "点名时间止",hidden = true)
    private Date endTime;

    @ApiModelProperty(value = "点名人员信息")
    private List<RepresentPersonVO> representPersonVos;

    @ApiModelProperty(value = "已点名人员信息")
    private List<RepresentPersonVO> representPerson;

    @ApiModelProperty(value = "未点名人员信息")
    private List<RepresentPersonVO> NotRepresentPerson;

    @ApiModelProperty(value = "异常处置结果")
    private String errorHandleResult;

    @ApiModelProperty(value = "定时计划")
    private String configPeriod;

    @ApiModelProperty(value = "定时计划code")
    private String configPeriodCode;

    @ApiModelProperty("操作 1-视频点名")
    private Integer isVideo;

    @ApiModelProperty("1-点名类型是否包含视频点名")
    private Integer isVideoType;

    @ApiModelProperty(value = "测温正常人数")
    private Integer temperatureNum = 0;

    @ApiModelProperty(value = "温度异常人数")
    private Integer temperatureErrNum = 0;

    @ApiModelProperty(value = "温度人数全部")
    private Integer temperatureAllNum;

    @ApiModelProperty(value = "发起方 1-实战平台 2-仓外屏")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DMFQF")
    private Integer initSource;

    @ApiModelProperty(value = "发起人")
    private String operatePeopleName;

}
