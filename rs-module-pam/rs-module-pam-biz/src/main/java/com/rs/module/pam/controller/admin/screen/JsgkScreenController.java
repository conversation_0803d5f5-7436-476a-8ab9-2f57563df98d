package com.rs.module.pam.controller.admin.screen;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.service.screen.JsgkScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "大屏-数字孪生平台-监所概况")
@RestController
@RequestMapping("/pam/screen/jsgk")
@Validated
public class JsgkScreenController {

    @Resource
    private JsgkScreenService jsgkScreenService;

    @GetMapping("/jlzb")
    @ApiOperation(value = "警力装备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
    })
    public CommonResult<List<JSONObject>> jlzb(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(jsgkScreenService.jlzb(code));
    }

    @GetMapping("/sqdt")
    @ApiOperation(value = "所情动态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "1:近一周  2:近一月", required = true)
    })
    public CommonResult<JSONObject> sqdt(@RequestParam("code") String code,
                                         @RequestParam("type") String type) {
        Assert.notBlank(code, "code不能为空");
        Assert.notBlank(type, "type不能为空");
        return success(jsgkScreenService.sqdt(code, type));
    }

    @GetMapping("/topInfo")
    @ApiOperation(value = "顶部统计信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
    })
    public CommonResult<JSONObject> topInfo(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(jsgkScreenService.topInfo(code));
    }

    // 值班情况 /acp-com/acp/zh/staffDutyRecord/listByDutySingleDateIndex

    // 业务装备 功能室配备  民警/辅警数  /dam-com/dam/prison/archive/get

    // 各监区人员分布 /pam-com/pam/screen/jydt/gjqryfb

    @GetMapping("/cnwptj")
    @ApiOperation(value = "仓内外屏统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
    })
    public CommonResult<JSONObject> cnwptj(@RequestParam("code") String code) {
        Assert.notBlank(code, "code不能为空");
        return success(jsgkScreenService.cnwptj(code));
    }


    @GetMapping("/zdztfb")
    @ApiOperation(value = "终端状态分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "1：仓内屏, 2：仓外屏", required = true)
    })
    public CommonResult<JSONObject> zdztfb(@RequestParam("code") String code,
                                           @RequestParam(value = "type", defaultValue = "1") String type) {
        Assert.notBlank(code, "code不能为空");
        Assert.isTrue(Arrays.asList("1,2".split(",")).contains(type), "非法类型");
        return success(jsgkScreenService.zdztfb(code, type));
    }


}
