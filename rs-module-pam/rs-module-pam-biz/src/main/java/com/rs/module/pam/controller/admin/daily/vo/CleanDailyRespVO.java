package com.rs.module.pam.controller.admin.daily.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.module.pam.dto.JoinPositionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/8/8 17:10
 */
@ApiModel(description = "管理后台 - 监所事务管理-日常清监 Response VO")
@Data
public class CleanDailyRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;
    @ApiModelProperty("检查监室，多个逗号分割")
    private String checkRoomId;
    @ApiModelProperty("检查监室名称，多个逗号分割")
    private String checkRoomName;
    @ApiModelProperty("带队领导ID，多个逗号分割")
    private String leaderUserSfzh;
    @ApiModelProperty("带队领导ID，多个逗号分割")
    private String leaderUserName;
    @ApiModelProperty("参加民警，多个逗号分割")
    private String involvementUserSfzh;
    @ApiModelProperty("参加民警ID，多个逗号分割")
    private String involvementUserName;
    @ApiModelProperty("检查时间")
    private Date checkTime;
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记状态（字典：ZD_ZDRYGZ_GZZT）")
    private String status;
    @ApiModelProperty("登记时间")
    private Date operatorTime;
    @ApiModelProperty("是否存在违禁(0:否,1:是)")
    private Short isViolation;
    @ApiModelProperty("违禁情况")
    private String violationContent;
    @ApiModelProperty("推送内容")
    private String remarks;
    @ApiModelProperty("是否岗位协同：0 否, 1 是")
    private String isJoin;
    @ApiModelProperty("协同岗位,多个逗号隔开")
    private String joinGw;
    @ApiModelProperty("协同信息")
    private List<JoinPositionDTO> joinPositionDTOS;
    @ApiModelProperty("检查详情")
    private List<CleanDailyDetailVO> cleanDailyDetails;

}
