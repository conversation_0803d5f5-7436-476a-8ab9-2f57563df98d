package com.rs.module.pam.controller.admin.represent.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-监室点名 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RepresentRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监室name")
    private String roomName;
    @ApiModelProperty("监室总关押数")
    private Integer allInNum;
    @ApiModelProperty("监室在押数")
    private Integer inNum;
    @ApiModelProperty("监室外出数")
    private Integer outNum;
    @ApiModelProperty("点名开始时间")
    private Date startTime;
    @ApiModelProperty("点名结束时间")
    private Date endTime;
    @ApiModelProperty("点名状态（1：进行中，2：已结束，3：异常待处置）")
    private String presentStatus;
    @ApiModelProperty("已点人数")
    private Integer presentNum;
    @ApiModelProperty("异常人数")
    private Integer errorNum;
    @ApiModelProperty("异常处置结果")
    private String errorHandleResult;
    @ApiModelProperty("发起人")
    private Integer operatePeopleSfzh;
    @ApiModelProperty("发起人名字")
    private String operatePeopleName;
    @ApiModelProperty("发起时间")
    private Date operateTime;
    @ApiModelProperty("监区id")
    private String areaId;
    @ApiModelProperty("监区name")
    private String areaName;
    @ApiModelProperty("点名批号")
    private String presentNo;
    @ApiModelProperty("点名类型 0-临时点名 1-自动点名")
    private Integer presentType;
    @ApiModelProperty("有效期")
    private Integer expiryDate;
    @ApiModelProperty("1-视频点名")
    private Integer isVideo;
    @ApiModelProperty("点名测温正常人数")
    private Integer temperatureNum;
    @ApiModelProperty("点名测温异常人数")
    private Integer temperatureErrNum;
    @ApiModelProperty("监室报备人数")
    private Integer reportNum;
    @ApiModelProperty("发起方 1-实战平台 2-仓外屏")
    private Integer initSource;
}
