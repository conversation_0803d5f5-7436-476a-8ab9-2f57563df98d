package com.rs.module.pam.controller.admin.integral;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.pam.controller.admin.integral.vo.*;
import com.rs.module.pam.entity.duty.ShiftDO;
import com.rs.module.pam.entity.integral.IndicatorDO;
import com.rs.module.pam.entity.integral.RiskModelConfigDO;
import com.rs.module.pam.service.integral.IndicatorService;
import com.rs.module.pam.service.integral.RiskModelConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "分级处遇-风险模型")
@RestController
@RequestMapping("/pam/integral/riskModel")
@Validated
public class RiskModelConfigController {

    @Resource
    private RiskModelConfigService riskModelConfigService;
    @Resource
    private IndicatorService indicatorService;
    @Resource
    private BspApi bspApi;

    @PostMapping("/create")
    @ApiOperation(value = "创建风险模型信息")
    @LogRecordAnnotation(bizModule = "pam:integral:riskModel:create", operateType = LogOperateType.CREATE, title = "创建风险模型信息",
            success = "创建风险模型信息成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<RiskModelConfigDTO> createRiskModelConfig(@Valid @RequestBody RiskModelConfigSaveReqVO createReqVO) {
        RiskModelConfigDTO riskModelConfig = riskModelConfigService.createRiskModelConfig(createReqVO);
        return success(riskModelConfig);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改风险模型信息")
    @LogRecordAnnotation(bizModule = "pam:integral:riskModel:update", operateType = LogOperateType.UPDATE, title = "修改风险模型信息",
            success = "修改风险模型信息成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#update}}")
    public CommonResult<RiskModelConfigDTO> updateRiskModelConfig(@Valid @RequestBody RiskModelConfigDTO update) {
        RiskModelConfigDTO riskModelConfig = riskModelConfigService.updateRiskModelConfig(update);
        return success(riskModelConfig);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除风险模型信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteRiskModelConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           riskModelConfigService.deleteRiskModelConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得风险模型信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<RiskModelConfigDTO> getRiskModelConfig(@RequestParam("id") String id) {
        RiskModelConfigDO riskModelConfig = riskModelConfigService.getRiskModelConfig(id);
        RiskModelConfigDTO respVO = BeanUtils.toBean(riskModelConfig, RiskModelConfigDTO.class);
        respVO.setLevelConfigs(JSONObject.parseArray(riskModelConfig.getLevelConfig(), RiskModelLevelVO.class));
        // 获取权重配置
        List<IndicatorDO> indicatorTypeByRiskModel = indicatorService.getIndicatorTypeByRiskModel(id);
        respVO.setIndicatorList(BeanUtils.toBean(indicatorTypeByRiskModel, IndicatorRespVO.class));
        return success(respVO);
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得机构下所有风险模型信息")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<List<RiskModelConfigDTO>> list(@RequestParam("orgCode") String orgCode) {
        List<RiskModelConfigDO> list = riskModelConfigService.getRiskModelByOrgCode(orgCode);
        List<RiskModelConfigDTO> respList = new ArrayList<>();
        list.forEach(model -> {
            RiskModelConfigDTO respVO = BeanUtils.toBean(model, RiskModelConfigDTO.class);
            respVO.setLevelConfigs(JSONObject.parseArray(model.getLevelConfig(), RiskModelLevelVO.class));
            respList.add(respVO);
        });
        return success(respList);
    }

    @GetMapping("/getTree")
    @ApiOperation(value = "获得风险模型树")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<List<RiskModelTreeRespVO>> getTree(@RequestParam("orgCode") String orgCode) {
        // 获取风险模型信息
        List<RiskModelConfigDO> riskModelList = riskModelConfigService.getRiskModelByOrgCode(orgCode);
        List<String> riskModelIds = riskModelList.stream().map(riskModel -> riskModel.getId()).collect(Collectors.toList());
        // 获取指标信息
        List<IndicatorDO> indicatorList = indicatorService.getIndicatorByOrgCode(orgCode);

        Map<String, List<IndicatorDO>> indicatorsByRiskModel = indicatorList.stream()
                .filter(indicatorDO -> indicatorDO.getRiskModelId() != null)
                .collect(Collectors.groupingBy(IndicatorDO::getRiskModelId));

        // 构建 VO 树
        List<RiskModelTreeRespVO> riskModelTree = BeanUtils.toBean(riskModelList, RiskModelTreeRespVO.class);
        riskModelTree.forEach(model -> {
            List<IndicatorDO> modelIndicators = indicatorsByRiskModel.getOrDefault(model.getId(), Collections.emptyList());
            List<IndicatorTreeVO> tree = buildIndicatorTree(model.getId(), modelIndicators);
            model.setChildren(tree);
        });
        return success(riskModelTree);
    }

    @RequestMapping(value = "/initialize", method = RequestMethod.GET)
    @ApiOperation(value = "风险模型初始化")
    public CommonResult<?> initialize() {
        List<OrgRespDTO> orgList = bspApi.getAllOrgs();
        List<RiskModelConfigDO> configList = riskModelConfigService.list();
        List<String> orgCodeList = configList.stream().map(config -> config.getOrgCode()).collect(Collectors.toList());

        List<RiskModelConfigDO> defaultConfig = configList.stream()
                .filter(config -> "000000000000".equals(config.getOrgCode())).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (OrgRespDTO org : orgList) {
            if (orgCodeList.contains(org.getId())) {
                continue;
            }
            try {
                for (RiskModelConfigDO riskModelConfigDO : defaultConfig) {
                    riskModelConfigDO.setId(StringUtil.getGuid32());
                    riskModelConfigDO.setCityCode(org.getCityId());
                    riskModelConfigDO.setCityName(org.getCityName());
                    riskModelConfigDO.setRegCode(org.getRegionId());
                    riskModelConfigDO.setRegName(org.getRegionName());
                    riskModelConfigDO.setOrgCode(org.getId());
                    riskModelConfigDO.setOrgName(org.getName());
                }
                riskModelConfigService.saveBatch(defaultConfig);
                result.add(String.format("机构：%s，分级处遇风险模型初始化成功", org.getId()));
            } catch (Exception e) {
                result.add(String.format("机构：%s，分级处遇风险模型初始化失败：", org.getId(), e.getMessage()));
            }
        }
        return success(result);
    }

    private List<IndicatorTreeVO> buildIndicatorTree(String modelId, List<IndicatorDO> indicators) {
        if (CollectionUtil.isNull(indicators)) {
            return Collections.emptyList();
        }

        // 先按 parentId 分组
        indicators.forEach(indicatorDO -> {
            if (indicatorDO.getParentId() == null)
                indicatorDO.setParentId("");
        });
        Map<String, List<IndicatorDO>> childrenMap = indicators.stream()
                .collect(Collectors.groupingBy(IndicatorDO::getParentId));

        // 找出根节点（parentId 为空）
        List<IndicatorDO> rootNodes = indicators.stream()
                .filter(i -> StringUtil.isNullBlank(i.getParentId()))
                .collect(Collectors.toList());

        return rootNodes.stream()
                .map(i -> {
                    IndicatorTreeVO indicatorTreeVO = convertToVoWithChildren(i, childrenMap);
                    indicatorTreeVO.setParentId(modelId);
                    return indicatorTreeVO;
                })
                .collect(Collectors.toList());
    }

    private IndicatorTreeVO convertToVoWithChildren(IndicatorDO node, Map<String, List<IndicatorDO>> childrenMap) {
        IndicatorTreeVO vo = BeanUtils.toBean(node, IndicatorTreeVO.class);
        List<IndicatorTreeVO> children = childrenMap.getOrDefault(node.getId(), Collections.emptyList())
                .stream()
                .map(child -> convertToVoWithChildren(child, childrenMap))
                .collect(Collectors.toList());
        vo.setChildren(children);
        return vo;
    }

}
