package com.rs.module.pam.controller.admin.cook.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-特殊餐申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SpecialApplyRespVO extends BaseVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("配餐类型（字典：ZD_PCGL_PCLX ）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PCGL_PCLX")
    private String mealType;
    @ApiModelProperty("配餐开始日期")
    private Date mealStartTime;
    @ApiModelProperty("配餐结束时间")
    private Date mealEndTime;
    @ApiModelProperty("配餐天数")
    private Integer mealDays;
    @ApiModelProperty("用餐时段，多选逗号分割（字典：ZD_PCGL_DSLX）")
    private String mealPeriod;
    @ApiModelProperty("指定日期类型（字典：ZD_PCGL_ZDRQLX）")
    private String specifiedDateType;
    @ApiModelProperty("指定日期，多选逗号分割")
    private String specifiedDate;
    @ApiModelProperty("申请原因")
    private String reason;
    @ApiModelProperty("登记状态")
    private String regStatus;
    @ApiModelProperty("登记经办人")
    private String regOperatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String regOperatorXm;
    @ApiModelProperty("登记时间")
    private Date regTime;
    @ApiModelProperty("医生审批人身份证号")
    private String doctorApproverSfzh;
    @ApiModelProperty("医生审批人姓名")
    private String doctorApproverXm;
    @ApiModelProperty("医生审批时间")
    private Date doctorApproverTime;
    @ApiModelProperty("医生审批结果")
    private String doctorApprovalResult;
    @ApiModelProperty("医生审核意见")
    private String doctorApprovalComments;
    @ApiModelProperty("所领导审批人身份证号")
    private String leaderApproverSfzh;
    @ApiModelProperty("所领导审批人姓名")
    private String leaderApproverXm;
    @ApiModelProperty("所领导审批时间")
    private Date leaderApproverTime;
    @ApiModelProperty("所领导审批结果")
    private String leaderApprovalResult;
    @ApiModelProperty("所领导审核意见")
    private String leaderApprovalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
}
