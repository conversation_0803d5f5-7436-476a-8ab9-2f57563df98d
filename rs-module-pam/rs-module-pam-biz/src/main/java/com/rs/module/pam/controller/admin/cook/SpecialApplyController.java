package com.rs.module.pam.controller.admin.cook;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplyDoctorApproveReqVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplyLeaderApproveReqVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplyRespVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplySaveBatchReqVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplySaveReqVO;
import com.rs.module.pam.entity.cook.SpecialApplyDO;
import com.rs.module.pam.service.cook.SpecialApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室配餐-特殊餐申请")
@RestController
@RequestMapping("/pam/cook/specialApply")
@Validated
public class SpecialApplyController {

    @Resource
    private SpecialApplyService specialApplyService;

    @PostMapping("/create")
    @ApiOperation(value = "监室配餐-特殊餐申请")
    public CommonResult<String> createSpecialApply(@Valid @RequestBody SpecialApplySaveReqVO createReqVO) {
        return success(specialApplyService.createSpecialApply(createReqVO));
    }

    @PostMapping("/createBatchSpecialApply")
    @ApiOperation(value = "监室配餐-特殊餐批量申请")
    public CommonResult<String> createBatchSpecialApply(@Valid @RequestBody SpecialApplySaveBatchReqVO batchReqVO) {
        List<SpecialApplySaveBatchReqVO.JgrybInfo> jgrybInfos = batchReqVO.getJgrybInfo();
        if (CollUtil.isEmpty(jgrybInfos)) {
            throw new ServerException("请选择要申请特殊餐的监室人员");
        }
        List<SpecialApplySaveReqVO> createReqVOS = new ArrayList<>();
        for (SpecialApplySaveBatchReqVO.JgrybInfo jgrybInfo : jgrybInfos) {
            SpecialApplySaveReqVO createReqVO = new SpecialApplySaveReqVO();
            BeanUtils.copyProperties(batchReqVO, createReqVO);
            createReqVO.setJgrybm(jgrybInfo.getJgrybm());
            specialApplyService.validateExistingApplication(jgrybInfo.getJgrybm());
            createReqVO.setRoomId(jgrybInfo.getRoomId());
            createReqVOS.add(createReqVO);
        }
        specialApplyService.createSpecialApplyBatch(createReqVOS);
        return success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "监室配餐-更新特殊餐申请")
    public CommonResult<Boolean> updateSpecialApply(@Valid @RequestBody SpecialApplySaveReqVO updateReqVO) {
        specialApplyService.updateSpecialApply(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "监室配餐-获取特殊餐申请")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SpecialApplyRespVO> getSpecialApply(@RequestParam("id") String id) {
        SpecialApplyDO specialApply = specialApplyService.getSpecialApply(id);
        long between = DateUtil.between(specialApply.getMealStartTime(), specialApply.getMealEndTime(), DateUnit.DAY);
        SpecialApplyRespVO respVO = BeanUtils.toBean(specialApply, SpecialApplyRespVO.class);
        respVO.setMealDays(Integer.parseInt(String.valueOf(between)));
        return success(respVO);
    }

    @PostMapping("/updateProcessStatus")
    @ApiOperation(value = "监室配餐-更新特殊餐申请流程状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "regStatus", value = "登记状态", required = true, paramType = "query"),
            @ApiImplicitParam(name = "actInstId", value = "流程实例ID", required = false, paramType = "query")
    })
    public CommonResult<Boolean> updateProcessStatus(@NotBlank(message = "id不能为空") @RequestParam(value = "id") String id,
                                                     @NotBlank(message = "登记状态不能为空") @RequestParam(value = "regStatus") String regStatus,
                                                     @RequestParam(value = "actInstId", required = false) String actInstId) {
        return success(specialApplyService.updateProcessStatus(id, regStatus, actInstId));
    }

    @PostMapping("/doctorApprove")
    @ApiOperation(value = "监室配餐-医生审批")
    public CommonResult<Boolean> doctorApprove(@Valid @RequestBody SpecialApplyDoctorApproveReqVO approveReqVO) {
        specialApplyService.doctorApprove(approveReqVO);
        return success(true);
    }

    @PostMapping("/leaderApprove")
    @ApiOperation(value = "监室配餐-所领导审批")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody SpecialApplyLeaderApproveReqVO approveReqVO) {
        specialApplyService.leaderApprove(approveReqVO);
        return success(true);
    }

    @GetMapping("/getSpecialApplyPrisonerByJgrybm")
    @ApiOperation(value = "监室配餐-特殊餐申请人员查询")
    public CommonResult<PrisonerVwRespVO> getSpecialApplyPrisonerByJgrybm(@RequestParam(value = "jgrybm") String jgrybm) {
        PrisonerVwRespVO prisonerVwRespVO = specialApplyService.getSpecialApplyPrisonerByJgrybm(jgrybm);
        return success(prisonerVwRespVO);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "监室配餐-特殊餐申请")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSpecialApply(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            specialApplyService.deleteSpecialApply(id);
        }
        return success(true);
    }

    @PostMapping("/confirmSpecialApply")
    @ApiOperation(value = "监室配餐-管教民警确认申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "success", value = "是否成功", required = true, paramType = "query")
    })
    public CommonResult confirmSpecialApply(@RequestBody Map<String, String> params) {
        String id = params.get("id");
        String success = params.get("success");
        specialApplyService.confirmSpecialApply(id, success);
        return success();
    }


}
