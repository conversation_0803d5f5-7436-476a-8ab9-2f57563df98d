<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.screen.ZdJydtScreenDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getAllOrgInfo" parameterType="list" resultType="com.alibaba.fastjson.JSONObject">
        select * from acp_pm_org where is_del = 0 and id in (<foreach collection="list" item="item" separator=",">#{item}</foreach> )
    </select>

    <select id="getJyzs" parameterType="list" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        org_code,
        coalesce(sum(case when ryzt = '10' then 1 else 0 end),0) as jyzs, -- 羁押总数
        coalesce(sum(case when ryzt = '10' and rssj >= CURRENT_DATE then 1 else 0 end),0) as jrrs, --今日入所
        coalesce(sum(case when ryzt = '11' and cssj >= CURRENT_DATE then 1 else 0 end),0) as jrcs --今日出所
        FROM vw_acp_pm_prisoner_list
        where is_del = 0
        and org_code  in (<foreach collection="list" item="item" separator=",">#{item}</foreach> ) GROUP BY org_code
    </select>

    <select id="syAndCsObj" parameterType="list" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('day', NOW()) and rssj &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as today_sy,
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('day', NOW()) - interval ' 1 day' and rssj  &lt; DATE_TRUNC('day', NOW())then 1 else 0 end),0) as yesterday_sy,
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('year', NOW()) and rssj &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as now_year_sy,

        coalesce(sum(case when cssj &gt;= DATE_TRUNC('day', NOW()) and cssj &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as today_cs,
        coalesce(sum(case when cssj &gt;= DATE_TRUNC('day', NOW()) - interval ' 1 day' and cssj &lt; DATE_TRUNC('day', NOW())then 1 else 0 end),0) as yesterday_cs,
        coalesce(sum(case when cssj &gt;= DATE_TRUNC('year', NOW()) and cssj &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as now_year_cs
        FROM vw_acp_pm_prisoner_list
        where is_del = 0
        and org_code in (<foreach collection="list" item="item" separator=",">#{item}</foreach> )
    </select>

    <select id="syAndCsList" resultType="com.alibaba.fastjson.JSONObject">
            -- 月份查询
            WITH  ym as (
            select to_char(d, 'YYYY-MM') as year_month from generate_series(
                            date_trunc('month', CURRENT_DATE) - INTERVAL' 11 month',
                            date_trunc('month', CURRENT_DATE),
                            INTERVAL' 1 month') d
            ),
            -- 收押查询
            rs as (
            select a.rs_ym, count(1) as rs_total from (
            select to_char(rssj, 'YYYY-MM') as rs_ym   FROM vw_acp_pm_prisoner_in
            where is_del = 0  and rssj >= DATE_TRUNC('month', NOW()) - INTERVAL ' 11 month'
            and org_code in (<foreach collection="list" item="item" separator=",">#{item}</foreach> )
            ) a GROUP BY a.rs_ym
            ),
            -- 出所查询
            cs as (
            select b.cs_ym, count(1) as cs_total from (
            select to_char(cssj, 'YYYY-MM') as cs_ym   FROM vw_acp_pm_prisoner_out
            where is_del = 0 and cssj >= DATE_TRUNC('month', NOW()) - INTERVAL ' 11 month'
            and org_code in (<foreach collection="list" item="item" separator=",">#{item}</foreach> )
            ) b GROUP BY b.cs_ym
            )
        select ym.year_month, coalesce(rs.rs_total,0) as rs_total, coalesce(cs.cs_total, 0) as cs_total
        from ym left join rs on ym.year_month=rs.rs_ym  left join cs on ym.year_month=cs.cs_ym
    </select>


</mapper>
