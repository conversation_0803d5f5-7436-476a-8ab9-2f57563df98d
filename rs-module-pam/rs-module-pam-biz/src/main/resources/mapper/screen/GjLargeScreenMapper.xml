<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.screen.GjLargeScreenDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!--
     bycsrsqfbyrsrs：本月出所人数且非本月入所人数
     dqzyrs：当前在押人数
     byrsrs：本月入所人数
     cqjyrs：超期羁押人数
     -->
    <select id="getJydtJyxx" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        coalesce(sum(CASE WHEN ryzt='10' THEN 1 ELSE 0 END ),0) as dqzyrs,
        coalesce(sum(CASE WHEN ryzt='10' and rssj &gt;= date_trunc('month', now()) THEN 1 ELSE 0 END ),0)  as byrsrs,
        coalesce(sum(CASE WHEN ryzt='11' and cssj &gt;= date_trunc('month', now()) and rssj &lt; date_trunc('month', now()) THEN 1 ELSE 0 END ),0)  as bycsrsqfbyrsrs,
        coalesce(sum(CASE WHEN ryzt='10' and now() > gyqx THEN 1 ELSE 0 END ),0) as cqjyrs
        FROM "vw_acp_pm_prisoner_list"  where is_del = 0
        <choose>
            <when test="codeType=='01'">
                and jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and area_id = #{code}
            </when>
            <otherwise>
                and org_code = #{code}
            </otherwise>
        </choose>
    </select>
    <!--
    acp_gj_risk_assmt
    zs: 风险总数 rl1: 一级风险数  rl2: 二级风险数  rl3: 三级风险数
    -->
    <select id="getFxryfb" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            count(1) as zs,
            coalesce(sum(CASE WHEN a.fxdj='1' THEN 1 ELSE 0 END ),0) as rl1,
            coalesce(sum(CASE WHEN a.fxdj='2' THEN 1 ELSE 0 END ),0) as rl2,
            coalesce(sum(CASE WHEN a.fxdj='3' THEN 1 ELSE 0 END ),0) as rl3
        FROM vw_acp_pm_prisoner_in a  where a.is_del=0 and a.fxdj in ('1','2','3')
        <choose>
            <when test="codeType=='01'">
                and a.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and a.area_id = #{code}
            </when>
            <otherwise>
                and a.org_code = #{code}
            </otherwise>
        </choose>

    </select>
    <!--
    jrtzrj：今日调整入监数
    zrtzrj：昨日调整入监数
    -->
    <select id="getTzrjxx" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        coalesce(sum(CASE WHEN a.return_time &gt;= CURRENT_DATE THEN 1 ELSE 0 END ),0) as jrtzrj,
        coalesce(sum(CASE WHEN a.return_time &lt; CURRENT_DATE THEN 1 ELSE 0 END ),0) as zrtzrj
        FROM "acp_gj_prison_room_change"  a left join acp_pm_area_prison_room b on a.new_room_id = b.id
          where a.is_del=0 and a.is_change=1
        <choose>
            <when test="codeType=='01'">
                and a.new_room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and a.org_code = #{code}
            </otherwise>
        </choose>
        and a.return_time >= CURRENT_DATE - INTERVAL '1 day'
    </select>

    <!--
    jrtzcj：今日调整出监数
    zrtzcj：昨日调整出监数
    -->
    <select id="getTzcjxx" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        coalesce(sum(CASE WHEN a.escorting_time &gt;= CURRENT_DATE THEN 1 ELSE 0 END ),0) as jrtzcj,
        coalesce(sum(CASE WHEN a.escorting_time &lt; CURRENT_DATE THEN 1 ELSE 0 END ),0) as zrtzcj
        FROM "acp_gj_prison_room_change" a left join acp_pm_area_prison_room b on a.old_room_id = b.id
        where a.is_del=0 and a.is_change=1
        <choose>
            <when test="codeType=='01'">
                and a.old_room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and a.org_code = #{code}
            </otherwise>
        </choose>
        and a.escorting_time >= CURRENT_DATE - INTERVAL '1 day'
    </select>

    <select id="tjcswgCount" parameterType="string" resultType="int">
        select count(1) as tjcswg from acp_wb_escort a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
        where a.is_del=0  and a.return_time is null
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="csjywgCount" parameterType="string" resultType="int">
        select count(1) as csjywg from acp_pm_out_prison_treatment a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
        where a.is_del=0  and a.out_time is not null and a.back_time is null
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getWcdtTx" parameterType="string" resultType="int">
        select count(1) from acp_wb_arraignment a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
		where a.is_del=0
        and TO_TIMESTAMP(concat(to_char(a.start_apply_arraignment_time,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') &lt;= now()
        and TO_TIMESTAMP(concat(to_char(a.end_apply_arraignment_time,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') &gt;=now()
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getWcdtTj" parameterType="string" resultType="int">
        select count(1) as tjcswg from acp_wb_escort a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
        where a.is_del = 0
        and a.apply_escort_date=CURRENT_DATE
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getWcdtLshj" parameterType="string" resultType="int">
        select count(1) FROM acp_wb_lawyer_meeting a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
		where a.is_del=0
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_start_time,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') &lt;= now()
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_end_time,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') &gt;=now()
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getWcdtJshj" parameterType="string" resultType="int">
        select count(1) FROM acp_wb_family_meeting a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
		where a.is_del=0
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_start_time,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') &lt;= now()
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_end_time,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') &gt;=now()
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getWcdtJshjVideo" parameterType="string" resultType="int">
        select count(1) FROM acp_wb_family_meeting_video a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
		where a.is_del=0 and a.status  in ('0','1','2')
        and a.notification_meeting_date BETWEEN CURRENT_DATE and CURRENT_DATE  + INTERVAL '1 day'
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getWcdtSglshj" parameterType="string" resultType="int">
        select count(1) FROM acp_wb_consular_meeting a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
		where a.is_del=0
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_start_time,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') &lt;= now()
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_end_time,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') &gt;=now()
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getWcdtCsjy" parameterType="string" resultType="int">
        select count(1) from acp_pm_out_prison_treatment a
        left join  vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
        where a.is_del=0 and a.out_time BETWEEN CURRENT_DATE and CURRENT_DATE  + INTERVAL '1 day'
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getZdgzryDdgy" parameterType="string"
            resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        select b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from acp_gj_alone_imprison a inner join vw_acp_pm_prisoner_in b on a.jgrybm = b.jgrybm
        where a.is_del = 0 and a.status ='04' and a.out_time > now()
        <choose>
            <when test="codeType=='01'">
                and a.old_room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getTagListByJgrybmList" parameterType="list" resultType="com.rs.module.base.entity.pm.PrisonerTagDO">
            select * from acp_pm_prisoner_tag where is_del=0 and jgrybm in (
            <foreach collection="jgrybmList" separator="," item="item">
                #{item}
            </foreach>
            )
    </select>

    <select id="getZdgzryJjsy" parameterType="string" resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        select b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from acp_gj_equipment_use a left join vw_acp_pm_prisoner_in b on a.jgrybm = b.jgrybm
        where a.is_del = 0 and a.status = '06'
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getZdgzryLsgd"  parameterType="string" resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        select b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from acp_gj_equipment_use a left join vw_acp_pm_prisoner_in b on a.jgrybm = b.jgrybm
        where a.is_del = 0 and a.status = '06' and a.is_temp_fixation=1
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getZdgzryZdry" parameterType="string" resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        select b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
            from pam_attention_prisoner a inner join vw_acp_pm_prisoner_in b on a.jgrybm = b.jgrybm
            where a.is_del = 0 and a.reg_status = '3'
            <choose>
                <when test="codeType=='01'">
                    and b.jsh = #{code}
                </when>
                <when test="codeType=='02'">
                    and b.area_id = #{code}
                </when>
                <otherwise>
                    and b.org_code = #{code}
                </otherwise>
            </choose>
    </select>

    <select id="getZdgzryWjry" parameterType="string" resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        select  b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from vw_acp_pm_prisoner_in b
        where b.is_del=0 and b.gj not in ('156','158','344','446')
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getZdgzryJrdq" parameterType="string" resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        select  b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from vw_acp_pm_prisoner_in b
        where b.is_del=0 and b.gyqx BETWEEN CURRENT_DATE and CURRENT_DATE  + INTERVAL '1 day'
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getZdgzryZbh"  parameterType="string" resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        SELECT b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
         FROM "ihc_pm_severely_sick_manage" a inner join vw_acp_pm_prisoner_in b on a.jgrybm = b.jgrybm
        where a.is_del = 0 and a.business_status = '1'
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getZdgzryFxry" parameterType="string" resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        SELECT b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        FROM  vw_acp_pm_prisoner_in b
        where b.is_del = 0 and b.ryzt = '10' and b.fxdj is not null
        <choose>
            <when test="codeType=='01'">
                and b.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and b.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getZdgzryAll" parameterType="list" resultType="com.rs.module.pam.entity.screen.FocusOnPersonnelDO">
        select DISTINCT b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from (
            <foreach collection="list" separator="union all" item="item">
                 ${item}
            </foreach>
		) b
    </select>

    <!--查询会见公共方法-->
    <sql id="commonHj">
        -- 提讯
        select a.add_time, a.org_code,  a.jgrybm, a.room_id as hjs, to_char(a.start_apply_arraignment_time,'HH24:MI') as st, to_char(a.end_apply_arraignment_time,'HH24:MI') as et, 'tx' as business_type,   (case when a.escorting_time is null then 'dtc' when a.escorting_time is not null and a.return_time is null then 'dth' else 'ytc' end ) as status
        from acp_wb_arraignment a
        where a.is_del=0
        and a.org_code = #{orgCode}
        and TO_TIMESTAMP(concat(to_char(a.start_apply_arraignment_time,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') &lt;= now()
        and TO_TIMESTAMP(concat(to_char(a.end_apply_arraignment_time,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') &gt;=now()
        union all
        -- 提解
        select a.add_time, a.org_code,  a.jgrybm, a.room_id as hjs, to_char(a.add_time,'HH24:MI') as st, to_char(a.arraignment_end_time,'HH24:MI') as et, 'tj' as business_type,   (case when a.escorting_time is null then 'dtc' when a.escorting_time is not null and a.return_time is null then 'dth' else 'ytc' end ) as status
        from acp_wb_escort a where a.is_del = 0
        and a.org_code = #{orgCode}
        and apply_escort_date = CURRENT_DATE
        union all
        -- 律师会见
        select a.add_time, a.org_code,  a.jgrybm, a.room_id as hjs, to_char(a.apply_meeting_start_time,'HH24:MI') as st, to_char(a.apply_meeting_end_time,'HH24:MI') as et, 'lshj' as business_type,   (case when a.escorting_time is null then 'dtc' when a.escorting_time is not null and a.return_time is null then 'dth' else 'ytc' end ) as status
        from acp_wb_lawyer_meeting a  where a.is_del = 0
        and a.org_code = #{orgCode}
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_start_time,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') &lt;= now()
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_end_time,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') &gt;=now()
        union all
        -- 家属会见
        select a.add_time, a.org_code,  a.jgrybm, a.room_id as hjs, to_char(a.apply_meeting_start_time,'HH24:MI') as st, to_char(a.apply_meeting_end_time,'HH24:MI') as et, 'jshj' as business_type,   (case when a.escorting_time is null then 'dtc' when a.escorting_time is not null and a.return_time is null then 'dth' else 'ytc' end ) as status
        from acp_wb_family_meeting a where a.is_del=0
        and a.org_code = #{orgCode}
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_start_time,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') &lt;= now()
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_end_time,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') &gt;=now()
        union all
        -- 家属单向视频会见
        select a.add_time, a.org_code,  a.jgrybm, (SELECT max(c.room_id) FROM "acp_gj_in_out_records" c where c.is_del=0 and  c.business_type='03' and c.inout_type='01' and c.business_id = a.id) as hjs, to_char(a.add_time,'HH24:MI') as st, to_char(a.meeting_end_time,'HH24:MI') as et, 'jsdxsphj' as business_type,   (case when (SELECT max(c.inout_time) FROM "acp_gj_in_out_records" c where c.is_del=0 and  c.business_type='03' and c.inout_type='01' and c.business_id = a.id) is null then 'dtc' when (SELECT max(c.inout_time) FROM "acp_gj_in_out_records" c where c.is_del=0 and  c.business_type='03' and c.inout_type='01' and c.business_id = a.id) is not null and (SELECT max(c.inout_time) FROM "acp_gj_in_out_records" c where c.is_del=0 and  c.business_type='03' and c.inout_type='02' and c.business_id = a.id) is null then 'dth' else 'ytc' end ) as status
        from acp_wb_family_meeting_video a where a.is_del=0 and a.status  in ('0','1','2')
        and a.org_code = #{orgCode}
        and a.notification_meeting_date BETWEEN CURRENT_DATE and CURRENT_DATE  + INTERVAL '1 day'
        union all
        -- 使馆领事会见
        select a.add_time, a.org_code, a.jgrybm, a.room_id as hjs, to_char(a.apply_meeting_start_time,'HH24:MI') as st, to_char(a.apply_meeting_end_time,'HH24:MI') as et, 'sglshj' as business_type,   (case when a.escorting_time is null then 'dtc' when a.escorting_time is not null and a.return_time is null then 'dth' else 'ytc' end ) as status
        from acp_wb_consular_meeting a where a.is_del=0
        and a.org_code = #{orgCode}
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_start_time,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') &lt;= now()
        and TO_TIMESTAMP(concat(to_char(a.apply_meeting_end_time,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') &gt;=now()
    </sql>

    <select id="getDtcDthCountByOrgCode" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select
        coalesce(sum((case when b.status='dtc' then 1 else 0 end)),0) dtc,
        coalesce(sum((case when b.status='dth' then 1 else 0 end)),0) dth
        from ( <include refid="commonHj"/> ) b
    </select>

    <select id="getTodayMeetingPage" parameterType="string" resultType="com.rs.module.pam.entity.screen.CommonMeetingDO">
        select b.*, d.xm, d.front_photo, d.jsh, d.room_name, d.sshj, d.sxzm, d.gyqx
        from ( <include refid="commonHj"/> ) b inner join vw_acp_pm_prisoner_in d on b.jgrybm = d.jgrybm
        where 1=1
        order by b.add_time desc
    </select>

    <select id="getJsswItems" resultType="com.alibaba.fastjson.JSONObject">
        select c.bus_type as bustype, max(c.bus_name) as busname, count(*) as buscount from (
            SELECT a.org_code, a.bus_type, a.bus_name, COALESCE(b.jsh,a.room_id) as room_id
            FROM "acp_sys_bus_trace" a left join vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
            where  bus_type in (<foreach collection="list" item="item" separator=",">#{item}</foreach> )
            and a.add_time >=CURRENT_DATE
            ) c inner join acp_pm_area_prison_room d on c.room_id = d.id
        where 1=1
        <choose>
            <when test="codeType=='01'">
                and c.room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and d.area_id = #{code}
            </when>
            <otherwise>
                and c.org_code = #{code}
            </otherwise>
        </choose>
        GROUP BY c.bus_type
    </select>

    <select id="getJsswItemArray"  resultType="com.alibaba.fastjson.JSONObject">
        select c.*, d.area_id from (
        SELECT a.id, a.org_code, a.add_time, to_char(a.add_time,'HH24:MI') as hm, a.bus_type, a.bus_name, a."content", COALESCE(b.jsh,a.room_id) as room_id
        FROM "acp_sys_bus_trace" a left join vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
        where  bus_type in (<foreach collection="list" item="item" separator=",">#{item}</foreach> )
        and a.add_time >=CURRENT_DATE
        ) c inner join acp_pm_area_prison_room d on c.room_id = d.id
        where 1=1
        <choose>
            <when test="codeType=='01'">
                and c.room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and d.area_id = #{code}
            </when>
            <otherwise>
                and c.org_code = #{code}
            </otherwise>
        </choose>
        order by c.add_time desc
    </select>


    <select id="getWggjPage" parameterType="string" resultType="com.rs.module.pam.entity.screen.WgdjDO">
        SELECT a.id, a.room_name as address_name, a.add_time,
        concat(a.room_name,a.jgryxm,'，',a.violation_content) as violation_content,
        (case when a.status='03' then '2' else '3'end) as handle_status,
         '2' as wglb
         FROM "acp_pi_violation_record" a
        left join acp_pm_area_prison_room b on a.room_id = b.id
        where a.is_del = '0'
        <choose>
            <when test='timeRange=="1"'>
                and a.add_time &gt;= date_trunc('day', now())
            </when>
            <when test='timeRange=="2"'>
                and a.add_time &gt;= date_trunc('week', now())
            </when>
            <when test='timeRange=="3"'>
                and a.add_time &gt;= date_trunc('month', now())
            </when>
            <otherwise>
                and a.add_time &gt;= date_trunc('year', now())
            </otherwise>
        </choose>
        <choose>
            <when test='handleStatus=="1"'>
                and  a.status in ('03','04')
            </when>
            <when test='handleStatus=="2"'>
                and a.status in ('03')
            </when>
            <otherwise>
                and a.status in ('04')
            </otherwise>
        </choose>

        <choose>
            <when test="codeType=='01'">
                and a.room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and a.org_code = #{code}
            </otherwise>
        </choose>
        order by a.add_time desc
    </select>

    <select id="getWggjAllCount" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT count(1) as allCount,
        coalesce(sum(case when a.status = '03' then 1 else 0 end),0) as noHandleCount,
        coalesce(sum(case when a.status = '04' then 1 else 0 end),0) as hasHandleCount
        FROM "acp_pi_violation_record" a left join acp_pm_area_prison_room b on a.room_id = b.id
        where a.is_del = '0' and a.status in ('03','04')
        <choose>
            <when test='timeRange=="1"'>
                and a.add_time &gt;= date_trunc('day', now())
            </when>
            <when test='timeRange=="2"'>
                and a.add_time &gt;= date_trunc('week', now())
            </when>
            <when test='timeRange=="3"'>
                and a.add_time &gt;= date_trunc('month', now())
            </when>
            <otherwise>
                and a.add_time &gt;= date_trunc('year', now())
            </otherwise>
        </choose>
        <choose>
            <when test="codeType=='01'">
                and a.room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and a.org_code = #{code}
            </otherwise>
        </choose>
    </select>

</mapper>
