<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.complaint.SuggestionDao">

    <select id="pageRecord" resultType="com.rs.module.pam.entity.complaint.SuggestionDO">
        select a.*, b.xb, COALESCE(EXTRACT(YEAR FROM AGE(NOW(), b.csrq)),0) as age
        from pam_complaint_suggestion a
        left join vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
        where a.is_del = 0
         <if test="jgrybm != null and jgrybm != ''">
             and a.jgrybm = #{jgrybm}
         </if>
         <if test="roomId != null and roomId != ''">
             and a.room_id = #{roomId}
         </if>
         <if test="startTime != null">
             and a.add_time &gt;= #{startTime}
         </if>
         <if test="endTime != null">
             and a.add_time &lt; #{endTime}
         </if>
    </select>

</mapper>
