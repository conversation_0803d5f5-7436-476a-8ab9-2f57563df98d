package com.rs.module.acp.enums.db;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/8/14 15:56
 */
@Data
public class VerificationMode {
    /**
     * 验证模式（指掌纹信息: 01, 虹膜信息: 02, 人像信息: 03, DNA信息: 04）
     */
    @ApiModelProperty("检查模式（指掌纹信息: 01, 虹膜信息: 02, 人像信息: 03, DNA信息: 04）")
    private String code;
    /**
     * 验证结果（0：失败，1：成功）
     */
    @ApiModelProperty("验证结果（0：失败，1：成功）")
    private String result;
}
