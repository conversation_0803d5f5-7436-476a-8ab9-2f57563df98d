<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.LawyerDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getLawyerPageByJgrybm" resultType="com.rs.module.acp.controller.admin.wb.vo.LawyerSelectVO">
        SELECT
            t2.id,
            t1.id as lawyer_prisoner_id,
            t2.xm,
            t2.xb,
            t2.zjhm,
            t2.lxfs,
            t2.lslx,
            t2.zyzhm,
            t1.entrust_stage,
            t2.lsdw,
            t1.status,
            t1.entrust_type,
            t1.principal,
            t1.principal_id,
            t1.power_of_attorney_type,
            t1.power_of_attorney_url,
            t1.letter_number,
            t2.zp_url,
            t2.zyzs_url,
            concat_ws('~',to_char(t1.add_time,'YYYY-MM-DD HH24:MI:SS'),'案件结束') entrust_time
        FROM
            acp_wb_lawyer_prisoner t1
                LEFT JOIN acp_wb_lawyer t2 ON t1.lawyer_id = t2.ID
        WHERE
          t1.org_code = #{pageReqVO.orgCode}
          and t1.jgrybm = #{pageReqVO.jgrybm}
          <if test="pageReqVO.xm != null and pageReqVO.xm != ''">
              and t2.xm like concat('%',#{pageReqVO.xm},'%')
          </if>
            <if test="pageReqVO.zjhm != null and pageReqVO.zjhm != ''">
                and t2.xm like concat('%',#{pageReqVO.zjhm},'%')
            </if>
        <if test="pageReqVO.lsdw != null and pageReqVO.lsdw != ''">
            and t2.lsdw like concat('%',#{pageReqVO.lsdw},'%')
        </if>
          AND t1.is_del = 0 AND t2.is_del = 0
          AND t1.status = '1'
    </select>
    
</mapper>
