<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.zh.ReportJschmrqkDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
     
    <!-- 时间段sql脚本 -->
    <sql id="rangeSql">
		AND ${fieldName} &gt;= ((TO_TIMESTAMP(#{reportDate}, 'yyyy-MM-dd hh24:mi:ss') - INTERVAL '1 day') + INTERVAL '8 hour')
		AND ${fieldName} &lt;= (TO_TIMESTAMP(#{reportDate}, 'yyyy-MM-dd hh24:mi:ss') + INTERVAL '8 hour')
	</sql>
    
    <!-- 前24小时-查询脚本 --> 
	<select id="getDataOf24Hours" resultType="com.alibaba.fastjson.JSONObject">
		SELECT
			COALESCE ( SUM ( CASE WHEN ryzt = '10' THEN 1 ELSE 0 END ), 0 ) AS zsryzs,
			COALESCE ( SUM ( CASE WHEN ryzt = '11' THEN 1 ELSE 0 END ), 0 ) AS csryzs,
			0 as hsryzs
			FROM
				vw_acp_pm_prisoner_list T 
			WHERE
				T.org_code = #{orgCode}
				AND T.is_del = 0
				<include refid="rangeSql">
					<property name="fieldName" value="T.rssj" />
				</include>
	</select>
	
	<!-- 截至目前-查询脚本 -->
	<select id="getDataOfUpToNow" resultType="com.alibaba.fastjson.JSONObject">
		SELECT
			COALESCE ( SUM ( CASE WHEN ryzt = '10' THEN 1 ELSE 0 END ), 0 ) AS zsryzsAll,
			COALESCE (SUM ( CASE WHEN  (SELECT count(1) from acp_db_out_hospital a where a.jgrybm = t.jgrybm and a.is_del=0 and a.current_step!='09') > 0 THEN 1 else 0 end),0) as csjyryzsAll,
			COALESCE (SUM ( CASE WHEN EXTRACT(YEAR FROM AGE(NOW(), t.csrq)) > 65 THEN 1 ELSE 0 END ),0 ) AS lswysryAll,
			COALESCE (SUM ( CASE WHEN  (SELECT count(1) from ihc_bd_psychiatric_mgr a where a.jgrybm = t.jgrybm and a.is_del=0 and a.status = '1') > 0 THEN 1 else 0 end),0) as jsycryAll,
			COALESCE (SUM ( CASE WHEN position(t.tssf in '25,17,40,3,4,28') >0 THEN 1 ELSE 0 END ),0 ) AS tssfryzsAll,
			COALESCE (SUM ( CASE WHEN t.gj not in ('156','158','344','446') THEN 1 ELSE 0 END ),0 ) AS wjryzsAll
		FROM
			vw_acp_pm_prisoner_in_list t
		WHERE
			 t.org_code = #{orgCode} and t.is_del=0 and t.ryzt = '10'
	</select>

	<!-- 前24小时-提讯总数 -->
	<select id="getDataOf24Tx" resultType="com.alibaba.fastjson.JSONObject">
		SELECT count(1) AS txzs
		FROM
			acp_wb_arraignment t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode} 
			AND t1.is_del = 0 
			AND T.ryzt = '10'
			<include refid="rangeSql">
				<property name="fieldName" value="t1.start_apply_arraignment_time" />
			</include>
	</select>
	
	<!-- 前24小时-提解总数 -->
	<select id="getDataOf24Tj" resultType="com.alibaba.fastjson.JSONObject">
		SELECT 
			COUNT(1) AS tjzs 
		FROM
			acp_wb_escort t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode}
			AND t1.is_del = 0 
			AND T.ryzt = '10'
			<include refid="rangeSql">
				<property name="fieldName" value="t1.apply_escort_date" />
			</include>
	</select>
	
	<!-- 前24小时-律师会见总数 -->
	<select id="getDataOf24Lshj" resultType="com.alibaba.fastjson.JSONObject">
		SELECT 
			COUNT (1) AS lshjzs
		FROM
			acp_wb_lawyer_meeting t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode}
			AND t1.is_del = 0 
			AND T.ryzt = '10'
			<include refid="rangeSql">
				<property name="fieldName" value="t1.apply_meeting_start_time" />
			</include>
	</select>
	
	<!-- 收押情况-入所人员 -->
	<select id="getSyqk24Rsry" resultType="java.util.Map">
		SELECT
			jgrybm,
			xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			xb,
			hjd,
			rsyy,
			sxzm,
			badw
		FROM
			vw_acp_pm_prisoner_in_list T 
		WHERE
			T.org_code = #{orgCode}
			AND T.is_del = 0 
			AND T.ryzt = '10'
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 收押情况-出所人员 -->
	<select id="getSyqk24Csry" resultType="java.util.Map">
		SELECT
			jgrybm,
			xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			xb,
			hjd,
			rsyy,
			sxzm,
			badw
		FROM
			vw_acp_pm_prisoner_out_list T 
		WHERE
			T.org_code = #{orgCode} 
			AND T.is_del = 0 
			AND T.ryzt = '11' 
			<include refid="rangeSql">
				<property name="fieldName" value="T.cssj" />
			</include>
	</select>
	
	<!-- 在押情况-65岁以上人员 -->
	<select id="getZyqk24Above65Years" resultType="java.util.Map">
		SELECT
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh,
			T2.room_name 
		FROM
			vw_acp_pm_prisoner_in_list T , acp_pm_area_prison_room t2
		WHERE
			T.jsh = t2.room_code
			AND T.org_code = #{orgCode}
			AND T.is_del = 0 
			AND T.ryzt = '10' 
			AND EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) > 65
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 在押情况-加戴械具人员 -->
	<select id="getZyqk24Jdxj" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			acp_gj_equipment_use t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			T1.org_code = #{orgCode} 
			AND T1.is_del = 0
			AND T.ryzt = '10'
			<include refid="rangeSql">
				<property name="fieldName" value="t1.actual_end_time" />
			</include>
	</select>
	
	<!-- 在押情况-单独关押 -->
	<select id="getZyqk24Ddgy" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			acp_gj_alone_imprison t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			T1.org_code = #{orgCode} 
			AND T1.is_del = 0 
			AND T.ryzt = '10'
			<include refid="rangeSql">
				<property name="fieldName" value="t1.out_time" />
			</include>
	</select>
	
	<!-- 在押情况-关注群体人员 -->
	<select id="getZyqk24Gzqtry" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			pam_attention_prisoner t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			T1.org_code = #{orgCode} 
			AND T1.is_del = 0 
			AND T.ryzt = '10' 
			AND t1.reg_status = '3'
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 在押情况-精神异常人员 -->
	<select id="getZyqk24Jsyc" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			ihc_bd_psychiatric_mgr t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			T1.org_code = #{orgCode} 
			AND T1.is_del = 0 
			AND T.ryzt = '10' 
			AND t1.status = '1'
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 在押情况-吞食异物人员 -->
	<select id="getZyqk24Tsyw" resultType="java.util.Map">
		SELECT
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			acp_db_out_hospital t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			T1.org_code = #{orgCode} 
			AND T1.is_del = 0 
			and t1.current_step!='09'
			and t1.sfcztsywqk = 1
			AND T.ryzt = '10'
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 在押情况-特殊身份 -->
	<select id="getZyqk24Tssf" resultType="java.util.Map">
		SELECT
			jgrybm,
			xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			xb,
			jsh 
		FROM
			vw_acp_pm_prisoner_in_list T 
		WHERE
			T.org_code = #{orgCode}
			AND T.is_del = 0 
			AND T.ryzt = '10' 
			AND position(t.tssf in '25,17,40,3,4,28') >0
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 外籍人员管控情况-国籍分布情况 -->
	<select id="getWjrygkGjfb" resultType="java.util.Map">
		SELECT
			gj,
			COUNT ( 1 )  as zs
		FROM
			vw_acp_pm_prisoner_in_list T 
		WHERE
			T.org_code = #{orgCode}
			AND T.is_del = 0 
			AND T.ryzt = '10' 
			AND T.gj NOT IN ( '156', '158', '344', '446' )
		GROUP BY gj
		order by count(1) desc
	</select>
	
	<!-- 外籍人员管控情况-涉嫌犯罪类型 -->
	<select id="getWjrygkSxfzlx" resultType="java.util.Map">
		SELECT
			sxzm,
			COUNT ( 1 )  as zs
		FROM
			vw_acp_pm_prisoner_in_list T 
		WHERE
			T.org_code = #{orgCode}
			AND T.is_del = 0 
			AND T.ryzt = '10' 
			AND T.gj NOT IN ( '156', '158', '344', '446' )
		GROUP BY sxzm
		order by count(1) desc
	</select>
	
	<!-- 服务办案工作情况-提讯 -->
	<select id="getFwbagzqk24Tx" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			acp_wb_arraignment t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode}
			AND t1.is_del = 0 
			AND T.ryzt = '10' 
			<include refid="rangeSql">
				<property name="fieldName" value="t1.start_apply_arraignment_time" />
			</include>
	</select>
	
	<!-- 服务办案工作情况-提解 -->
	<select id="getFwbagzqk24Tj" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			acp_wb_escort t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode}
			AND t1.is_del = 0 
			AND T.ryzt = '10' 
			<include refid="rangeSql">
				<property name="fieldName" value="t1.apply_escort_date" />
			</include>
	</select>
	
	<!-- 服务办案工作情况-律师接待 -->
	<select id="getFwbagzqk24Lsjd" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			acp_wb_lawyer_meeting t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode}
			AND t1.is_del = 0 
			AND T.ryzt = '10' 
			<include refid="rangeSql">
				<property name="fieldName" value="t1.apply_meeting_start_time" />
			</include>
	</select>
	
	<!-- 服务办案工作情况-接待家属 -->
	<select id="getFwbagzqk24Jdjs" resultType="java.util.Map">
		SELECT 1
	</select>
	
	<!-- 服务办案工作情况-交付执行 -->
	<select id="getFwbagzqk24Jfzx" resultType="java.util.Map">
		SELECT 1
	</select>
	
	<!-- 服务办案工作情况-使馆会见 -->
	<select id="getFwbagzqk24Sghj" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			T.xb,
			T.jsh 
		FROM
			acp_wb_consular_meeting t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode}
			AND t1.is_del = 0 
			AND T.ryzt = '10'
			<include refid="rangeSql">
				<property name="fieldName" value="t1.apply_meeting_start_time" />
			</include>
	</select>
	
	<!-- 重点风险人员管控情况-三级风险 -->
	<select id="getZdfxry24Sjfx" resultType="java.util.Map">
		SELECT
			COALESCE ( SUM ( CASE WHEN fxdj = '1' THEN 1 ELSE 0 END ), 0 ) AS yjfx,			
			COALESCE ( SUM ( CASE WHEN fxdj = '2' THEN 1 ELSE 0 END ), 0 ) AS ejfx,			
			COALESCE ( SUM ( CASE WHEN fxdj = '3' THEN 1 ELSE 0 END ), 0 ) AS sjfx
		FROM
			vw_acp_pm_prisoner_in_list T 
		WHERE
			T.org_code = #{orgCode}
			AND T.is_del = 0 
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 重点风险人员管控情况-医疗重点人员及住院人员具体情况 -->
	<select id="getZdfxry24Zyqk" resultType="java.util.Map">
		SELECT
			T.jgrybm,
			T.xm,
			t1.symptom_desc 
		FROM
			acp_db_out_hospital t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode}
			AND t1.is_del = 0 
			AND T.ryzt = '10' 
			and t.fxdj in ('1','2','3')
			and t1.current_step!='09'
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 其他基础数据-巡诊发药 -->
	<select id="getJcsj24Xzfy" resultType="java.lang.Integer">
		SELECT count(1) as cs from ihc_ipm_prescribe_execute t
		where 
			T.org_code = #{orgCode} 
			AND T.is_del = 0
			<include refid="rangeSql">
				<property name="fieldName" value="T.add_time" />
			</include>
	</select>
	
	<!-- 其他基础数据-所内治疗 -->
	<select id="getJcsj24Snzl" resultType="java.lang.Integer">
		SELECT count(1) as cs from ihc_ipm_outpatient  t 
		where 
			T.org_code = #{orgCode} 
			AND T.is_del = 0 
			<include refid="rangeSql">
				<property name="fieldName" value="T.add_time" />
			</include>
	</select>
	
	<!-- 其他基础数据-以绝食、拒绝医疗等方式对抗监管情况 -->
	<select id="getJcsj24Dkjg" resultType="java.lang.Integer">
		SELECT count(1) as cs from ihc_ipm_prescribe_execute t 
		where 
			T.org_code = #{orgCode} 
			AND T.is_del = 0 
			and t.dose_status = '2'
			<include refid="rangeSql">
				<property name="fieldName" value="T.add_time" />
			</include>
	</select>
	
	<!-- 其他基础数据-一级 -->
	<select id="getJcsj24Yj" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			t.sxzm,
			t.rssj,
			t.sshj,
			t1.assmt_reason
		FROM
			acp_gj_risk_assmt t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode} 
			AND t1.is_del = 0 
			and t1.status = '2'
			AND T.ryzt = '10' 
			and t.fxdj = '1'
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 其他基础数据-二级 -->
	<select id="getJcsj24Ej" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			t.sxzm,
			t.rssj,
			t.sshj,
			t1.assmt_reason
		FROM
			acp_gj_risk_assmt t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode}
			AND t1.is_del = 0 
			and t1.status = '2'
			AND T.ryzt = '10' 
			and t.fxdj = '2'
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
	
	<!-- 其他基础数据-三级 -->
	<select id="getJcsj24Sj" resultType="java.util.Map">
		SELECT 
			T.jgrybm,
			T.xm,
			EXTRACT ( YEAR FROM AGE( NOW( ), T.csrq ) ) AS nl,
			t.sxzm,
			t.rssj,
			t.sshj,
			t1.assmt_reason
		FROM
			acp_gj_risk_assmt t1
			INNER JOIN vw_acp_pm_prisoner_in_list T ON t1.jgrybm = T.jgrybm 
		WHERE
			t1.org_code = #{orgCode} 
			AND t1.is_del = 0 
			and t1.status = '2'
			AND T.ryzt = '10' 
			and t.fxdj = '3'
			<include refid="rangeSql">
				<property name="fieldName" value="T.rssj" />
			</include>
	</select>
</mapper>
