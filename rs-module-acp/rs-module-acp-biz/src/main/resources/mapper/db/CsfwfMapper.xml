<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.db.CsfwfDao">


    <select id="getCsfwfStatistics" resultType="com.rs.module.acp.controller.app.pm.vo.AppCsFwfStatisticsVO">
        SELECT
        COUNT(1) AS total,
        business_reason AS code
        FROM
        "acp_db_csfwf"
        WHERE
        is_del = '0'
        AND check_result = '01'
        AND business_type = #{businessType,jdbcType=VARCHAR}
        AND org_code = #{orgCode,jdbcType=VARCHAR}
        AND add_time >= CURRENT_DATE AND add_time &lt; CURRENT_DATE + 1
        GROUP BY
        business_reason
    </select>
</mapper>
