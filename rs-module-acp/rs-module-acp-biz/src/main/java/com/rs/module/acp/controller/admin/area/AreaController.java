package com.rs.module.acp.controller.admin.area;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.listener.admin.area.AreaDataImportListener;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.AreaData;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.service.pm.AreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-区域")
@RestController
@RequestMapping("/base/area")
@Validated
@Slf4j
public class AreaController {
    @Resource
    private AreaService areaService;

    @GetMapping("/getAreaListByOrgCode")
    @ApiOperation(value = "获得实战平台-监管管理-获取监所的所有监区和监室,根据监所编码")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orgCode", value = "监所编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "roomCodes", dataType = "String", value = "逗号分隔监室号", required = false)
    })
    public CommonResult<List<AreaListRespVO>> getAreaListByOrgCode(@RequestParam("orgCode") String orgCode,
                                                                   @RequestParam(value = "roomCodes",required = false) String roomCodes) {
        if (StrUtil.isBlank(orgCode)) {
            throw new IllegalArgumentException("orgCode不能为空");
        }
        List<AreaListRespVO> list = areaService.getAreaListByOrgCode(orgCode, AreaTypeEnum.DETENTION_AREA.getCode(),roomCodes);
        return success(list);
    }

    @GetMapping("/getAreaListByOrgCodeAndAreaType")
    @ApiOperation(value = "获得实战平台-监管管理-获取监所的所有监区和各种类型区域,根据监所编码和区域类型")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orgCode", value = "监所编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "areaType", dataType = "String", value = "区域类型(OTHER:其他, DETENTION_FACILITY:监所, DETENTION_AREA:监区, DETENTION_ROOM:监室, STAIRWAY:楼道, CORRIDOR:走廊, GUARD_ROOM:门卫室, INTERROGATION_ROOM:审讯室, ADMISSION_HALL:收押大厅, OFFICE:办公室, MONITORING_CENTER:监控中心, COMPUTER_ROOM:机房, INFIRMARY:医务室, ELECTRONIC_EDUCATION_ROOM:电化教育室, POLICE_EQUIPMENT_ROOM:警用装备室, FAMILY_VISITING_ROOM:家属会见室, LAWYER_VISITING_ROOM:律师会见室, PERIMETER:周界, SENTRY_POST:岗哨, MULTIFUNCTION_HALL:多功能厅, CONFERENCE_ROOM:会议室, TRAINING_FIELD:训练场, WORKSHOP:工场, BOILER_ROOM:开水房, PSYCHOLOGICAL_COUNSELING_ROOM:心理咨询室, SOCIAL_WORKER_OFFICE:社工办公室, KITCHEN:厨房, CONFLICT_RESOLUTION_ROOM:矛盾化解室, WAREHOUSE:仓库, DRESSING_ROOM:更衣室, AB_GATE:AB门, INQUIRY_ROOM:询问室, TALKING_ROOM:谈话室, DUTY_ROOM:值班室, MEDICAL_EXAMINATION_CENTER:体检中心, RECEPTION_HALL:接待大厅, LAWN:草坪, FLOOR:楼层, SUB_CONTROL_ROOM:分控室 )", required = true)
    })
    public CommonResult<List<Tree<String>>> getAreaListByOrgCodeAndAreaType(@RequestParam("orgCode") String orgCode,
                                                                            @RequestParam("areaType") String areaType) {
        if (StrUtil.isBlank(orgCode)) {
            throw new IllegalArgumentException("orgCode不能为空");
        }
        AreaTypeEnum areaTypeEnum = AreaTypeEnum.valueOf(areaType);
        List<Tree<String>> treeList = areaService.getAreaListByOrgCodeAndAreaType(orgCode, areaTypeEnum);
        return success(treeList);
    }

    @GetMapping("/getAreaByOrgCode")
    @ApiOperation(value = "获得实战平台-监管管理-获取监所的监区信息,根据监所编码")
    public CommonResult<List<AreaInfoRespVO>> getAreaByOrgCode(@RequestParam("orgCode") String orgCode) {
        if (StrUtil.isBlank(orgCode)) {
            throw new IllegalArgumentException("orgCode不能为空");
        }
        List<AreaInfoRespVO> list = areaService.getAreaByOrgCode(orgCode, AreaTypeEnum.DETENTION_AREA.getCode());
        return success(list);
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-区域")
    @LogRecordAnnotation(bizModule = "acp:area:create", operateType = LogOperateType.CREATE, title = "创建实战平台-监管管理-区域",
            success = "创建实战平台-监管管理-区域成功", fail = "创建实战平台-监管管理-区域失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createArea(@Valid @RequestBody AreaSaveReqDTO createReqVO) {
        return success(areaService.createArea(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-区域")
    @LogRecordAnnotation(bizModule = "acp:area:update", operateType = LogOperateType.UPDATE, title = "更新实战平台-监管管理-区域",
            success = "更新实战平台-监管管理-区域成功", fail = "更新实战平台-监管管理-区域失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateArea(@Valid @RequestBody AreaSaveReqVO updateReqVO) {
        areaService.updateArea(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-区域")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:area:delete", operateType = LogOperateType.DELETE, title = "删除实战平台-监管管理-区域",
            success = "删除实战平台-监管管理-区域成功", fail = "删除实战平台-监管管理-区域失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteArea(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            areaService.deleteArea(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-区域")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AreaRespVO> getArea(@RequestParam("id") String id) {
        AreaDO area = areaService.getArea(id);
        return success(BeanUtils.toBean(area, AreaRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-区域分页")
    public CommonResult<PageResult<AreaRespVO>> getAreaPage(@Valid @RequestBody AreaPageReqVO pageReqVO) {
        PageResult<AreaDO> pageResult = areaService.getAreaPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AreaRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-区域列表")
    public CommonResult<List<AreaRespVO>> getAreaList(@Valid @RequestBody AreaListReqVO listReqVO) {
        List<AreaDO> list = areaService.getAreaList(listReqVO);
        return success(BeanUtils.toBean(list, AreaRespVO.class));
    }

    /**
     * 监所区域 - 区域树
     * @return
     */
    @GetMapping(value = "getAreaTree")
    @ApiOperation(value = "监所区域树")
    public CommonResult<List<Tree<String>>> getAreaTree(
            @RequestParam("orgCode") String orgCode,
            @RequestParam(name = "areaName", required = false) String areaName) {
        if (StrUtil.isBlank(orgCode)) {
            throw new IllegalArgumentException("orgCode不能为空");
        }
        AreaListReqVO listReqVO = new AreaListReqVO();
        listReqVO.setOrgCode(orgCode);
        listReqVO.setAreaName(areaName);
        return success(areaService.getAreaTree(listReqVO));
    }



    @GetMapping(value = "getAreaCameraTree")
    @ApiOperation(value = "获取监所下摄像头信息")
    public CommonResult<List<Tree<String>>> getAreaCameraTree(
            @RequestParam(value = "orgCode",required = false) String orgCode,
            @RequestParam(name = "areaName", required = false) String areaName) {
        if (StrUtil.isBlank(orgCode)) {
            throw new IllegalArgumentException("orgCode不能为空");
        }
        AreaListReqVO listReqVO = new AreaListReqVO();
        listReqVO.setOrgCode(orgCode);
        listReqVO.setAreaName(areaName);
        List<Tree<String>> result =  areaService.getAreaCameraTree(listReqVO);
        return success(result);
    }

    @GetMapping("/children")
    @ApiOperation(value = "区域管理-点选某个树节点时查询该节点下的所有子节点")
    public CommonResult<List<AreaInfoRespVO>> getChildrenAreaList(
            @RequestParam("orgCode") String orgCode,
            @RequestParam("id") String id,
            @RequestParam(name = "areaName", required = false) String areaName,
            @RequestParam(name = "areaCode", required = false) String areaCode,
            @RequestParam(name = "areaType", required = false) String areaType) {

        List<AreaInfoRespVO> children = areaService.getChildrenAreas(orgCode, id, areaName, areaCode, areaType);
        return success(children);
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "获得实战平台-监管管理-区域详细信息（包括监室信息）")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AreaInfoRespVO> getAreaDetail(@RequestParam("id") String id) {
        AreaInfoRespVO area = areaService.getAreaDetail(id);
        return success(area);
    }

    @ApiOperation(value = "导入区域数据")
    @PostMapping(value = "/importAreaData")
    public CommonResult<String> importAreaData(@RequestParam("file") MultipartFile file) throws Exception {
        List<AreaData> areaDataList = new ArrayList<>();
        AreaDataImportListener listener = new AreaDataImportListener(areaDataList);


        EasyExcel.read(file.getInputStream(), AreaData.class, listener)
                .sheet(0)
                .headRowNumber(2)
                .doRead();

        // 处理导入的数据
         areaService.dealAreaData(areaDataList);

        return CommonResult.success("导入区域数据成功");
    }

    /**
     * 区域导出模板
     * <AUTHOR>
     * @date 2025/5/20 15:25
     * @param [response]
     * @return void
     */
    @ApiOperation("区域导入模板下载")
    @RequestMapping(value = "exportModel", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportModel(HttpServletResponse response) {
        ClassPathResource resource = new ClassPathResource("template/xlsx/qyglmb.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            // 设置响应头
            String fileName = "区域导入模板.xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 直接复制文件流到响应输出流
            IOUtils.copy(inputStream, response.getOutputStream());
        } catch (Exception e) {
            log.error("区域导入模板下载失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }



}
