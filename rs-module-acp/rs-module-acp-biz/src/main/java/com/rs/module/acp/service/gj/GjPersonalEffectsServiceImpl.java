package com.rs.module.acp.service.gj;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.gj.vo.*;
import com.rs.module.acp.entity.gj.GjPersonalEffectsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.GjPersonalEffectsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-随身物品登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GjPersonalEffectsServiceImpl extends BaseServiceImpl<GjPersonalEffectsDao, GjPersonalEffectsDO> implements GjPersonalEffectsService {

    @Resource
    private GjPersonalEffectsDao gjPersonalEffectsDao;

    @Override
    public String createGjPersonalEffects(GjPersonalEffectsSaveReqVO createReqVO) {
        // 插入
        GjPersonalEffectsDO gjPersonalEffects = BeanUtils.toBean(createReqVO, GjPersonalEffectsDO.class);
        gjPersonalEffectsDao.insert(gjPersonalEffects);
        // 返回
        return gjPersonalEffects.getId();
    }

    @Override
    public void updateGjPersonalEffects(GjPersonalEffectsSaveReqVO updateReqVO) {
        // 校验存在
        validateGjPersonalEffectsExists(updateReqVO.getId());
        // 更新
        GjPersonalEffectsDO updateObj = BeanUtils.toBean(updateReqVO, GjPersonalEffectsDO.class);
        gjPersonalEffectsDao.updateById(updateObj);
    }

    @Override
    public void deleteGjPersonalEffects(String id) {
        // 校验存在
        validateGjPersonalEffectsExists(id);
        // 删除
        gjPersonalEffectsDao.deleteById(id);
    }

    private void validateGjPersonalEffectsExists(String id) {
        if (gjPersonalEffectsDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-随身物品登记数据不存在");
        }
    }

    @Override
    public GjPersonalEffectsDO getGjPersonalEffects(String id) {
        return gjPersonalEffectsDao.selectById(id);
    }

    @Override
    public PageResult<GjPersonalEffectsDO> getGjPersonalEffectsPage(GjPersonalEffectsPageReqVO pageReqVO) {
        return gjPersonalEffectsDao.selectPage(pageReqVO);
    }

    @Override
    public List<GjPersonalEffectsDO> getGjPersonalEffectsList(GjPersonalEffectsListReqVO listReqVO) {
        return gjPersonalEffectsDao.selectList(listReqVO);
    }


}
