package com.rs.module.acp.service.gj.equipment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.*;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentSaveExternalInfoVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.AppEquipmentUseRespVO;
import com.rs.module.acp.dao.gj.EquipmentUseDao;
import com.rs.module.acp.dao.gj.EquipmentUseExtendDao;
import com.rs.module.acp.dao.gj.EquipmentUseRemoveDao;
import com.rs.module.acp.entity.gj.EquipmentUseDO;
import com.rs.module.acp.entity.gj.EquipmentUseExtendDO;
import com.rs.module.acp.entity.gj.EquipmentUseRemoveDO;
import com.rs.module.acp.enums.gj.EquipmentStatusEnum;
import com.rs.module.acp.enums.gj.GjRiskAssmtTypeEnum;
import com.rs.module.acp.enums.gj.PrisonRoomStatusEnum;
import com.rs.module.acp.service.gj.punishment.PunishmentService;
import com.rs.module.acp.service.gj.riskassmt.RiskAssmtTodoService;
import com.rs.module.acp.util.EquipmentUseUtil;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.AgeCalculatorUtil;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.util.MsgUtil;
import com.rs.util.DicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

/**
 * 实战平台-管教业务-械具使用 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class EquipmentUseServiceImpl extends BaseServiceImpl<EquipmentUseDao, EquipmentUseDO> implements EquipmentUseService {

    @Resource
    private EquipmentUseDao equipmentUseDao;

    @Resource
    private EquipmentUseExtendDao equipmentUseExtendDao;

    @Resource
    private EquipmentUseRemoveDao equipmentUseRemoveDao;

    @Resource
    private PunishmentService punishmentService;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private RiskAssmtTodoService riskAssmtTodoService;

    @Resource
    private BusTraceService busTraceService;

    private final String defKey = "guanjiaofengxiaopinggushiyongchengpi";

    private final String defKeyExtend = "guanjiaojiejushiyongyanchangshenpi";

    private final String defKeyRemove = "guanjiaojiejushiyongtiqianjiechu";


    @Override
    public String createEquipmentUse(EquipmentUseSaveReqVO createReqVO) {
        // 插入
        EquipmentUseDO equipmentUse = BeanUtils.toBean(createReqVO, EquipmentUseDO.class);
        equipmentUse.setStatus(EquipmentStatusEnum.YDJ.getCode());
        equipmentUseDao.insert(equipmentUse);
        return equipmentUse.getId();
    }

    @Override
    public void updateEquipmentUse(EquipmentUseSaveReqVO updateReqVO) {
        // 校验存在
        validateEquipmentUseExists(updateReqVO.getId());
        // 更新
        EquipmentUseDO updateObj = BeanUtils.toBean(updateReqVO, EquipmentUseDO.class);
        equipmentUseDao.updateById(updateObj);
    }

    @Override
    public void deleteEquipmentUse(String id) {
        // 校验存在
        validateEquipmentUseExists(id);
        // 删除
        equipmentUseDao.deleteById(id);
    }

    private void validateEquipmentUseExists(String id) {
        if (equipmentUseDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-械具使用数据不存在");
        }
    }

    @Override
    public EquipmentUseRespVO getEquipmentUse(String id) {
        EquipmentUseDO equipmentUseDO =  equipmentUseDao.selectById(id);
        EquipmentUseRespVO result = BeanUtils.toBean(equipmentUseDO, EquipmentUseRespVO.class);
        List<EquipmentUseExtendDO> equipmentUseExtendDOList = equipmentUseExtendDao.selectList(new LambdaQueryWrapper<EquipmentUseExtendDO>().eq(EquipmentUseExtendDO::getEquipmentUseId, id).orderByDesc(EquipmentUseExtendDO::getUpdateTime));
        if(CollUtil.isNotEmpty(equipmentUseExtendDOList)){
            List<EquipmentUseExtendRespVO> extendRespVOList = BeanUtils.toBean(equipmentUseExtendDOList, EquipmentUseExtendRespVO.class);
            result.setExtendRespList( extendRespVOList);
        }

        List<EquipmentUseRemoveDO> equipmentUseRemoveList = equipmentUseRemoveDao.selectList(new LambdaQueryWrapper<EquipmentUseRemoveDO>().eq(EquipmentUseRemoveDO::getEquipmentUseId, id).orderByDesc(EquipmentUseRemoveDO::getAddTime));
        if(CollUtil.isNotEmpty(equipmentUseRemoveList)){
            equipmentUseRemoveList.forEach( e->{
                if(EquipmentStatusEnum.YJC.getCode().equals(e.getStatus())){
                    result.setRemoveTime(e.getRemoveTime());
                    result.setRemoveReason(e.getRemoveReason());
                    result.setRemoveRegUser(e.getAddUserName());
                    result.setRemoveExecuteSituation(e.getRemoveExecuteSituation());
                }

                if(EquipmentStatusEnum.DJC.getCode().equals(e.getStatus())){
                    result.setAdvanceRemoveReason(e.getRemoveReason());
                    result.setRemoveApproverSfzh(e.getApproverSfzh());
                    result.setRemoveApproverXm(e.getApproverXm());
                    result.setRemoveApproverTime(e.getApproverTime());
                    result.setRemoveApprovalResult(e.getApprovalResult());
                    result.setRemoveApprovalAutograph(e.getApprovalAutograph());
                    result.setRemoveApprovalComments(e.getApprovalComments());
                }

            });
        }
        return result;
    }

    @Override
    public AppEquipmentUseRespVO getAppEquipmentUse(String id) {
        EquipmentUseDO equipmentUseDO =  equipmentUseDao.selectById(id);
        AppEquipmentUseRespVO result = BeanUtils.toBean(equipmentUseDO, AppEquipmentUseRespVO.class);
        List<EquipmentUseExtendDO> equipmentUseExtendDOList = equipmentUseExtendDao.selectList(
                new LambdaQueryWrapper<EquipmentUseExtendDO>()
                        .eq(EquipmentUseExtendDO::getEquipmentUseId, id)
                        .orderByDesc(EquipmentUseExtendDO::getUpdateTime));
        if(CollUtil.isNotEmpty(equipmentUseExtendDOList)){
            EquipmentUseExtendDO extendDO = equipmentUseExtendDOList.get(0);
            if (EquipmentStatusEnum.YCDSP.getCode().equals(extendDO.getStatus())) {
                result.setExtendReason(extendDO.getExtendReason());
                result.setExtendDay(extendDO.getExtendDay());
            }
        }

        List<EquipmentUseRemoveDO> equipmentUseRemoveList = equipmentUseRemoveDao.selectList(
                new LambdaQueryWrapper<EquipmentUseRemoveDO>()
                        .eq(EquipmentUseRemoveDO::getEquipmentUseId, id)
                        .orderByDesc(EquipmentUseRemoveDO::getAddTime));
        if(CollUtil.isNotEmpty(equipmentUseRemoveList)){
            EquipmentUseRemoveDO removeDO = equipmentUseRemoveList.get(0);
            if(EquipmentStatusEnum.DJC.getCode().equals(removeDO.getStatus())){
                    result.setAdvanceRemoveReason(removeDO.getRemoveReason());
                }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createStartFlow(EquipmentUseStartFlowReqVO createReqVO) {
        // 插入
        EquipmentUseDO equipmentUse = BeanUtils.toBean(createReqVO, EquipmentUseDO.class);
        equipmentUse.setStatus(EquipmentStatusEnum.YDJ.getCode());
        equipmentUseDao.insert(equipmentUse);
        //启动流程审批跳转链接
        String msgUrl = StrUtil.format("/#/discipline/restraintsUsedCheck?curId={}&saveType=approve", equipmentUse.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", equipmentUse.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_JJSY.getCode());

        //【使用情形】：当选择了  BSP 字典编码：ZD_GJ_EQUIPMENT_SCENE   字典代码  01,02,03,04,05
        // 【
        // 一审判处死刑、二审维持原死刑判决等待复核、执行；
        // 在看守所医务室治疗或者住院治疗的、提讯、提解、出庭受审或者出所就医等途中；
        // 因病出所治疗或者其他原因需要出所】；
        // 这5类情形，不需要走审批流程，默认审批通过
        // 】
        if("01".equals(equipmentUse.getExecuteSituation())
                || "02".equals(equipmentUse.getExecuteSituation())
                || "03".equals(equipmentUse.getExecuteSituation())
                || "04".equals(equipmentUse.getExecuteSituation())
                || "05".equals(equipmentUse.getExecuteSituation())){
            CommonResult commonResult =  BspApprovalUtil.autoCompleteProcess(defKey, equipmentUse.getId(), "【审批】戒具使用呈批", msgUrl, variables, HttpUtils.getAppCode());
            if(!commonResult.getSuccess()){
                throw new RuntimeException(commonResult.getMessage());
            }
            equipmentUse.setApprovalResult("1");
            equipmentUse.setApproverTime(new Date());
            equipmentUse.setApproverXm("系统审批");
            equipmentUse.setStatus(EquipmentStatusEnum.DDJ.getCode());
        } else {
            JSONObject result = BspApprovalUtil.commonStartProcess(defKey, equipmentUse.getId(), "【审批】戒具使用呈批", msgUrl, variables, HttpUtils.getAppCode());
            log.info("==========result:{}", result);
            if (result.getIntValue("code") == HttpStatus.OK.value()) {
                JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
                equipmentUse.setActInstId(bpmTrail.getString("actInstId"));
                equipmentUse.setTaskId(bpmTrail.getString("taskId"));
                equipmentUse.setStatus(EquipmentStatusEnum.CPDSP.getCode());
            } else {
                throw new ServerException("流程启动失败");
            }
        }

        equipmentUseDao.updateById(equipmentUse);
        return equipmentUse.getId();
    }

    @Override
    public Boolean submitCreateInfo(String processCmd) {

        //【使用情形】：当选择了  BSP 字典编码：ZD_GJ_EQUIPMENT_SCENE   字典代码  01,02,03,04,05
        // 【
        // 一审判处死刑、二审维持原死刑判决等待复核、执行；
        // 在看守所医务室治疗或者住院治疗的、提讯、提解、出庭受审或者出所就医等途中；
        // 因病出所治疗或者其他原因需要出所】；
        // 这5类情形，不需要走审批流程，默认审批通过
        // 】
        JSONObject jsonObject = JSON.parseObject(processCmd);
        String businessId = jsonObject.getString("businessId");
        EquipmentUseDO equipmentUse = equipmentUseDao.selectById(businessId);
        String defKey = jsonObject.getString("defKey"), msgTit = jsonObject.getString("msgTit"), msgUrl = jsonObject.getString("msgUrl"),fapp = jsonObject.getString("fApp");
        Map<String, Object> variables = jsonObject.getJSONObject("variables");

        //启动流程
        if("01".equals(equipmentUse.getExecuteSituation())
                || "02".equals(equipmentUse.getExecuteSituation())
                || "03".equals(equipmentUse.getExecuteSituation())
                || "04".equals(equipmentUse.getExecuteSituation())
                || "05".equals(equipmentUse.getExecuteSituation())){
            CommonResult commonResult =  BspApprovalUtil.autoCompleteProcess(defKey, equipmentUse.getId(), msgTit, msgUrl, variables, fapp);
            if(!commonResult.getSuccess()){
                throw new RuntimeException(commonResult.getMessage());
            }
            equipmentUse.setApprovalResult("1");
            equipmentUse.setApproverTime(new Date());
            equipmentUse.setApproverXm("系统审批");
            equipmentUse.setStatus(EquipmentStatusEnum.DDJ.getCode());
        } else {
            JSONObject  temp =  BspApprovalUtil.commonStartProcess(defKey, equipmentUse.getId(), msgTit, msgUrl, variables, fapp);
            if(temp.getBoolean("success") == null || !temp.getBoolean("success")){
               throw new RuntimeException("启动流程失败！" + jsonObject.toJSONString());
            }
            equipmentUse.setStatus(EquipmentStatusEnum.CPDSP.getCode());
        }
        equipmentUseDao.updateById(equipmentUse);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean regInfo(EquipmentUseSaveRegInfoVO regInfoVO) {

        EquipmentUseDO equipmentUse = equipmentUseDao.selectById(regInfoVO.getId());
        Assert.notNull(equipmentUse, StrUtil.format("传入ID有误 id：{}", regInfoVO.getId()));
        equipmentUse.setStartTime(regInfoVO.getStartTime());
        equipmentUse.setEndTime(regInfoVO.getEndTime());
        equipmentUse.setActualStartTime(regInfoVO.getStartTime());
        equipmentUse.setActualEndTime(regInfoVO.getEndTime());
        equipmentUse.setExecuteSituation(regInfoVO.getExecuteSituation());
        equipmentUse.setUseRegTime(new Date());

        equipmentUse.setStatus(EquipmentStatusEnum.JISY.getCode());

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        equipmentUse.setUseRegUser(sessionUser.getName());
        equipmentUseDao.updateById(equipmentUse);

        RiskAssmtTodoSaveReqVO riskAssmtTodoSaveReqVO = new RiskAssmtTodoSaveReqVO();
        riskAssmtTodoSaveReqVO.setJgrybm( equipmentUse.getJgrybm());
        riskAssmtTodoSaveReqVO.setRiskType(GjRiskAssmtTypeEnum.JJSYFXPG.getCode());
        riskAssmtTodoSaveReqVO.setSourceBusinessId(equipmentUse.getId());
        riskAssmtTodoService.createRiskAssmtTodo(riskAssmtTodoSaveReqVO);

        MsgAddVO vo = new MsgAddVO();
        vo.setPcid(equipmentUse.getId());
        //TODO 前端提供
        vo.setUrl("");
        vo.setMsgType("10");
        vo.setModuleCode("ACP_GJ_RISK_MENT_IN_TODO");
        vo.setOrgCode(equipmentUse.getOrgCode());
        vo.setOrgName(equipmentUse.getOrgName());
        vo.setJgrybm(equipmentUse.getJgrybm());
        vo.setToOrgCode(equipmentUse.getOrgCode());
        vo.setBusinessId(equipmentUse.getId());
        MsgUtil.sendMsg(vo);

        //审批通过，且需要关联惩罚
        if(equipmentUse.getIsAssociatedPunishment() != null && equipmentUse.getIsAssociatedPunishment() ==  1){
            PunishmentSaveExternalInfoVO punVO = new PunishmentSaveExternalInfoVO();
            punVO.setId(equipmentUse.getId());
            //punVO.setReasonName(equipmentUse.getUseReason());
            punVO.setRemark(equipmentUse.getRemark());
            punVO.setAddUser(equipmentUse.getAddUser());
            punVO.setAddUserName(equipmentUse.getAddUserName());
            punVO.setAddTime(equipmentUse.getAddTime());
            punVO.setDataSources("1");
            punVO.setJgrybm(equipmentUse.getJgrybm());
            //punVO.setJgryxm(entity.getJgrybm());
            punVO.setReasonName( equipmentUse.getUseReason());
            punVO.setMeasures(equipmentUse.getPunishmentMeasures());
            punVO.setMeasuresName(DicUtils.translate( "ZD_GJCFNR",equipmentUse.getPunishmentMeasures()) );
            punVO.setStartDate(equipmentUse.getStartTime());
            punVO.setEndDate(equipmentUse.getEndTime());

            PrisonerVwRespVO respVO = prisonerService.getPrisonerSelectCompomenOne(equipmentUse.getJgrybm(), PrisonerQueryRyztEnum.ZS);
            punVO.setJgryxm(respVO.getXm());
            punishmentService.saveExternal(punVO);
        }

        return true;
    }

    @Override
    public List<EquipmentUseRespVO> getEquipmentUseListByJgrybm(String jgrybm) {
        List<EquipmentUseDO> equipmentUseList = equipmentUseDao
                .selectList(
                        new LambdaQueryWrapper<EquipmentUseDO>().
                                in(EquipmentUseDO::getStatus,
                                    //这三个状态表示有使用过
                                    Arrays.asList(EquipmentStatusEnum.YCDSP.getCode(),
                                            EquipmentStatusEnum.TQJCDSP.getCode(),
                                            EquipmentStatusEnum.JISY.getCode(),
                                            EquipmentStatusEnum.DJC.getCode(),
                                            EquipmentStatusEnum.YJC.getCode())
                                ).eq(EquipmentUseDO::getJgrybm, jgrybm).orderByDesc(EquipmentUseDO::getAddTime));
        if(CollUtil.isNotEmpty(equipmentUseList)){
            return BeanUtils.toBean(equipmentUseList, EquipmentUseRespVO.class);
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean extendApproval(EquipmentExtendApprovalApplyReqVO createReqVO) {
        EquipmentUseDO equipmentUse = equipmentUseDao.selectById(createReqVO.getId());
        Assert.notNull(equipmentUse, StrUtil.format("传入ID有误 id：{}", createReqVO.getId()));

        EquipmentUseExtendDO useExtendDO = EquipmentUseExtendDO.builder().build();
        useExtendDO.setEquipmentUseId(equipmentUse.getId());
        useExtendDO.setExtendReason(createReqVO.getExtendReason());
        useExtendDO.setExtendDay(createReqVO.getExtendDay());

        useExtendDO.setStatus(EquipmentStatusEnum.YDJ.getCode());
        equipmentUseExtendDao.insert(useExtendDO);

        //TODO 待前端提供
        String msgUrl = StrUtil.format("/#/discipline/restraintsUsedCheck?curId={}&saveType=approve", createReqVO.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", createReqVO.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_JJSY.getCode());
        JSONObject result = BspApprovalUtil.commonStartProcess(defKeyExtend, useExtendDO.getId(), "【审批】戒具使用呈批", msgUrl, variables, HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            useExtendDO.setActInstId(bpmTrail.getString("actInstId"));
            useExtendDO.setTaskId(bpmTrail.getString("taskId"));
            useExtendDO.setStatus(EquipmentStatusEnum.YCDSP.getCode());
            equipmentUseExtendDao.updateById( useExtendDO);
            equipmentUse.setStatus(EquipmentStatusEnum.YCDSP.getCode());
            equipmentUseDao.updateById(equipmentUse);
        } else {
            throw new ServerException("流程启动失败");
        }
        return true;
    }

    @Override
    public void leaderApproveExtendApply(GjApproveReqVO approveReqVO) {
        EquipmentUseDO equipmentUse = equipmentUseDao.selectById(approveReqVO.getId());
        Assert.notNull(equipmentUse, StrUtil.format("传入ID有误 id：{}", equipmentUse.getId()));
        List<EquipmentUseExtendDO> useExtendDOList = equipmentUseExtendDao.selectList(new LambdaQueryWrapper<EquipmentUseExtendDO>().eq(EquipmentUseExtendDO::getEquipmentUseId,approveReqVO.getId() ).orderByDesc(EquipmentUseExtendDO::getAddTime));
        if(CollUtil.isNotEmpty(useExtendDOList)){
            EquipmentUseExtendDO equipmentUseExtendDO = useExtendDOList.get(0);

            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            EquipmentStatusEnum statusEnum = EquipmentStatusEnum.getByCode(equipmentUseExtendDO.getStatus());
            if (!EquipmentStatusEnum.YCDSP.getCode().equals(statusEnum.getCode())) {
                throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
            }
            String statusName = "已通过审批", passStatus =  "审批通过";
            String approvalResult = approveReqVO.getApprovalResult();
            BspApproceStatusEnum bspApproceStatusEnum;
            if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
                bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
                equipmentUseExtendDO.setStatus(EquipmentStatusEnum.JISY.getCode());
                equipmentUse.setUseDays( equipmentUseExtendDO.getExtendDay() +  equipmentUse.getUseDays());

                if(equipmentUseExtendDO.getExtendDay() != null ){
                    Date newDate = DateUtil.offsetDay(equipmentUse.getActualEndTime(), equipmentUseExtendDO.getExtendDay() );
                    equipmentUse.setActualEndTime(newDate);
                }

                equipmentUse.setStatus(EquipmentStatusEnum.JISY.getCode());
                equipmentUseDao.updateById(equipmentUse);

            } else {
                bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
                equipmentUseExtendDO.setStatus(EquipmentStatusEnum.CPBTG.getCode());
                statusName = "审批不通过";
                passStatus =  statusName;
            }

            //校验当前人有没有审批权限
            Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(equipmentUseExtendDO.getTaskId(), sessionUser.getIdCard());
            if (!isApproval) {
                throw new ServerException("当前人无审批权限");
            }
            equipmentUseExtendDO.setApprovalComments(approveReqVO.getApprovalComments());
            equipmentUseExtendDO.setApproverXm(sessionUser.getName());
            equipmentUseExtendDO.setApproverSfzh(sessionUser.getIdCard());
            equipmentUseExtendDO.setApprovalResult(approveReqVO.getApprovalResult());
            equipmentUseExtendDO.setApproverTime(new Date());

            Map<String, Object> variables = new HashMap<>();
            variables.put("ywbh", equipmentUseExtendDO.getId());
            variables.put("busType", MsgBusTypeEnum.GJ_JJSY.getCode());

            //审批
            JSONObject nowApproveUser = new JSONObject();
            nowApproveUser.put("orgCode", sessionUser.getOrgCode());
            nowApproveUser.put("orgName", sessionUser.getOrgName());
            nowApproveUser.put("idCard", sessionUser.getIdCard());
            nowApproveUser.put("name", sessionUser.getName());
            boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                    BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
            JSONObject result = BspApprovalUtil.approvalProcess(defKey, equipmentUseExtendDO.getActInstId(), equipmentUseExtendDO.getTaskId(), equipmentUseExtendDO.getId(),
                    bspApproceStatusEnum, equipmentUseExtendDO.getApprovalComments(), null, null, terminateTask,
                    variables, nowApproveUser, HttpUtils.getAppCode());
            log.info("=======result:{}", result);
            if (result.getIntValue("code") == HttpStatus.OK.value()) {
                JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
                equipmentUseExtendDO.setTaskId(bpmTrail.getString("taskId"));
            } else {
                throw new ServerException("流程审批失败");
            }
            String apprName = "延长戒具使用审批";
            sendMsg(apprName, passStatus, equipmentUse.getId(),equipmentUse.getJgrybm(), equipmentUse.getAddTime(), equipmentUse.getAddUserName(), statusName, equipmentUseExtendDO.getId());
            equipmentUseExtendDao.updateById( equipmentUseExtendDO);

        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeApprovalApply(EquipmentUseRemoveApplyReqVO createReqVO) {
        EquipmentUseDO equipmentUse = equipmentUseDao.selectById(createReqVO.getId());
        Assert.notNull(equipmentUse, StrUtil.format("传入ID有误 id：{}", equipmentUse.getId()));

        EquipmentUseRemoveDO useExtendDO = EquipmentUseRemoveDO.builder().build();
        useExtendDO.setEquipmentUseId(equipmentUse.getId());
        useExtendDO.setRemoveReason(createReqVO.getRemoveReason());
        useExtendDO.setRemoveExecuteSituation(createReqVO.getRemoveExecuteSituation());

        useExtendDO.setStatus(EquipmentStatusEnum.TQJCDSP.getCode());
        equipmentUseRemoveDao.insert(useExtendDO);

        //TODO 启动流程审批跳转链接  等待前端提供
        String msgUrl = StrUtil.format("/#/discipline/restraintsUsedCheck?curId={}&saveType=revoke", useExtendDO.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", useExtendDO.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_JJSY.getCode());
        JSONObject result = BspApprovalUtil.commonStartProcess(defKeyRemove, useExtendDO.getId(), "【审批】提前解除戒具审批", msgUrl, variables, HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            useExtendDO.setActInstId(bpmTrail.getString("actInstId"));
            useExtendDO.setTaskId(bpmTrail.getString("taskId"));
            equipmentUseRemoveDao.updateById(useExtendDO);
        } else {
            throw new ServerException("流程启动失败");
        }
        equipmentUse.setIsRemove(1);
        equipmentUse.setStatus(EquipmentStatusEnum.TQJCDSP.getCode() );
        equipmentUseDao.updateById( equipmentUse);

        return true;

    }

    @Override
    public void leaderApproveRemoveApply(GjApproveReqVO approveReqVO) {
        EquipmentUseDO equipmentUse = equipmentUseDao.selectById(approveReqVO.getId());
        Assert.notNull(equipmentUse, StrUtil.format("传入ID有误 id：{}", equipmentUse.getId()));
        List<EquipmentUseRemoveDO> useExtendDOList = equipmentUseRemoveDao.selectList(new LambdaQueryWrapper<EquipmentUseRemoveDO>()
                        .eq(EquipmentUseRemoveDO::getStatus, EquipmentStatusEnum.TQJCDSP.getCode())
                .eq(EquipmentUseRemoveDO::getEquipmentUseId,approveReqVO.getId() ).orderByDesc(EquipmentUseRemoveDO::getAddTime));
        if(CollUtil.isNotEmpty(useExtendDOList)){
            EquipmentUseRemoveDO equipmentUseExtendDO = useExtendDOList.get(0);
            BeanUtils.copyProperties(approveReqVO, equipmentUseExtendDO);

            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            EquipmentStatusEnum statusEnum = EquipmentStatusEnum.getByCode(equipmentUseExtendDO.getStatus());
            if (!EquipmentStatusEnum.TQJCDSP.getCode().equals(statusEnum.getCode())) {
                throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
            }

            equipmentUseExtendDO.setStatus(EquipmentStatusEnum.DJC.getCode());
            equipmentUse.setStatus(EquipmentStatusEnum.DJC.getCode());

            String statusName = "已通过审批", passStatus =  "审批通过";
            String approvalResult = approveReqVO.getApprovalResult();
            BspApproceStatusEnum bspApproceStatusEnum;
            if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
                bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
                equipmentUseExtendDO.setStatus(EquipmentStatusEnum.DJC.getCode());
                equipmentUse.setIsRemove(1);
                equipmentUse.setStatus(EquipmentStatusEnum.DJC.getCode());
                equipmentUseExtendDO.setRemoveTime(new Date());
                equipmentUse.setActualEndTime(new Date());
            } else {
                bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
                equipmentUseExtendDO.setStatus(EquipmentStatusEnum.JISY.getCode());
                equipmentUse.setStatus(EquipmentStatusEnum.JISY.getCode());
                statusName = "审批不通过";
                passStatus =  statusName;
            }
            //equipmentUseRemoveDao.updateById(equipmentUseExtendDO);
            equipmentUseDao.updateById(equipmentUse);

            //校验当前人有没有审批权限
            Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(equipmentUseExtendDO.getTaskId(), sessionUser.getIdCard());
            if (!isApproval) {
                throw new ServerException("当前人无审批权限");
            }
            equipmentUseExtendDO.setApprovalComments(approveReqVO.getApprovalComments());
            equipmentUseExtendDO.setApproverXm(sessionUser.getName());
            equipmentUseExtendDO.setApproverSfzh(sessionUser.getIdCard());
            equipmentUseExtendDO.setApprovalResult(approveReqVO.getApprovalResult());
            equipmentUseExtendDO.setApproverTime(new Date());

            Map<String, Object> variables = new HashMap<>();
            variables.put("ywbh", equipmentUseExtendDO.getId());
            variables.put("busType", MsgBusTypeEnum.GJ_JJSY.getCode());
            //审批
            JSONObject nowApproveUser = new JSONObject();
            nowApproveUser.put("orgCode", sessionUser.getOrgCode());
            nowApproveUser.put("orgName", sessionUser.getOrgName());
            nowApproveUser.put("idCard", sessionUser.getIdCard());
            nowApproveUser.put("name", sessionUser.getName());
            boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                    BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
            JSONObject result = BspApprovalUtil.approvalProcess(defKey, equipmentUseExtendDO.getActInstId(), equipmentUseExtendDO.getTaskId(), equipmentUseExtendDO.getId(),
                    bspApproceStatusEnum, equipmentUseExtendDO.getApprovalComments(), null, null, terminateTask,
                    variables, nowApproveUser, HttpUtils.getAppCode());
            log.info("=======result:{}", result);
            if (result.getIntValue("code") == HttpStatus.OK.value()) {
                JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
                equipmentUseExtendDO.setTaskId(bpmTrail.getString("taskId"));
            } else {
                throw new ServerException("流程审批失败");
            }
            String apprName = "提前戒具使用审批";
            sendMsg(apprName, passStatus, equipmentUse.getId(),equipmentUse.getJgrybm(), equipmentUse.getAddTime(), equipmentUse.getAddUserName(), statusName, equipmentUseExtendDO.getId());
            equipmentUseRemoveDao.updateById( equipmentUseExtendDO);


            if( BspApproceStatusEnum.PASSED_END.equals(bspApproceStatusEnum)){
                PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( equipmentUse.getJgrybm());
                busTraceService.saveBusTrace(BusTypeEnum.YEWU_JJJC, GjBusTraceUtil.buildEquipmentUseCanalBusTraceContent(equipmentUse, equipmentUseExtendDO,prisoner.getXm()),
                        equipmentUse.getJgrybm(),
                        SessionUserUtil.getSessionUser().getOrgCode(),
                        equipmentUse.getId());
            }


        }
    }

    @Override
    public void leaderApproveApply(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        EquipmentUseDO entity = equipmentUseDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, StrUtil.format("传入ID有误 id：{}", entity.getId()));

        EquipmentStatusEnum statusEnum = EquipmentStatusEnum.getByCode(entity.getStatus());
        if (!PrisonRoomStatusEnum.DSP.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }
        String statusName = "已通过审批", passStatus =  "审批通过";
        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
            entity.setStatus(EquipmentStatusEnum.DDJ.getCode());
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            entity.setStatus(EquipmentStatusEnum.CPBTG.getCode());
            statusName = "审批不通过";
            passStatus =  statusName;
        }
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        entity.setApprovalComments(approveReqVO.getApprovalComments());
        entity.setApproverXm(sessionUser.getName());
        entity.setApproverSfzh(sessionUser.getIdCard());
        entity.setApprovalResult(approveReqVO.getApprovalResult());
        entity.setApproverTime(new Date());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", entity.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_JJSY.getCode());

        //判断是否需要走公安机构领导审批
        PrisonerVwRespVO prisonerVwRespVO =  prisonerService.getPrisonerSelectCompomenOne(entity.getJgrybm(), PrisonerQueryRyztEnum.ALL);
        //判断被监管人员，是否需要固定戒具，或者老年人、未成年人
        Integer flowStatus = 0;
        if(prisonerVwRespVO.getCsrq() != null){
            if(AgeCalculatorUtil.calculateAge(prisonerVwRespVO.getCsrq()) > 60 && AgeCalculatorUtil.calculateAge(prisonerVwRespVO.getCsrq()) < 18){
                flowStatus = 1;
            }
        }
        //固定戒具
        if(entity.getIsTempFixation() != null && entity.getIsTempFixation() == 1){
            flowStatus = 1;
        }
        variables.put("flowStatus", flowStatus);


        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(defKey, entity.getActInstId(), entity.getTaskId(), entity.getId(),
                bspApproceStatusEnum, entity.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败");
        }
        String apprName = "戒具使用呈批审批";
        sendMsg(apprName, passStatus, entity.getId(),entity.getJgrybm(), entity.getAddTime(), entity.getAddUserName(), statusName, entity.getId());

        //审批通过 且 需要公安机构领导审批
        if(BspApproceStatusEnum.PASSED_END.equals(bspApproceStatusEnum) && flowStatus == 1){
            entity.setStatus(EquipmentStatusEnum.CPDSP.getCode());
        }
        equipmentUseDao.updateById( entity);


        //链路
        if( BspApproceStatusEnum.PASSED_END.equals(bspApproceStatusEnum)){
            PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( entity.getJgrybm());
            busTraceService.saveBusTrace(BusTypeEnum.YEWU_XJSY, GjBusTraceUtil.buildEquipmentUseBusTraceContent(entity,prisoner.getXm()),
                    entity.getJgrybm(),
                    SessionUserUtil.getSessionUser().getOrgCode(),
                    entity.getId());
        }



    }

    @Override
    public List<GjApprovalTraceVO> getApproveTrack(String id) {
        //EquipmentStatusEnum
        List<GjApprovalTraceVO> result = new ArrayList<>();
        EquipmentUseDO entity = equipmentUseDao.selectById(id);
        if(entity == null){
            return result;
        }
        //戒具使用呈批-start
        GjApprovalTraceVO approvalTraceVOJjsysp = new GjApprovalTraceVO();
        approvalTraceVOJjsysp.setNodeKey("jjsysp");
        approvalTraceVOJjsysp.setNodeName("戒具使用呈批");
        approvalTraceVOJjsysp.setNodeStatus(1);
        approvalTraceVOJjsysp.setNodeCreateTime(DateUtil.formatDateTime(entity.getAddTime()));
        List<GjApprovalTraceVO.TraceVO> nodeInfoList = new ArrayList<>();
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("呈批人").val(entity.getAddUserName()).build());
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("呈批时间").val( DateUtil.formatDateTime(entity.getAddTime())).build());
        if(StrUtil.isNotBlank(entity.getPunishmentToolType())){
            StringBuffer sb = new StringBuffer();
            String[] array = entity.getPunishmentToolType().split(",");
            for (int i = 0; i < array.length; i++) {
                String dic =  DicUtils.translate("ZD_GJ_EQUIPMENT_TYPE", array[i]);
                if(StrUtil.isNotBlank(dic)){
                    if(sb.length() > 0){
                        sb.append("，");
                    }
                    sb.append(dic);
                }
            }
            if(sb.length() > 0){
                nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("戒具类型").val( DateUtil.formatDateTime(entity.getAddTime())).build());
            }
        }
        if(StrUtil.isNotBlank( entity.getUseDays())){
            nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("戒具使用天数").val( entity.getUseDays() + "天").build());
        }
        approvalTraceVOJjsysp.setNodeInfo(nodeInfoList);
        result.add(approvalTraceVOJjsysp);
        //戒具使用呈批-end

        // 公安侧，领导审批意见-Start
        List<GjApprovalTraceVO> apprJjsyList = EquipmentUseUtil.converBspApprovalTrack(entity.getActInstId());
        if(CollUtil.isNotEmpty( apprJjsyList)){
            result.addAll(apprJjsyList);
        }
        // 公安侧，领导审批意见-end

        //戒具使用登记 -start
        if(StrUtil.isNotBlank( entity.getUseRegUser())){
            GjApprovalTraceVO approvalTraceVOJjsyspdj = new GjApprovalTraceVO();
            approvalTraceVOJjsyspdj.setNodeKey("jjsyspdj");
            approvalTraceVOJjsyspdj.setNodeName("戒具使用登记");
            approvalTraceVOJjsyspdj.setNodeStatus(1);
            approvalTraceVOJjsyspdj.setNodeCreateTime(DateUtil.formatDateTime(entity.getUseRegTime()));
            List<GjApprovalTraceVO.TraceVO> nodeInfoListJjsyspdj  = new ArrayList<>();
            nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("登记人").val(entity.getUseRegUser()).build());
            nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("登记时间").val( DateUtil.formatDateTime(entity.getUseRegTime())).build());
            nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("执行情况").val( entity.getExecuteSituation()).build());
            approvalTraceVOJjsyspdj.setNodeInfo(nodeInfoListJjsyspdj );
            result.add( approvalTraceVOJjsyspdj);
        }
        //戒具使用登记 -end

        //延长戒具审批 -Start
        List<EquipmentUseExtendDO> useExtendDOList = equipmentUseExtendDao.selectList(new LambdaQueryWrapper<EquipmentUseExtendDO>().eq(EquipmentUseExtendDO::getEquipmentUseId, entity.getId()));
        if(CollUtil.isNotEmpty(useExtendDOList)){
            useExtendDOList.forEach( e->{
                GjApprovalTraceVO approvalTraceVOTemp = new GjApprovalTraceVO();
                approvalTraceVOTemp.setNodeKey("ycjjsycp");
                approvalTraceVOTemp.setNodeName("延长戒具使用呈批");
                approvalTraceVOTemp.setNodeStatus(1);
                approvalTraceVOTemp.setNodeCreateTime(DateUtil.formatDateTime(e.getAddTime()));
                List<GjApprovalTraceVO.TraceVO> nodeInfoLisTemp  = new ArrayList<>();
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("呈批人").val(e.getAddUserName()).build());
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("呈批时间").val( DateUtil.formatDateTime(e.getAddTime())).build());
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("延长天数").val( e.getExtendDay() + "天").build());
                approvalTraceVOTemp.setNodeInfo(nodeInfoLisTemp );
                result.add( approvalTraceVOTemp);

                List<GjApprovalTraceVO> apprYLcList = EquipmentUseUtil.converBspApprovalTrack(e.getActInstId());
                if(CollUtil.isNotEmpty( apprYLcList)){
                    result.addAll(apprYLcList);
                }
            });

        }
        //延长戒具审批 -end

        //解除 戒具审批 -Start
        List<EquipmentUseRemoveDO> removeDOList = equipmentUseRemoveDao.selectList(new LambdaQueryWrapper<EquipmentUseRemoveDO>().eq(EquipmentUseRemoveDO::getEquipmentUseId, entity.getId()));
        if(CollUtil.isNotEmpty(removeDOList)){
            removeDOList.forEach( e->{
                GjApprovalTraceVO approvalTraceVOTemp = new GjApprovalTraceVO();
                approvalTraceVOTemp.setNodeKey("tqjcjjsysp");
                approvalTraceVOTemp.setNodeName("提前解除戒具使用呈批");
                approvalTraceVOTemp.setNodeStatus(1);
                approvalTraceVOTemp.setNodeCreateTime(DateUtil.formatDateTime(entity.getAddTime()));
                List<GjApprovalTraceVO.TraceVO> nodeInfoLisTemp  = new ArrayList<>();
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("呈批人").val(e.getAddUserName()).build());
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("呈批时间").val( DateUtil.formatDateTime(e.getAddTime())).build());
                nodeInfoLisTemp.add(GjApprovalTraceVO.TraceVO.builder().key("提前解除理由").val(  e.getRemoveReason()).build());
                approvalTraceVOTemp.setNodeInfo(nodeInfoLisTemp );
                result.add( approvalTraceVOTemp);

                List<GjApprovalTraceVO> apprYLcList = EquipmentUseUtil.converBspApprovalTrack(e.getActInstId());
                if(CollUtil.isNotEmpty( apprYLcList)){
                    result.addAll(apprYLcList);
                }

                //解除登记 戒具审批 -Start
                if(StrUtil.isNotBlank( e.getRemoveExecuteSituation())){
                    GjApprovalTraceVO approvalTraceVOJjsyspdj = new GjApprovalTraceVO();
                    approvalTraceVOJjsyspdj.setNodeKey("jcjjsydj");
                    approvalTraceVOJjsyspdj.setNodeName("解除戒具使用登记");
                    approvalTraceVOJjsyspdj.setNodeStatus(1);
                    approvalTraceVOJjsyspdj.setNodeCreateTime(DateUtil.formatDateTime(entity.getUpdateTime()));
                    List<GjApprovalTraceVO.TraceVO> nodeInfoListJjsyspdj  = new ArrayList<>();
                    nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("登记人").val(entity.getUpdateUserName()).build());
                    nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("登记时间").val( DateUtil.formatDateTime(entity.getUpdateTime())).build());
                    nodeInfoListJjsyspdj.add(GjApprovalTraceVO.TraceVO.builder().key("执行情况").val( e.getRemoveExecuteSituation()).build());
                    approvalTraceVOJjsyspdj.setNodeInfo(nodeInfoListJjsyspdj );
                    result.add( approvalTraceVOJjsyspdj);
                }
                //解除登记 戒具审批 -Start

            });
        }
        //解除 戒具审批  -end
        //Collections.sort(result, Comparator.comparing(
        //        GjApprovalTraceVO::getNodeCreateTime,
        //        (dateStr1, dateStr2) -> {
        //            try {
        //                return DateUtil.parse(dateStr2).compareTo(DateUtil.parse(dateStr1));
        //            } catch (Exception e) {
        //                throw new IllegalArgumentException("日期格式错误: " + e.getMessage(), e);
        //            }
        //        }
        //));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeRegInfo(EquipmentUseRemoveApplyRegInfoVO regInfo) {

        EquipmentUseDO equipmentUse = equipmentUseDao.selectById(regInfo.getId());
        Assert.notNull(equipmentUse, StrUtil.format("传入ID有误 id：{}", regInfo.getId()));
        equipmentUse.setRemoveTime(new Date());
        equipmentUse.setStatus(EquipmentStatusEnum.YJC.getCode());
        equipmentUseDao.updateById(equipmentUse);

        EquipmentUseRemoveDO equipmentUseRemoveDO = BeanUtils.toBean(regInfo, EquipmentUseRemoveDO.class);
        equipmentUseRemoveDO.setId(null);
        equipmentUseRemoveDO.setIsRemove(equipmentUse.getIsRemove());
        equipmentUseRemoveDO.setEquipmentUseId( equipmentUse.getId());
        equipmentUseRemoveDO.setStatus(EquipmentStatusEnum.YJC.getCode());
        equipmentUseRemoveDao.insert(equipmentUseRemoveDO);
    }

    @Override
    public List<EquipmentUseRespWearVO> getWearEquipmentUse(Date startTime, Date endTime) {
        return equipmentUseDao.getWearEquipmentUse(startTime, endTime);
    }

    /**
     * 消息发送
     * <AUTHOR>
     * @date 2025/6/7 10:01
     * @param [apprName, passStatus, bizId, jgrybm, addTime, addUserName, statusName, pcid]
     * @return void
     */
    private void sendMsg(String apprName,String passStatus,String bizId,String jgrybm,Date addTime,String addUserName,String statusName,String pcid){
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        //该审批，可能是申请，也可能是撤销
        String moduleCode = "GJ_EQUIPMENT_USE";
        String msgType = "12";
        String title = apprName + passStatus ;	//消息标题
        //entity.setStatus(status);
        MsgAddVO msg = new MsgAddVO();
        // 等前端路由封装
        String msgUrl = StrUtil.format("/#/discipline/restraintsUsedCheck?curId={}&saveType=revoke", bizId);
        msg.setUrl(msgUrl);
        msg.setModuleCode(moduleCode);
        //消息类型代码 字典编码对应：ZD_MSG_BUSTYP
        msg.setMsgType(msgType);
        msg.setTitle(title);
        msg.setOrgName(sessionUser.getOrgName());
        msg.setToOrgCode(sessionUser.getOrgCode());
        msg.setBusinessId(bizId);
        msg.setJgrybm(jgrybm);
        Map<String, Object> contentData = new HashMap<>();
        contentData.put("addTime", DateUtil.formatDateTime(addTime));
        contentData.put("addUserName", addUserName);
        contentData.put("statusName", statusName);
        contentData.put("typeName", "使用呈批");
        msg.setContentData(contentData);
        msg.setPcid(pcid);
        MsgUtil.sendMsg(msg);
    }

}
