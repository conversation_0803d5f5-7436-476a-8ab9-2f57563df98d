package com.rs.module.acp.dao.pi;

import com.rs.module.acp.entity.pi.FixedScreenMonitorPrisonerDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-巡视管控-定屏监控与被监管人员关联 Dao
*
* <AUTHOR>
*/
@Mapper
public interface FixedScreenMonitorPrisonerDao extends IBaseDao<FixedScreenMonitorPrisonerDO> {
    @Delete("delete from acp_pi_fixed_screen_monitor_prisoner where fixed_screen_monitor_id = #{fixedScreenMonitorId}")
    void deleteByFixedScreenMonitorId(@Param("fixedScreenMonitorId") String fixedScreenMonitorId);
}
