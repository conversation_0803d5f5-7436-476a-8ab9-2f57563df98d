package com.rs.module.acp.controller.admin.gj;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.ZyflzdFeedbackReqVO;
import com.rs.module.acp.controller.admin.gj.vo.ZyflzdRespVO;
import com.rs.module.acp.controller.admin.gj.vo.ZyflzdSaveReqVO;
import com.rs.module.acp.entity.gj.ZyflzdDO;
import com.rs.module.acp.service.gj.ZyflzdService;
import com.rs.module.base.vo.FileReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管教业务-专业法律指导")
@RestController
@RequestMapping("/acp/gj/zyflzd")
@Validated
public class ZyflzdController {

    @Resource
    private ZyflzdService zyflzdService;

    @PostMapping("/create")
    @ApiOperation(value = "创建专业法律指导")
    public CommonResult<String> createZyflzd(@Valid @RequestBody ZyflzdSaveReqVO createReqVO) {
        return success(zyflzdService.createZyflzd(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新专业法律指导")
    public CommonResult<Boolean> updateZyflzd(@Valid @RequestBody ZyflzdSaveReqVO updateReqVO) {
        zyflzdService.updateZyflzd(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除专业法律指导")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteZyflzd(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           zyflzdService.deleteZyflzd(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得专业法律指导")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ZyflzdRespVO> getZyflzd(@RequestParam("id") String id) {
        ZyflzdDO zyflzd = zyflzdService.getZyflzd(id);
        ZyflzdRespVO respVO = BeanUtils.toBean(zyflzd, ZyflzdRespVO.class);
        respVO.setFkfjList(JSONObject.parseArray(zyflzd.getFkfj(), FileReqVO.class));
        return success(respVO);
    }

    @PostMapping("/feedback")
    @ApiOperation(value = "反馈")
    public CommonResult<Boolean> feedback(@Valid @RequestBody ZyflzdFeedbackReqVO feedback) {
        zyflzdService.feedback(feedback);
        return success(true);
    }

}
