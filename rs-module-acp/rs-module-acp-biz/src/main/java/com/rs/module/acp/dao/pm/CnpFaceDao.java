package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.pm.CnpFaceDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-监管管理-仓内外屏人脸信息维护 Dao
*
* <AUTHOR>
*/
@Mapper
public interface CnpFaceDao extends IBaseDao<CnpFaceDO> {


    default PageResult<CnpFaceDO> selectPage(CnpFacePageReqVO reqVO) {
        Page<CnpFaceDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CnpFaceDO> wrapper = new LambdaQueryWrapperX<CnpFaceDO>()
            .eqIfPresent(CnpFaceDO::getPersonnelType, reqVO.getPersonnelType())
            .eqIfPresent(CnpFaceDO::getPersonnelCode, reqVO.getPersonnelCode())
            .eqIfPresent(CnpFaceDO::getPhoto, reqVO.getPhoto())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(CnpFaceDO::getAddTime);
        }
        Page<CnpFaceDO> cnpFacePage = selectPage(page, wrapper);
        return new PageResult<>(cnpFacePage.getRecords(), cnpFacePage.getTotal());
    }
    default List<CnpFaceDO> selectList(CnpFaceListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CnpFaceDO>()
            .eqIfPresent(CnpFaceDO::getPersonnelType, reqVO.getPersonnelType())
            .eqIfPresent(CnpFaceDO::getPersonnelCode, reqVO.getPersonnelCode())
            .eqIfPresent(CnpFaceDO::getPhoto, reqVO.getPhoto())
        .orderByDesc(CnpFaceDO::getAddTime));    }


    }
