package com.rs.module.acp.service.pi;

import javax.validation.*;

import com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor.FixedScreenMonitorPrisonerSaveReqVO;
import com.rs.module.acp.entity.pi.FixedScreenMonitorDO;
import com.rs.module.acp.entity.pi.FixedScreenMonitorPrisonerDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-定屏监控与被监管人员关联 Service 接口
 *
 * <AUTHOR>
 */
public interface FixedScreenMonitorPrisonerService extends IBaseService<FixedScreenMonitorPrisonerDO>{

    /**
     * 创建实战平台-巡视管控-定屏监控与被监管人员关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFixedScreenMonitorPrisoner(@Valid FixedScreenMonitorPrisonerSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-定屏监控与被监管人员关联
     *
     * @param updateReqVO 更新信息
     */
    void updateFixedScreenMonitorPrisoner(@Valid FixedScreenMonitorPrisonerSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-定屏监控与被监管人员关联
     *
     * @param id 编号
     */
    void deleteFixedScreenMonitorPrisoner(String id);

    /**
     * 获得实战平台-巡视管控-定屏监控与被监管人员关联
     *
     * @param id 编号
     * @return 实战平台-巡视管控-定屏监控与被监管人员关联
     */
    FixedScreenMonitorPrisonerDO getFixedScreenMonitorPrisoner(String id);

    void batchSavePrisoner(FixedScreenMonitorDO entity);
}
