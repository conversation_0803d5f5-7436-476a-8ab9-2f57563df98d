package com.rs.module.acp.service.pi;

import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczGywlListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczGywlPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczGywlSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglSqczGywlDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.SqglSqczGywlDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情管理-所情处置关联业务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqglSqczGywlServiceImpl extends BaseServiceImpl<SqglSqczGywlDao, SqglSqczGywlDO> implements SqglSqczGywlService {

    @Resource
    private SqglSqczGywlDao sqglSqczGywlDao;

    @Override
    public String createSqglSqczGywl(SqglSqczGywlSaveReqVO createReqVO) {
        // 插入
        SqglSqczGywlDO sqglSqczGywl = BeanUtils.toBean(createReqVO, SqglSqczGywlDO.class);
        sqglSqczGywlDao.insert(sqglSqczGywl);
        // 返回
        return sqglSqczGywl.getId();
    }

    @Override
    public void updateSqglSqczGywl(SqglSqczGywlSaveReqVO updateReqVO) {
        // 校验存在
        validateSqglSqczGywlExists(updateReqVO.getId());
        // 更新
        SqglSqczGywlDO updateObj = BeanUtils.toBean(updateReqVO, SqglSqczGywlDO.class);
        sqglSqczGywlDao.updateById(updateObj);
    }

    @Override
    public void deleteSqglSqczGywl(String id) {
        // 校验存在
        validateSqglSqczGywlExists(id);
        // 删除
        sqglSqczGywlDao.deleteById(id);
    }

    private void validateSqglSqczGywlExists(String id) {
        if (sqglSqczGywlDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情管理-所情处置关联业务数据不存在");
        }
    }

    @Override
    public SqglSqczGywlDO getSqglSqczGywl(String id) {
        return sqglSqczGywlDao.selectById(id);
    }

    @Override
    public PageResult<SqglSqczGywlDO> getSqglSqczGywlPage(SqglSqczGywlPageReqVO pageReqVO) {
        return sqglSqczGywlDao.selectPage(pageReqVO);
    }

    @Override
    public List<SqglSqczGywlDO> getSqglSqczGywlList(SqglSqczGywlListReqVO listReqVO) {
        return sqglSqczGywlDao.selectList(listReqVO);
    }


}
