package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.CnpFaceDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-仓内外屏人脸信息维护 Service 接口
 *
 * <AUTHOR>
 */
public interface CnpFaceService extends IBaseService<CnpFaceDO>{

    /**
     * 创建实战平台-监管管理-仓内外屏人脸信息维护
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCnpFace(@Valid CnpFaceSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-仓内外屏人脸信息维护
     *
     * @param updateReqVO 更新信息
     */
    void updateCnpFace(@Valid CnpFaceSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-仓内外屏人脸信息维护
     *
     * @param id 编号
     */
    void deleteCnpFace(String id);

    /**
     * 获得实战平台-监管管理-仓内外屏人脸信息维护
     *
     * @param id 编号
     * @return 实战平台-监管管理-仓内外屏人脸信息维护
     */
    CnpFaceDO getCnpFace(String id);

    /**
    * 获得实战平台-监管管理-仓内外屏人脸信息维护分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-仓内外屏人脸信息维护分页
    */
    PageResult<CnpFaceDO> getCnpFacePage(CnpFacePageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-仓内外屏人脸信息维护列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-仓内外屏人脸信息维护列表
    */
    List<CnpFaceDO> getCnpFaceList(CnpFaceListReqVO listReqVO);


}
