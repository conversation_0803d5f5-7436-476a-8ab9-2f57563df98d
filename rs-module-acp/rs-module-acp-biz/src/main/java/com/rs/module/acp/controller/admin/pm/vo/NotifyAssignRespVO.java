package com.rs.module.acp.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.pm.NotifyAssignRecipientDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-通知交办 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class NotifyAssignRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("发布人")
    private String addUserName;
    @ApiModelProperty("发布时间")
    private Date addTime;
    @ApiModelProperty("通知标题")
    private String title;
    @ApiModelProperty("发布类型")
    private String publishType;
    @ApiModelProperty("正文内容")
    private String content;
    @ApiModelProperty("是否全警")
    private Short isFullPolice;
//    @ApiModelProperty("被考核对象类型，01:岗位、02：部门、03：用户")
//    private String handleObjectType;
//    @ApiModelProperty("被考核对象ID")
//    private String handleObjectId;
//    @ApiModelProperty("被考核对象名称")
//    private String handleObjectName;
//    @ApiModelProperty("接收人")
//    private List<NotifyAssignRecipientDO> userList;
    @ApiModelProperty("附件地址")
    private String attUrl;
//    @ApiModelProperty("状态")
//    private String status;
}
