package com.rs.module.acp.controller.app.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.*;
import com.rs.module.acp.controller.app.gj.vo.AppEquipmentUseRespVO;
import com.rs.module.acp.entity.gj.EquipmentUseDO;
import com.rs.module.acp.service.gj.equipment.EquipmentUseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "app端-械具使用审批")
@RestController
@RequestMapping("/app/acp/gj/equipmentUse")
@Validated
public class AppEquipmentUseController {

    @Resource
    private EquipmentUseService equipmentUseService;


    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-械具使用")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AppEquipmentUseRespVO> getEquipmentUse(@RequestParam("id") String id) {
        AppEquipmentUseRespVO equipmentUse = equipmentUseService.getAppEquipmentUse(id);
        return success(equipmentUse);
    }

    @PostMapping("/leaderApproveApply")
    @ApiOperation(value = "领导审批-管教业务-使用呈批审批")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:leaderApproveApply", operateType = LogOperateType.UPDATE, title = "管教业务-戒具使用-领导审批",
            success = "管教业务-戒具使用-领导审批成功", fail = "管教业务-戒具使用-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApproveApply(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        equipmentUseService.leaderApproveApply(approveReqVO);
        return success(true);
    }


    /**
     * 延长所领导审批
     * <AUTHOR>
     * @date 2025/6/5 14:36
     * @return com.rs.framework.common.pojo.CommonResult<java.lang.Boolean>
     */
    @PostMapping("/leaderApproveExtendApply")
    @ApiOperation(value = "领导审批-管教业务-延长所领导审批")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:leaderApproveExtendApply", operateType = LogOperateType.UPDATE, title = "管教业务-戒具使用-领导审批",
            success = "管教业务-戒具使用-领导审批成功", fail = "管教业务-戒具使用-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApproveExtendApply(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        equipmentUseService.leaderApproveExtendApply(approveReqVO);
        return success(true);
    }


    @PostMapping("/leaderApproveRemoveApply")
    @ApiOperation(value = "领导审批-管教业务-提前解除所领导审批")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:leaderApproveExtendApply", operateType = LogOperateType.UPDATE, title = "管教业务-戒具使用-提交解除领导审批",
            success = "管教业务-戒具使用-提前解除-领导审批成功", fail = "管教业务-戒具使用-提前解除-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApproveRemoveApply(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        equipmentUseService.leaderApproveRemoveApply(approveReqVO);
        return success(true);
    }


}
