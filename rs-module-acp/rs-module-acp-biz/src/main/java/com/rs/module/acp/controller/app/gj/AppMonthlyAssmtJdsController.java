package com.rs.module.acp.controller.app.gj;

import cn.hutool.core.lang.Assert;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.MonthlyAssmtJdsConfirmReqVO;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.MonthlyAssmtJdsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.MonthlyAssmtJdsRespVO;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.MonthlyAssmtJdsSaveReqVO;
import com.rs.module.acp.entity.gj.MonthlyAssmtJdsDO;
import com.rs.module.acp.enums.gj.MonthlyAssmtJdsStatus;
import com.rs.module.acp.service.gj.diagnosiassmtjds.MonthlyAssmtJdsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-月度考核(戒毒所)")
@RestController
@RequestMapping("/app/acp/gj/monthlyAssmtJds")
@Validated
public class AppMonthlyAssmtJdsController {

    @Resource
    private MonthlyAssmtJdsService monthlyAssmtJdsService;

    @GetMapping("/records")
    @ApiOperation(value = "App-个人考核记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编号"),
            @ApiImplicitParam(name = "type", value = "01：全部 02：待签收 03：已签收")
    })
    public CommonResult<List<MonthlyAssmtJdsRespVO>> records(@RequestParam("jgrybm") String jgrybm,
                                                             @RequestParam(value = "type", defaultValue = "01") String type) {
        Assert.notEmpty(jgrybm,"监管人员编码不能为空");
        MonthlyAssmtJdsListReqVO listReqVO = new MonthlyAssmtJdsListReqVO();
        listReqVO.setJgrybm(jgrybm);
        if (!"01".equals(type)) {
            listReqVO.setStatus(MonthlyAssmtJdsStatus.getByCode(type).getCode());
        } else {
            listReqVO.setStatusList(new ArrayList<>(Arrays.asList(MonthlyAssmtJdsStatus.DJYQM.getCode(), MonthlyAssmtJdsStatus.YWC.getCode())));
        }
        List<MonthlyAssmtJdsDO> list = monthlyAssmtJdsService.getMonthlyAssmtJdsList(listReqVO);
        return success(BeanUtils.toBean(list, MonthlyAssmtJdsRespVO.class));
    }

    @PostMapping("/confirm")
    @ApiOperation(value = "App-考核确认")
    public CommonResult<Boolean> confirm(@Valid @RequestBody MonthlyAssmtJdsConfirmReqVO confirmReqVO) {
        monthlyAssmtJdsService.confirm(confirmReqVO);
        return success(true);
    }

}
