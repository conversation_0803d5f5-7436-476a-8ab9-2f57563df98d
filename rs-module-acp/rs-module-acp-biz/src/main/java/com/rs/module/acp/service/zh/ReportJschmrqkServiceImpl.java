package com.rs.module.acp.service.zh;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkSaveReqVO;
import com.rs.module.acp.dao.zh.ReportJschmrqkDao;
import com.rs.module.acp.entity.zh.ReportJschmrqkDO;


/**
 * 早850监所晨会每日情况 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReportJschmrqkServiceImpl extends BaseServiceImpl<ReportJschmrqkDao, ReportJschmrqkDO> implements ReportJschmrqkService {

    @Resource
    private ReportJschmrqkDao reportJschmrqkDao;

    @Override
    public String createReportJschmrqk(ReportJschmrqkSaveReqVO createReqVO) {    	
        // 插入
        ReportJschmrqkDO reportJschmrqk = BeanUtils.toBean(createReqVO, ReportJschmrqkDO.class);
        reportJschmrqkDao.insert(reportJschmrqk);
        // 返回
        return reportJschmrqk.getId();
    }

    @Override
    public void updateReportJschmrqk(ReportJschmrqkSaveReqVO updateReqVO) {
        // 校验存在
        validateReportJschmrqkExists(updateReqVO.getId());
        // 更新
        ReportJschmrqkDO updateObj = BeanUtils.toBean(updateReqVO, ReportJschmrqkDO.class);
        reportJschmrqkDao.updateById(updateObj);
    }

    @Override
    public void deleteReportJschmrqk(String id) {
        // 校验存在
        validateReportJschmrqkExists(id);
        // 删除
        reportJschmrqkDao.deleteById(id);
    }

    private void validateReportJschmrqkExists(String id) {
        if (reportJschmrqkDao.selectById(id) == null) {
            throw new ServerException("早850监所晨会每日情况数据不存在");
        }
    }

    @Override
    public ReportJschmrqkDO getReportJschmrqk(String id) {
        return reportJschmrqkDao.selectById(id);
    }

    @Override
    public PageResult<ReportJschmrqkDO> getReportJschmrqkPage(ReportJschmrqkPageReqVO pageReqVO) {
        return reportJschmrqkDao.selectPage(pageReqVO);
    }

    @Override
    public List<ReportJschmrqkDO> getReportJschmrqkList(ReportJschmrqkListReqVO listReqVO) {
        return reportJschmrqkDao.selectList(listReqVO);
    }

    /**
     * 根据报告日期获取晨会报告
     * @param reportDate Date 报告日期
     * @return ReportJschmrqkDO
     */
    @Override
    public ReportJschmrqkDO getByReportDate(Date reportDate) {
    	QueryWrapper<ReportJschmrqkDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("report_date", reportDate);
    	return getOne(wrapper);
    }
    
    /**
     * 获取前24小时统计数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOf24Hours(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getDataOf24Hours(orgCode, reportDate);
    }

    /**
     * 截至目前统计数据
     * @param orgCode String 机构代码
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOfUpToNow(String orgCode) {
    	return reportJschmrqkDao.getDataOfUpToNow(orgCode);
    }
    
    /**
     * 获取前24小时提讯数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOf24Tx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getDataOf24Tx(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时提解数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOf24Tj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getDataOf24Tj(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时律师会见数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOf24Lshj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getDataOf24Lshj(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时收押情况-入所人员数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getSyqk24Rsry(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getSyqk24Rsry(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时收押情况-出所人员数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getSyqk24Csry(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getSyqk24Csry(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-65岁以上人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Above65Years(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Above65Years(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-加戴械具人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Jdxj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Jdxj(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-单独关押
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Ddgy(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Ddgy(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-关注群体人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Gzqtry(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Gzqtry(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-精神异常人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Jsyc(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Jsyc(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-吞食异物人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Tsyw(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Tsyw(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-特殊身份
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Tssf(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Tssf(orgCode, reportDate);
    }
    
    /**
     * 外籍人员管控情况-国籍分布情况
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getWjrygkGjfb(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getWjrygkGjfb(orgCode, reportDate);
    }
    
    /**
     * 外籍人员管控情况-涉嫌犯罪类型
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getWjrygkSxfzlx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getWjrygkSxfzlx(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-提讯
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Tx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Tx(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-提解
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Tj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Tj(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-律师接待
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Lsjd(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Lsjd(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-接待家属
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Jdjs(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Jdjs(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-交付执行
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Jfzx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Jfzx(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Sghj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Sghj(orgCode, reportDate);
    }
    
    /**
     * 重点风险人员管控情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public Map<String, Object> getZdfxry24Sjfx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZdfxry24Sjfx(orgCode, reportDate);
    }
    
    /**
     * 重点风险人员管控情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZdfxry24Zyqk(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZdfxry24Zyqk(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-巡诊发药
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public Integer getJcsj24Xzfy(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Xzfy(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-所内治疗
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public Integer getJcsj24Snzl(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Snzl(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-以绝食、拒绝医疗等方式对抗监管情况
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public Integer getJcsj24Dkjg(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Dkjg(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-一级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getJcsj24Yj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Yj(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-二级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getJcsj24Ej(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Ej(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-三级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getJcsj24Sj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Sj(orgCode, reportDate);
    }
}
