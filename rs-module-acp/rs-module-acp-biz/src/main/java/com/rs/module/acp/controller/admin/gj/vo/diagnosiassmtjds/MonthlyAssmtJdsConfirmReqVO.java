package com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-月度考核(戒毒所)新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MonthlyAssmtJdsConfirmReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("在押人员签名地址")
    @NotEmpty(message = "在押人员签名地址不能为空")
    private String prisonerSignature;


}
