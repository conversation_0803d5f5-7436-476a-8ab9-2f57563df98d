package com.rs.module.acp.controller.app.gj.vo;

import com.alibaba.fastjson.JSONObject;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-监室监控-app列表 Request VO")
@Data
public class RoomMonitorAppListVO{

    private static final long serialVersionUID = 1L;

    /*@ApiModelProperty("一级风险人员")
    private List<RoomMonitorAppVO> fxdjOneList;
    @ApiModelProperty("一级风险人员数量")
    private Integer fxdjOneCount;
    @ApiModelProperty("二级风险人员")
    private List<RoomMonitorAppVO> fxdjTwoList;
    @ApiModelProperty("二级风险人员数量")
    private Integer fxdjTwoCount;

    @ApiModelProperty("三级风险人员")
    private List<RoomMonitorAppVO> fxdjOThreeList;
    @ApiModelProperty("三级风险人员数量")
    private Integer fxdjThreeCount;

    @ApiModelProperty("加戴戒具人员")
    private List<RoomMonitorAppVO> jdjjList;
    @ApiModelProperty("加戴戒具人员数量")
    private Integer jdjjCount;

    @ApiModelProperty("新入所人员")
    private List<RoomMonitorAppVO> xrsList;
    @ApiModelProperty("新入所人员数量")
    private Integer xrsCount;*/
    @ApiModelProperty("人员类型及人员集合")
    private List<RoomMonitorAppVOInfo> personTypeList;
    @ApiModelProperty("当前时间值班信息")
    private JSONObject dutyResult;
    @ApiModelProperty("当前一日生活制度")
    private JSONObject lifetEvent;
    @Data
    public static class RoomMonitorAppVOInfo{

        @ApiModelProperty("人员集合")
        private List<RoomMonitorAppVO> list;
        @ApiModelProperty("人员数量")
        private Integer count;
        @ApiModelProperty("人员类型标题")
        private String title;

        public RoomMonitorAppVOInfo(String title,Integer count,List<RoomMonitorAppVO> list) {
            this.list = list;
            this.count = count;
            this.title = title;
        }
    }
}
