package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-模板配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_mbpz")
@KeySequence("acp_pi_sqgl_mbpz_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_mbpz")
public class SqglMbpzDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所请来源，字典：
     */
    private String eventSrc;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 所情类型配置,存JSON
     */
    private String eventTypeSettings;
    /**
     * 处置情况模板,存JSON
     */
    private String handlingSituationTemplate;
    /**
     * 推送对象配置，存JSON
     */
    private String pushObjectSettings;
    /**
     * 告警类型
     */
    private String alarmType;
    /**
     * 是否启用
     */
    private Short isEnabled;

    /**
     * 模板编号
     */
    private String templateCode;
}
