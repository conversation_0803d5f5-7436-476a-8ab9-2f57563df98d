package com.rs.module.acp.controller.admin.db;

import com.rs.module.acp.controller.admin.db.vo.zyjwzx.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.db.ZyjwzxDO;
import com.rs.module.acp.service.db.ZyjwzxService;

@Api(tags = "实战平台-收押业务-暂予监外执行")
@RestController
@RequestMapping("/acp/db/zyjwzx")
@Validated
public class ZyjwzxController {

    @Resource
    private ZyjwzxService zyjwzxService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-暂予监外执行")
    public CommonResult<String> createZyjwzx(@Valid @RequestBody ZyjwzxSaveReqVO createReqVO) {
        return success(zyjwzxService.createZyjwzx(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-暂予监外执行")
    public CommonResult<Boolean> updateZyjwzx(@Valid @RequestBody ZyjwzxSaveReqVO updateReqVO) {
        zyjwzxService.updateZyjwzx(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-暂予监外执行")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteZyjwzx(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           zyjwzxService.deleteZyjwzx(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-暂予监外执行")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ZyjwzxRespVO> getZyjwzx(@RequestParam("id") String id) {
        return success(zyjwzxService.getZyjwzx(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-暂予监外执行分页")
    public CommonResult<PageResult<ZyjwzxRespVO>> getZyjwzxPage(@Valid @RequestBody ZyjwzxPageReqVO pageReqVO) {
        PageResult<ZyjwzxDO> pageResult = zyjwzxService.getZyjwzxPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ZyjwzxRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-暂予监外执行列表")
    public CommonResult<List<ZyjwzxRespVO>> getZyjwzxList(@Valid @RequestBody ZyjwzxListReqVO listReqVO) {
        List<ZyjwzxDO> list = zyjwzxService.getZyjwzxList(listReqVO);
        return success(BeanUtils.toBean(list, ZyjwzxRespVO.class));
    }

    @PostMapping("/dj")
    @ApiOperation(value = "登记")
    public CommonResult<String> dj(@Valid @RequestBody ZyjwzxDjReqVO djReqVO) {
        return success(zyjwzxService.dj(djReqVO));
    }

    @PostMapping("/swhcs")
    @ApiOperation(value = "所务会初审")
    public CommonResult<Boolean> swhcs(@Valid @RequestBody ZyjwzxSwhcsReqVO swhcsReqVO) {
        zyjwzxService.swhcs(swhcsReqVO);
        return success(true);
    }

    @PostMapping("/zdjdjc")
    @ApiOperation(value = "诊断鉴定检查")
    public CommonResult<Boolean> zdjdjc(@Valid @RequestBody ZyjwzxZdjdjcReqVO zdjdjcReqVO) {
        zyjwzxService.zdjdjc(zdjdjcReqVO);
        return success(true);
    }

    @PostMapping("/bwjycx")
    @ApiOperation(value = "保外就医程序")
    public CommonResult<Boolean> bwjycx(@Valid @RequestBody ZyjwzxBwjycxReqVO bwjycxReqVO) {
        zyjwzxService.bwjycx(bwjycxReqVO);
        return success(true);
    }

    @PostMapping("/hsjzd")
    @ApiOperation(value = "核实居住地")
    public CommonResult<Boolean> hsjzd(@Valid @RequestBody ZyjwzxHsjzdReqVO hsjzdReqVO) {
        zyjwzxService.hsjzd(hsjzdReqVO);
        return success(true);
    }

    @PostMapping("/swhsy")
    @ApiOperation(value = "所务会审议")
    public CommonResult<Boolean> swhsy(@Valid @RequestBody ZyjwzxSwhsyReqVO swhsyReqVO) {
        zyjwzxService.swhsy(swhsyReqVO);
        return success(true);
    }

    @PostMapping("/sngs")
    @ApiOperation(value = "所内公示")
    public CommonResult<Boolean> sngs(@Valid @RequestBody ZyjwzxSngsReqVO sngsReqVO) {
        zyjwzxService.sngs(sngsReqVO);
        return success(true);
    }

    @PostMapping("/tzsh")
    @ApiOperation(value = "听证审核")
    public CommonResult<Boolean> tzsh(@Valid @RequestBody ZyjwzxTzshReqVO tzshReqVO) {
        zyjwzxService.tzsh(tzshReqVO);
        return success(true);
    }

    @PostMapping("/zsjcsjd")
    @ApiOperation(value = "驻所检察室监督")
    public CommonResult<Boolean> zsjcsjd(@Valid @RequestBody ZyjwzxZsjcsjdReqVO zsjcsjdReqVO) {
        zyjwzxService.zsjcsjd(zsjcsjdReqVO);
        return success(true);
    }

    @PostMapping("/gajggs")
    @ApiOperation(value = "公安机关公示")
    public CommonResult<Boolean> gajggs(@Valid @RequestBody ZyjwzxGajggsReqVO gajggsReqVO) {
        zyjwzxService.gajggs(gajggsReqVO);
        return success(true);
    }

    @PostMapping("/gajgsp")
    @ApiOperation(value = "公安机关审批")
    public CommonResult<Boolean> gajgsp(@Valid @RequestBody ZyjwzxGajgspReqVO gajgspReqVO) {
        zyjwzxService.gajgsp(gajgspReqVO);
        return success(true);
    }

    @PostMapping("/zfjfzx")
    @ApiOperation(value = "罪犯交付执行")
    public CommonResult<Boolean> zfjfzx(@Valid @RequestBody ZyjwzxZfjfzxReqVO zfjfzxReqVO) {
        zyjwzxService.zfjfzx(zfjfzxReqVO);
        return success(true);
    }

}
