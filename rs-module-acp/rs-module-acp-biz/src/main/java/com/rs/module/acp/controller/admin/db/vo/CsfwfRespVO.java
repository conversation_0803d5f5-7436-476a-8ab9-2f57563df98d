package com.rs.module.acp.controller.admin.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-出所防误放 Response VO")
@Data
public class CsfwfRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监室ID")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("业务类型（1：出所、2：入所）")
    private String businessType;
    @ApiModelProperty("业务原因")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SFCSCSYY")
    private String businessReason;
    @ApiModelProperty("对比方式")
    private String verificationMode;
    @ApiModelProperty("核验结果（01：待核验，02：验证成功，03：验证失败）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYYW_FWF_HYJG")
    private String checkResult;
    @ApiModelProperty("申请时间")
    private Date operateTime;
    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;
    @ApiModelProperty("经办民警")
    private String operatePolice;
    @ApiModelProperty("强制类型（1：强制带出，2：强制带入）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYYW_FWF_QZLX")
    private String mandatoryType;
    @ApiModelProperty("强制原因")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYYW_FWF_QZYY")
    private String mandatoryReason;
    @ApiModelProperty("性别")
    private String xb;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("被监管人员ID")
    private String prisonerId;
    @ApiModelProperty("关联业务ID")
    private String businessId;

}
