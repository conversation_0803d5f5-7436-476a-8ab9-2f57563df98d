package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczGywlListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczGywlPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczGywlSaveReqVO;
import com.rs.module.acp.entity.pi.SqglSqczGywlDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情管理-所情处置关联业务 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglSqczGywlService extends IBaseService<SqglSqczGywlDO>{

    /**
     * 创建实战平台-巡视管控-所情管理-所情处置关联业务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSqglSqczGywl(@Valid SqglSqczGywlSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情管理-所情处置关联业务
     *
     * @param updateReqVO 更新信息
     */
    void updateSqglSqczGywl(@Valid SqglSqczGywlSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情管理-所情处置关联业务
     *
     * @param id 编号
     */
    void deleteSqglSqczGywl(String id);

    /**
     * 获得实战平台-巡视管控-所情管理-所情处置关联业务
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-所情处置关联业务
     */
    SqglSqczGywlDO getSqglSqczGywl(String id);

    /**
    * 获得实战平台-巡视管控-所情管理-所情处置关联业务分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情管理-所情处置关联业务分页
    */
    PageResult<SqglSqczGywlDO> getSqglSqczGywlPage(SqglSqczGywlPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情管理-所情处置关联业务列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情管理-所情处置关联业务列表
    */
    List<SqglSqczGywlDO> getSqglSqczGywlList(SqglSqczGywlListReqVO listReqVO);


}
