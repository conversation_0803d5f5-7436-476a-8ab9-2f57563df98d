package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情登记推送对象分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SqglSqdjtsdxPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所情登记ID")
    private String sqdjId;

    @ApiModelProperty("处置人身份证号")
    private String pushUserSfzh;

    @ApiModelProperty("推送人名称")
    private String pushUserName;

    @ApiModelProperty("推送时间")
    private Date[] pushTime;

    @ApiModelProperty("推送人所属岗位名称")
    private String pushPostName;

    @ApiModelProperty("推送岗位编号")
    private String pushPostCode;

    @ApiModelProperty("所情处置ID")
    private Object sqczId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
