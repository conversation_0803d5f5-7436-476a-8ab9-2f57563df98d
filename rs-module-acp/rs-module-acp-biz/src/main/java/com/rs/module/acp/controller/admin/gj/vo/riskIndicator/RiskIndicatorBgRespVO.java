package com.rs.module.acp.controller.admin.gj.vo.riskIndicator;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险指标 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskIndicatorBgRespVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("项-编码")
    private String itemCode;

    @ApiModelProperty("项-名称")
    private String itemName;

    @ApiModelProperty("完整报告时，每一项末尾数字")
    private long count;

    @ApiModelProperty("指标集合")
    private CopyOnWriteArrayList<RiskIndicatorSubBgRespVO> indicatorList;


}
