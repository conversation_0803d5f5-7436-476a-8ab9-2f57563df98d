package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.MonthlyAssmtJdsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.MonthlyAssmtJdsPageReqVO;
import com.rs.module.acp.entity.gj.MonthlyAssmtJdsDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-月度考核(戒毒所) Dao
*
* <AUTHOR>
*/
@Mapper
public interface MonthlyAssmtJdsDao extends IBaseDao<MonthlyAssmtJdsDO> {


    default PageResult<MonthlyAssmtJdsDO> selectPage(MonthlyAssmtJdsPageReqVO reqVO) {
        Page<MonthlyAssmtJdsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MonthlyAssmtJdsDO> wrapper = new LambdaQueryWrapperX<MonthlyAssmtJdsDO>()
            .eqIfPresent(MonthlyAssmtJdsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MonthlyAssmtJdsDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(MonthlyAssmtJdsDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(MonthlyAssmtJdsDO::getRoomName, reqVO.getRoomName())
            .betweenIfPresent(MonthlyAssmtJdsDO::getPushTime, reqVO.getPushTime())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue1, reqVO.getAssmtContentValue1())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue2, reqVO.getAssmtContentValue2())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue3, reqVO.getAssmtContentValue3())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue4, reqVO.getAssmtContentValue4())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue5, reqVO.getAssmtContentValue5())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue6, reqVO.getAssmtContentValue6())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue7, reqVO.getAssmtContentValue7())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue8, reqVO.getAssmtContentValue8())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue9, reqVO.getAssmtContentValue9())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentValue10, reqVO.getAssmtContentValue10())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentTotalScore, reqVO.getAssmtContentTotalScore())
            .eqIfPresent(MonthlyAssmtJdsDO::getBonusOrPenaltyPointsSituati, reqVO.getBonusOrPenaltyPointsSituati())
            .eqIfPresent(MonthlyAssmtJdsDO::getAsstmContentJson, reqVO.getAsstmContentJson())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtUserSfzh, reqVO.getAssmtUserSfzh())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtUser, reqVO.getAssmtUser())
            .betweenIfPresent(MonthlyAssmtJdsDO::getAssmtTime, reqVO.getAssmtTime())
            .eqIfPresent(MonthlyAssmtJdsDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(MonthlyAssmtJdsDO::getConfirmTime, reqVO.getConfirmTime())
            .eqIfPresent(MonthlyAssmtJdsDO::getPrisonerSignature, reqVO.getPrisonerSignature())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MonthlyAssmtJdsDO::getAddTime);
        }
        Page<MonthlyAssmtJdsDO> monthlyAssmtJdsPage = selectPage(page, wrapper);
        return new PageResult<>(monthlyAssmtJdsPage.getRecords(), monthlyAssmtJdsPage.getTotal());
    }
    default List<MonthlyAssmtJdsDO> selectList(MonthlyAssmtJdsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MonthlyAssmtJdsDO>()
            .eqIfPresent(MonthlyAssmtJdsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MonthlyAssmtJdsDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(MonthlyAssmtJdsDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(MonthlyAssmtJdsDO::getRoomName, reqVO.getRoomName())
            .betweenIfPresent(MonthlyAssmtJdsDO::getPushTime, reqVO.getPushTime())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtContentTotalScore, reqVO.getAssmtContentTotalScore())
            .eqIfPresent(MonthlyAssmtJdsDO::getBonusOrPenaltyPointsSituati, reqVO.getBonusOrPenaltyPointsSituati())
            .eqIfPresent(MonthlyAssmtJdsDO::getAsstmContentJson, reqVO.getAsstmContentJson())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtUserSfzh, reqVO.getAssmtUserSfzh())
            .eqIfPresent(MonthlyAssmtJdsDO::getAssmtUser, reqVO.getAssmtUser())
            .betweenIfPresent(MonthlyAssmtJdsDO::getAssmtTime, reqVO.getAssmtTime())
            .eqIfPresent(MonthlyAssmtJdsDO::getStatus, reqVO.getStatus())
            .inIfPresent(MonthlyAssmtJdsDO::getStatus, reqVO.getStatusList())
            .betweenIfPresent(MonthlyAssmtJdsDO::getConfirmTime, reqVO.getConfirmTime())
            .eqIfPresent(MonthlyAssmtJdsDO::getPrisonerSignature, reqVO.getPrisonerSignature())
        .orderByDesc(MonthlyAssmtJdsDO::getAddTime));    }


    List<MonthlyAssmtJdsDO> getMonthlyAssmtJdsListByInJds();

}
