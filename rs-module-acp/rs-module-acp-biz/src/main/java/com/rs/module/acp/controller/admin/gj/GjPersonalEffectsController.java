package com.rs.module.acp.controller.admin.gj;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.gj.vo.*;
import com.rs.module.acp.entity.gj.GjPersonalEffectsDO;
import com.rs.module.acp.service.gj.GjPersonalEffectsService;

@Api(tags = "实战平台-管教业务-随身物品登记")
@RestController
@RequestMapping("/acp/gj/personal/effects")
@Validated
public class GjPersonalEffectsController {

    @Resource
    private GjPersonalEffectsService gjPersonalEffectsService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-管教业务-随身物品登记-创建")
    public CommonResult<String> createGjPersonalEffects(@Valid @RequestBody GjPersonalEffectsSaveReqVO createReqVO) {
        return success(gjPersonalEffectsService.createGjPersonalEffects(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-管教业务-随身物品登记-更新")
    public CommonResult<Boolean> updateGjPersonalEffects(@Valid @RequestBody GjPersonalEffectsSaveReqVO updateReqVO) {
        gjPersonalEffectsService.updateGjPersonalEffects(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-管教业务-随身物品登记-删除")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteGjPersonalEffects(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           gjPersonalEffectsService.deleteGjPersonalEffects(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-管教业务-随身物品登记-获取")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<GjPersonalEffectsRespVO> getGjPersonalEffects(@RequestParam("id") String id) {
        GjPersonalEffectsDO gjPersonalEffects = gjPersonalEffectsService.getGjPersonalEffects(id);
        return success(BeanUtils.toBean(gjPersonalEffects, GjPersonalEffectsRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "实战平台-管教业务-随身物品登记分页")
    public CommonResult<PageResult<GjPersonalEffectsRespVO>> getGjPersonalEffectsPage(@Valid @RequestBody GjPersonalEffectsPageReqVO pageReqVO) {
        PageResult<GjPersonalEffectsDO> pageResult = gjPersonalEffectsService.getGjPersonalEffectsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GjPersonalEffectsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "实战平台-管教业务-随身物品登记列表")
    public CommonResult<List<GjPersonalEffectsRespVO>> getGjPersonalEffectsList(@Valid @RequestBody GjPersonalEffectsListReqVO listReqVO) {
        List<GjPersonalEffectsDO> list = gjPersonalEffectsService.getGjPersonalEffectsList(listReqVO);
        return success(BeanUtils.toBean(list, GjPersonalEffectsRespVO.class));
    }
}
