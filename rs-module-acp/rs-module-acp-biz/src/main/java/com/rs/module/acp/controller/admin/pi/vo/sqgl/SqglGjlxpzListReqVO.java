package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-告警类型配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglGjlxpzListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所请来源，字典：")
    private String eventSrc;

    @ApiModelProperty("告警类型，字典：")
    private String alarmType;

    @ApiModelProperty("所情等级，字典：")
    private String eventLevel;

    @ApiModelProperty("处理时效（分钟）")
    private Short processingDuration;

    @ApiModelProperty("排序号")
    private Integer orderId;

}
