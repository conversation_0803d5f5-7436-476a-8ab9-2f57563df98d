package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-告警类型配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_gjlxpz")
@KeySequence("acp_pi_sqgl_gjlxpz_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_gjlxpz")
public class SqglGjlxpzDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所请来源，字典：
     */
    private String eventSrc;
    /**
     * 告警类型，字典：
     */
    private String alarmType;
    /**
     * 所情等级，字典：
     */
    private String eventLevel;
    /**
     * 处理时效（分钟）
     */
    private Short processingDuration;
    /**
     * 排序号
     */
    private Integer orderId;
    /**
     * 是否启用
     */
    private Short isEnabled;

}
