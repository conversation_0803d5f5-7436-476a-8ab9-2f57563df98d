package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-告警类型配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglGjlxpzSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所请来源，字典：ZD_JJKS_SQLY")
    @NotEmpty(message = "所请来源，不能为空")
    private String eventSrc;

    @ApiModelProperty("告警类型，字典：ZD_JJKS_GJLX")
    @NotEmpty(message = "告警类型，不能为空")
    private String alarmType;

    @ApiModelProperty("所情等级，字典：ZD_JJKS_SQDJ")
    private String eventLevel;

    @ApiModelProperty("处理时效（分钟）")
    private Short processingDuration;

    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("是否启用（0：否，1：是）")
    private Short isEnabled;
}
