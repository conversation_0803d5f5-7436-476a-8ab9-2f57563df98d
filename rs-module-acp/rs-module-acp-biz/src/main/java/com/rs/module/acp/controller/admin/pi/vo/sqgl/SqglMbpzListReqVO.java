package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-模板配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglMbpzListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所请来源，字典：")
    private String eventSrc;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("所属单位编码")
    private String orgCode;


}
