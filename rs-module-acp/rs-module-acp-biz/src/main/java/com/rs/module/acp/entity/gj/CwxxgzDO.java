package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-监管管理-错误信息更正 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_cwxxgz")
@KeySequence("acp_gj_cwxxgz_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_cwxxgz")
public class CwxxgzDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监管人员身份证号
     */
    private String jgrysfzh;
    /**
     * 数据类型(01:推送,02:接收)
     */
    private String type;
    /**
     * 错误项
     */
    private String cwx;
    /**
     * 错误明细
     */
    private String cwmx;
    /**
     * 登记人身份证号
     */
    private String djrsfzh;
    /**
     * 登记人姓名
     */
    private String djrxm;
    /**
     * 登记人联系方式
     */
    private String djrlxdh;
    /**
     * 登记时间
     */
    private Date djsj;
    /**
     * 接收时间
     */
    private Date jssj;
    /**
     * 状态
     */
    private String status;
    /**
     * 附件地址
     */
    private String attUrl;

}
