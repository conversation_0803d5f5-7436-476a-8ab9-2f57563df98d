package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxGajggsReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("是否公安网站公示（1、是；2、否）")
    private String sfgawzgs;

    @ApiModelProperty("公安机关在网站公示起始日期")
    private Date gajgzwzgsqsrq;

    @ApiModelProperty("公安机关在网站公示截止日期")
    private Date gajgzwzgsjzrq;

    @ApiModelProperty("公安机关公示是否收到异议（1、是；2、否）")
    private String gajggssfsdyy;

    @ApiModelProperty("回复异议日期")
    private Date hfyyrq;

    @ApiModelProperty("回复异议情况")
    private String hfyyqk;

    @ApiModelProperty("公安机关在网站公示备注")
    private String gajgzwzgsbz;

    @ApiModelProperty("公安机关在网站公示材料")
    private String gajgzwzgscl;

    @ApiModelProperty("公示单位名称")
    private String gsdwmc;

    @ApiModelProperty("公示网站名称")
    private String gswzmc;

}
