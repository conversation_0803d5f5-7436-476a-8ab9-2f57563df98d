package com.rs.module.acp.service.gj.face2face;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFacePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceSaveReqVO;
import com.rs.module.acp.entity.gj.FaceToFaceDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.job.facetoface.bo.FaceToFaceBO;

/**
 * 实战平台-管教业务-面对面管理 Service 接口
 *
 * <AUTHOR>
 */
public interface FaceToFaceService extends IBaseService<FaceToFaceDO>{

    /**
     * 创建实战平台-管教业务-面对面管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFaceToFace(@Valid FaceToFaceSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-面对面管理
     *
     * @param updateReqVO 更新信息
     */
    void updateFaceToFace(@Valid FaceToFaceSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-面对面管理
     *
     * @param id 编号
     */
    void deleteFaceToFace(String id);

    /**
     * 获得实战平台-管教业务-面对面管理
     *
     * @param id 编号
     * @return 实战平台-管教业务-面对面管理
     */
    FaceToFaceDO getFaceToFace(String id);

    /**
    * 获得实战平台-管教业务-面对面管理分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-面对面管理分页
    */
    PageResult<FaceToFaceDO> getFaceToFacePage(FaceToFacePageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-面对面管理列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-面对面管理列表
    */
    List<FaceToFaceDO> getFaceToFaceList(FaceToFaceListReqVO listReqVO);


    List<FaceToFaceBO> getNeedRemindRoomId(List<String> excludeListOrg);

}
