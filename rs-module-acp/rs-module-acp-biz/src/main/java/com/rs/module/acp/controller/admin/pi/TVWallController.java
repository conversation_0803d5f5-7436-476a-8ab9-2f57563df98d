package com.rs.module.acp.controller.admin.pi;

import com.rs.third.api.component.haikang.HaiKangTVWallControlComponent;
import com.rs.third.api.dto.haikang.tvwall.*;
import com.rs.third.api.model.tvwall.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;


@Api(tags = "实战平台-巡视管控-高风险人员定屏监控测试")
@RestController
@RequestMapping("/acp/pi/tvwall")
@Validated
public class TVWallController {
    @Resource
    private HaiKangTVWallControlComponent tvWallControlComponent;

    // =============== 电视墙接口方法 ===============

    @GetMapping("/getAllResources")
    @ApiOperation("获取电视墙大屏信息")
    public HaiKangApiResponse<TvWallAllResourcesDTO> getAllResources() {
        try {
            return tvWallControlComponent.getAllResources();
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "获取电视墙大屏信息失败: " + e.getMessage(), null);
        }
    }

    @GetMapping("/getScenes")
    @ApiOperation("获取电视墙场景列表信息")
    public HaiKangApiResponse<TvWallScenesDTO> getScenes() {
        try {
            return tvWallControlComponent.getScenes();
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "获取电视墙场景列表失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/getWindowList")
    @ApiOperation("获取电视墙窗口信息列表")
    public HaiKangApiResponse<TvWallWndsDTO> getWindowList(@RequestBody TvWallWndsReq req) {
        try {
            return tvWallControlComponent.getWindowList(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "获取电视墙窗口信息列表失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/batchAddToWall")
    @ApiOperation("批量上墙")
    public HaiKangApiResponse<TvWallRealPlayDTO> batchAddToWall(@RequestBody TvWallRealPlayReq req) {
        try {
            return tvWallControlComponent.batchAddToWall(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "批量上墙失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/batchRemoveFromWall")
    @ApiOperation("批量下墙")
    public HaiKangApiResponse<TvWallRealPlayDTO> batchRemoveFromWall(@RequestBody TvWallRealPlayReq req) {
        try {
            return tvWallControlComponent.batchRemoveFromWall(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "批量下墙失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/createScene")
    @ApiOperation("场景创建")
    public HaiKangApiResponse<TvWallSceneDTO> createScene(@RequestBody TvWallSceneCreateReq req) {
        try {
            return tvWallControlComponent.createScene(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "创建场景失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/updateScene")
    @ApiOperation("场景修改")
    public HaiKangApiResponse<TvWallSceneDTO> updateScene(@RequestBody TvWallSceneUpdateReq req) {
        try {
            return tvWallControlComponent.updateScene(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "修改场景失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/deleteScene")
    @ApiOperation("场景删除")
    public HaiKangApiResponse<TvWallSceneDeleteDTO> deleteScene(@RequestBody TvWallSceneDeleteReq req) {
        try {
            return tvWallControlComponent.deleteScene(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "删除场景失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/saveAsScene")
    @ApiOperation("场景另存为")
    public HaiKangApiResponse<TvWallSceneDTO> saveAsScene(@RequestBody TvWallSceneSaveAsReq req) {
        try {
            return tvWallControlComponent.saveAsScene(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "场景另存为失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/switchScene")
    @ApiOperation("电视墙场景切换")
    public HaiKangApiResponse<TvWallSceneSwitchDTO> switchScene(@RequestBody TvWallSceneSwitchReq req) {
        try {
            return tvWallControlComponent.switchScene(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "切换场景失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/addAlarm")
    @ApiOperation("新增报警")
    public HaiKangApiResponse<TvWallAlarmDTO> addAlarm(@RequestBody TvWallAlarmAddReq req) {
        try {
            return tvWallControlComponent.addAlarm(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "新增报警失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/deleteAlarm")
    @ApiOperation("删除报警")
    public HaiKangApiResponse<TvWallAlarmDeleteDTO> deleteAlarm(@RequestBody TvWallAlarmDeleteReq req) {
        try {
            return tvWallControlComponent.deleteAlarm(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "删除报警失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/divideWindow")
    @ApiOperation("窗口分割")
    public HaiKangApiResponse<TvWallFloatWndDivisionDTO> divideWindow(@RequestBody TvWallFloatWndDivisionReq req) {
        try {
            return tvWallControlComponent.divideWindow(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "窗口分割失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/batchCreateWindows")
    @ApiOperation("窗口批量创建")
    public HaiKangApiResponse<TvWallFloatWndsDTO> batchCreateWindows(@RequestBody TvWallFloatWndsCreateReq req) {
        try {
            return tvWallControlComponent.batchCreateWindows(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "批量创建窗口失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/batchDeleteWindows")
    @ApiOperation("窗口批量删除")
    public HaiKangApiResponse<TvWallFloatWndsDeleteDTO> batchDeleteWindows(@RequestBody TvWallFloatWndsDeleteReq req) {
        try {
            return tvWallControlComponent.batchDeleteWindows(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "批量删除窗口失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/zoomInWindow")
    @ApiOperation("窗口放大")
    public HaiKangApiResponse<TvWallFloatWndZoomDTO> zoomInWindow(@RequestBody TvWallFloatWndZoomReq req) {
        try {
            return tvWallControlComponent.zoomInWindow(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1","窗口放大失败: " + e.getMessage(), null);
        }
    }
    @PostMapping("/moveWindow")
    @ApiOperation("窗口漫游")
    public HaiKangApiResponse<TvWallFloatWndMoveDTO> moveWindow(@RequestBody TvWallFloatWndMoveReq req) {
        try {
            return tvWallControlComponent.moveWindow(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "窗口漫游失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/setWindowLayer")
    @ApiOperation("窗口置顶或置底")
    public HaiKangApiResponse<TvWallFloatWndLayerCtrlDTO> setWindowLayer(@RequestBody TvWallFloatWndLayerCtrlReq req) {
        try {
            return tvWallControlComponent.setWindowLayer(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "窗口置顶/置底失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/zoomOutWindow")
    @ApiOperation("窗口还原")
    public HaiKangApiResponse<TvWallFloatWndZoomDTO> zoomOutWindow(@RequestBody TvWallFloatWndZoomOutReq req) {
        try {
            return tvWallControlComponent.zoomOutWindow(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "窗口还原失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/divideMonitor")
    @ApiOperation("非开窗设备窗口分割")
    public HaiKangApiResponse<TvWallMonitorDivisionDTO> divideMonitor(@RequestBody TvWallMonitorDivisionReq req) {
        try {
            return tvWallControlComponent.divideMonitor(req);
        } catch (Exception e) {
            return new HaiKangApiResponse("1", "非开窗设备窗口分割失败: " + e.getMessage(), null);
        }
    }

}
