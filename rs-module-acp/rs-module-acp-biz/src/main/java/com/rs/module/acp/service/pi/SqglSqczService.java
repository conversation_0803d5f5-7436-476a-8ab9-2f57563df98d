package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczSaveReqVO;
import com.rs.module.acp.entity.pi.SqglSqczDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情管理-所情处置 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglSqczService extends IBaseService<SqglSqczDO>{

    /**
     * 创建实战平台-巡视管控-所情管理-所情处置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSqglSqcz(@Valid SqglSqczSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情管理-所情处置
     *
     * @param updateReqVO 更新信息
     */
    void updateSqglSqcz(@Valid SqglSqczSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情管理-所情处置
     *
     * @param id 编号
     */
    void deleteSqglSqcz(String id);

    /**
     * 获得实战平台-巡视管控-所情管理-所情处置
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-所情处置
     */
    SqglSqczDO getSqglSqcz(String id);

    /**
    * 获得实战平台-巡视管控-所情管理-所情处置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情管理-所情处置分页
    */
    PageResult<SqglSqczDO> getSqglSqczPage(SqglSqczPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情管理-所情处置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情管理-所情处置列表
    */
    List<SqglSqczDO> getSqglSqczList(SqglSqczListReqVO listReqVO);


}
