package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglGjlxpzListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglGjlxpzPageReqVO;
import com.rs.module.acp.entity.pi.SqglGjlxpzDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情管理-告警类型配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SqglGjlxpzDao extends IBaseDao<SqglGjlxpzDO> {


    default PageResult<SqglGjlxpzDO> selectPage(SqglGjlxpzPageReqVO reqVO) {
        Page<SqglGjlxpzDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SqglGjlxpzDO> wrapper = new LambdaQueryWrapperX<SqglGjlxpzDO>()
            .eqIfPresent(SqglGjlxpzDO::getEventSrc, reqVO.getEventSrc())
            .eqIfPresent(SqglGjlxpzDO::getAlarmType, reqVO.getAlarmType())
            .eqIfPresent(SqglGjlxpzDO::getEventLevel, reqVO.getEventLevel())
            .eqIfPresent(SqglGjlxpzDO::getProcessingDuration, reqVO.getProcessingDuration())
            .eqIfPresent(SqglGjlxpzDO::getOrderId, reqVO.getOrderId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SqglGjlxpzDO::getAddTime);
        }
        Page<SqglGjlxpzDO> sqglGjlxpzPage = selectPage(page, wrapper);
        return new PageResult<>(sqglGjlxpzPage.getRecords(), sqglGjlxpzPage.getTotal());
    }
    default List<SqglGjlxpzDO> selectList(SqglGjlxpzListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SqglGjlxpzDO>()
            .eqIfPresent(SqglGjlxpzDO::getEventSrc, reqVO.getEventSrc())
            .eqIfPresent(SqglGjlxpzDO::getAlarmType, reqVO.getAlarmType())
            .eqIfPresent(SqglGjlxpzDO::getEventLevel, reqVO.getEventLevel())
            .eqIfPresent(SqglGjlxpzDO::getProcessingDuration, reqVO.getProcessingDuration())
            .eqIfPresent(SqglGjlxpzDO::getOrderId, reqVO.getOrderId())
        .orderByDesc(SqglGjlxpzDO::getAddTime));    }


    }
