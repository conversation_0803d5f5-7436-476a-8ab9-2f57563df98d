package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-模板配置推送对象新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglMbpzTsdxSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;


    @ApiModelProperty("推送岗位编码")
    private String disposePost;

    @ApiModelProperty("推送岗位名称")
    private String disposePostName;

    @ApiModelProperty("管理业务，多个用逗号隔开，字典：ZD_JJKS_GLYW")
    private String disposeBusiness;

    @ApiModelProperty("处置预案")
    private String disposePlans;

    @ApiModelProperty("排序")
    private Integer orderId;
}
