package com.rs.module.acp.controller.admin.gj.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.vo.FileReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-错误信息更正 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CwxxgzRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监管人员身份证号")
    private String jgrysfzh;
    @ApiModelProperty("数据类型(01:推送,02:接收)")
    private String type;
    @ApiModelProperty("错误项")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_CWXXBGX")
    private String cwx;
    @ApiModelProperty("错误明细")
    private String cwmx;
    @ApiModelProperty("登记人身份证号")
    private String djrsfzh;
    @ApiModelProperty("登记人姓名")
    private String djrxm;
    @ApiModelProperty("登记人联系方式")
    private String djrlxdh;
    @ApiModelProperty("登记时间")
    private Date djsj;
    @ApiModelProperty("接收时间")
    private Date jssj;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_CWXXGZ_STATUS")
    private String status;
    @ApiModelProperty("附件地址")
    private List<FileReqVO> attUrlList;
}
