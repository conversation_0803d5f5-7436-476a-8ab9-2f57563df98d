package com.rs.module.acp.controller.admin.db;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.InProcessStageEnum;
import com.rs.module.acp.cons.InTypeEnums;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.controller.admin.db.dto.CasePersonnelRespVO;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.dbSocialRelationsDO;
import com.rs.module.acp.service.db.DetainRegKssService;
import com.rs.module.acp.service.db.DbSocialRelationsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-羁押业务-看守所收押登记")
@RestController
@RequestMapping("/acp/db/detainRegKss")
@Validated
public class DetainRegKssController {

    @Resource
    private DetainRegKssService detainRegKssService;

    @Resource
    private DbSocialRelationsService dbSocialRelationsService;

    @Resource
    private DetainApi detainApi;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-羁押业务-看守所收押登记")
    public CommonResult<String> createDetainRegKss(@Valid @RequestBody DetainRegKssSaveReqVO createReqVO) {
        //新增操作，不用传ID，判断是否有传，如果有传了则提示不用传
//        if(createReqVO.getId() != null){
//            return CommonResult.error("新增操作，不用传ID字段！");
//        }

        return success(detainRegKssService.createDetainRegKss(createReqVO));
    }

    @PostMapping("/quickly/create")
    @ApiOperation(value = "快速入所登记")
    public CommonResult<String> createQuicklyDetainRegKss(@Valid @RequestBody DetainRegKssSaveReqVO createReqVO) {
        //快速入所，类型设置为快速入所
        createReqVO.setRslx(InTypeEnums.EMERGENCY.getCode());
        if(createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            createReqVO.setSpzt(LeaderApprovalStatusEnum.PENDING.getCode());
        }
        //是否存在人员编号，如果有则提示已存在


        return success(detainRegKssService.createDetainRegKss(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-羁押业务-看守所收押登记")
    public CommonResult<String> updateDetainRegKss(@Valid @RequestBody DetainRegKssSaveReqVO updateReqVO) {
        //更新操作，需要传ID，判断是否有传，如果未传则提示id不能为空
        if(updateReqVO.getId() == null||  "".equals(updateReqVO.getId())){
            return CommonResult.error("更新操作，需要传ID字段！");
        }
        //判断是否插入过，没有则先插入
//        detainRegKssService.ifExists(updateReqVO);
        if(updateReqVO.getStatus()!=null&&updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())){
            updateReqVO.setCurrentStep(InProcessStageEnum.PENDING_REGISTRATION.getCode());
        }else if(updateReqVO.getStatus()!=null&&updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())
        &&updateReqVO.getRslx().equalsIgnoreCase(InTypeEnums.EMERGENCY.getCode())&&(updateReqVO.getActInstId()==null|| updateReqVO.getActInstId().isEmpty())){
            updateReqVO.setSpzt(LeaderApprovalStatusEnum.PENDING.getCode());
            updateReqVO.setCurrentStep(InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode());
        }else if(updateReqVO.getStatus()!=null&&updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())
                &&updateReqVO.getRslx().equalsIgnoreCase(InTypeEnums.EMERGENCY.getCode())&&updateReqVO.getActInstId()!=null&&!updateReqVO.getActInstId().isEmpty()){
            updateReqVO.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
        }else if(updateReqVO.getStatus()!=null&&updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())){
            updateReqVO.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
        }
        detainRegKssService.updateDetainRegKss(updateReqVO);
        //循环更新社会关系
        dbSocialRelationsListReqVO listReqVO = new dbSocialRelationsListReqVO();
        listReqVO.setRybh(updateReqVO.getRybh());
        List<dbSocialRelationsDO> list = dbSocialRelationsService.getdbSocialRelationsList(listReqVO);
        //循环根据id删除
        for(dbSocialRelationsDO dbSocialRelationsDO : list){
            dbSocialRelationsService.deletedbSocialRelations(dbSocialRelationsDO.getId());
        }
        List<dbSocialRelationsSaveReqVO> socialRelations = updateReqVO.getSocialRelations();
        for (dbSocialRelationsSaveReqVO socialRelation : socialRelations) {
            socialRelation.setRybh(updateReqVO.getRybh());
            if(socialRelation.getIdType()==null|| socialRelation.getIdType().equals("")){
                socialRelation.setIdType("11");
            }
            dbSocialRelationsService.createdbSocialRelations(socialRelation);
        }

        return success(updateReqVO.getRybh());
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-羁押业务-看守所收押登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDetainRegKss(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            DetainRegKssDO detainRegKss = detainRegKssService.getDetainRegKss(id);
            // 创建并配置社会关系查询对象
            dbSocialRelationsListReqVO listReqVO = new dbSocialRelationsListReqVO();
            listReqVO.setRybh(detainRegKss.getRybh());

            // 获取与当前收押登记相关联的社会关系列表
            List<dbSocialRelationsDO> list = dbSocialRelationsService.getdbSocialRelationsList(listReqVO);
            //循环删除该list中的数据
            for (dbSocialRelationsDO socialRelations : list) {
                dbSocialRelationsService.deletedbSocialRelations(socialRelations.getId());
            }

           detainRegKssService.deleteDetainRegKss(id);
        }
        return success(true);
    }

    /**
     * 根据ID获取羁押业务中的看守所收押登记信息
     * 此方法首先根据提供的ID获取 DetainRegKssDO 对象，然后查询相关社会关系列表，
     * 将其转换为响应所需的格式，并将这些信息封装在 DetainRegKssRespVO 对象中返回
     *
     * @param id 编号，用于查询特定的看守所收押登记信息
     * @return 包含看守所收押登记信息及其相关社会关系的 CommonResult 对象
     */
    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-羁押业务-看守所收押登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DetainRegKssTempRespVO> getDetainRegKss(@RequestParam("id") String id) {
        // 根据ID获取看守所收押登记信息
        DetainRegKssDO detainRegKss = detainRegKssService.getDetainRegKss(id);

        // 创建并配置社会关系查询对象
        dbSocialRelationsListReqVO listReqVO = new dbSocialRelationsListReqVO();
        listReqVO.setRybh(detainRegKss.getRybh());

        // 获取与当前收押登记相关联的社会关系列表
        List<dbSocialRelationsDO> list = dbSocialRelationsService.getdbSocialRelationsList(listReqVO);

        // 将社会关系列表转换为响应所需的VO格式
        List<dbSocialRelationsRespVO> listVO = BeanUtils.toBean(list, dbSocialRelationsRespVO.class);

        // 将看守所收押登记信息转换为响应所需的VO格式
        DetainRegKssTempRespVO detainRegKssRespVO = BeanUtils.toBean(detainRegKss, DetainRegKssTempRespVO.class);

        // 将转换后的社会关系列表设置到收押登记响应对象中
        detainRegKssRespVO.setSocialRelations(listVO);

        // 返回包含收押登记信息及其社会关系的响应对象
        return success(detainRegKssRespVO);
    }


    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-羁押业务-看守所收押登记分页")
    public CommonResult<PageResult<DetainRegKssTempRespVO>> getDetainRegKssPage(@Valid @RequestBody DetainRegKssPageReqVO pageReqVO) {
        PageResult<DetainRegKssDO> pageResult = detainRegKssService.getDetainRegKssPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DetainRegKssTempRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-羁押业务-看守所收押登记列表")
    public CommonResult<List<DetainRegKssTempRespVO>> getDetainRegKssList(@Valid @RequestBody DetainRegKssListReqVO listReqVO) {
        List<DetainRegKssDO> list = detainRegKssService.getDetainRegKssList(listReqVO);
        return success(BeanUtils.toBean(list, DetainRegKssTempRespVO.class));
    }

    /**
     * 获取主表以及健康登记医生意见信息，根据ID参数
     */
    @GetMapping("/getCombineInfo")
    @ApiOperation(value = "获得基本信息以及医生意见信息")
    @ApiImplicitParam(name = "rybh", value = "RYBH")
    public CommonResult<CombineRespVO> getCombineInfo(@RequestParam("rybh") String rybh) {
        CombineRespVO db = detainRegKssService.getCombineInfo(rybh);

        return success(db);
    }

    /**
     * 更新所领导审批状态
     */
    @PostMapping("/updateLeaderApprovalStatus")
    @ApiOperation(value = "更新所领导审批状态")
    public CommonResult<Boolean> updateLeaderApprovalStatus(@Valid @RequestBody LeaderApprovalStatusReqVO updateReqVO) {
        detainRegKssService.updateLeaderApprovalStatus(updateReqVO);
        return success(true);
    }

    @PostMapping("/updateLeaderApprovalStatusByRybh")
    @ApiOperation(value = "更新所领导审批状态")
    public CommonResult<Boolean> updateLeaderApprovalStatusByRybh(@Valid @RequestBody LeaderApprovalStatusReqVO updateReqVO) {
        detainRegKssService.updateLeaderApprovalStatus(updateReqVO);
        return success(true);
    }

    /**
     * 根据人员编号获取人员信息
     */
    @GetMapping("/getPrisonerInfo")
    @ApiOperation(value = "根据人员编号获取人员信息")
    @ApiImplicitParam(name = "rybh", value = "RYBH")
    public CommonResult<DetainRegKssTempRespVO> getPrisonerInfo(@RequestParam("rybh") String rybh) {
        DetainRegKssDO db = detainRegKssService.getPrisonerInfo(rybh);

        return success(BeanUtils.toBean(db, DetainRegKssTempRespVO.class));
    }

    /**
     * 获取各个流程状态信息
     */
    @GetMapping("/getInRecordStatus")
    @ApiOperation(value = "获取各个流程状态信息")
    public CommonResult<InRecordStatusVO> getInRecordStatus(@RequestParam("rybh") String rybh) {
        InRecordStatusVO db = detainRegKssService.getInRecordStatus(rybh);
        return success(db);
    }

    /**
     * 更新流程信息
     *
     * @param updateReqVO
     * @return
     */
    @PostMapping("/updateWorkflowInfo")
    @ApiOperation(value = "更新流程信息-看守所收押登记")
    public CommonResult<Boolean> updateWorkflowInfo(@Valid @RequestBody DetainRegKssSaveReqVO updateReqVO) {
        //更新操作，需要传ID，判断是否有传，如果未传则提示id不能为空
        if(updateReqVO.getRybh() == null|| updateReqVO.getRybh().isEmpty()){
            return CommonResult.error("rybh参数不能为空！");
        }

        DetainRegKssDO db = detainRegKssService.getPrisonerInfo(updateReqVO.getRybh());
        if(db != null){
            updateReqVO.setId(db.getId());
            detainRegKssService.updateDetainRegKss(updateReqVO);
        }else {
            System.out.println("未找到RYBH为：" + updateReqVO.getRybh() + "的记录！");
        }


        return success(true);
    }

    /**
     * 更新流程阶段信息，提交状态信息
     * @ prison   监所  ek  sk
     * @ rybh      人员编号
     * @ status     状态
     * @ currentStep  当前阶段
     * @ spzt  审批状态
     * @return void
     */
    @PostMapping("/updateStepInfo")
    @ApiOperation(value = "更新阶段状态流转信息，提交状态信息")
    public CommonResult<Boolean> updateStepInfo(@Valid @RequestBody DetainRegKssUpdateStepInfoReqVO reqVO) {
        String prison = reqVO.getPrison();
        String rybh = reqVO.getRybh();
        String status = reqVO.getStatus();
        String currentStep = reqVO.getCurrentStep();
        String spzt = reqVO.getSpzt();
        String actInstId = reqVO.getActInstId();
        detainRegKssService.updateStepInfo(prison, rybh, status, currentStep, spzt,actInstId);
        return success(true);
    }

    /**
     * 根据jgrybm查询获取办案人员信息
     */
    @GetMapping("/getCasePersonnelByjgrybm")
    @ApiOperation(value = "根据jgrybm查询获取办案人员信息")
    public CommonResult<List<CasePersonnelRespVO>> getCasePersonnelList(@RequestParam("jgrybm") String jgrybm) {
//        CommonResult<com.rs.module.acp.controller.admin.db.dto.CasePersonnelRespVO> db = detainApi.getCasePersonnel(jgrybm);


            List<CasePersonnelRespVO> list = new ArrayList<>();
        if(jgrybm.equals("6226941653775213")) {
            CasePersonnelRespVO casePersonnelRespVO = new CasePersonnelRespVO();
            casePersonnelRespVO.setBadwlx("1");
            casePersonnelRespVO.setBadw("110000113");
            casePersonnelRespVO.setBar("刘鹏飞");
            casePersonnelRespVO.setBarlxff("13029382930");
            casePersonnelRespVO.setBarxb("1");
            casePersonnelRespVO.setBarzjlx("111");
            casePersonnelRespVO.setBarzjhm("334520198910232920");
            casePersonnelRespVO.setBahj("38");

            list.add(casePersonnelRespVO);

            CasePersonnelRespVO casePersonnelRespVO1 = new CasePersonnelRespVO();
            casePersonnelRespVO1.setBadwlx("1");
            casePersonnelRespVO1.setBadw("110000113");
            casePersonnelRespVO1.setBar("张清三");
            casePersonnelRespVO1.setBarlxff("18612345678");
            casePersonnelRespVO1.setBarxb("1");
            casePersonnelRespVO1.setBarzjlx("111");
            casePersonnelRespVO1.setBarzjhm("440106199205181234");
            casePersonnelRespVO1.setBahj("31");

            list.add(casePersonnelRespVO1);
        }else{
            CasePersonnelRespVO casePersonnelRespVO = new CasePersonnelRespVO();
        }

        return success(list);
    }
}
