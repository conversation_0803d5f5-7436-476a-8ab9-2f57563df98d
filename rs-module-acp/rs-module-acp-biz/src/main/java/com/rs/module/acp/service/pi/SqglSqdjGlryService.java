package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjGlryListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjGlryPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjGlrySaveReqVO;
import com.rs.module.acp.entity.pi.SqglSqdjGlryDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情管理-所情登记关联人员 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglSqdjGlryService extends IBaseService<SqglSqdjGlryDO>{

    /**
     * 创建实战平台-巡视管控-所情管理-所情登记关联人员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSqglSqdjGlry(@Valid SqglSqdjGlrySaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情管理-所情登记关联人员
     *
     * @param updateReqVO 更新信息
     */
    void updateSqglSqdjGlry(@Valid SqglSqdjGlrySaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情管理-所情登记关联人员
     *
     * @param id 编号
     */
    void deleteSqglSqdjGlry(String id);

    /**
     * 获得实战平台-巡视管控-所情管理-所情登记关联人员
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-所情登记关联人员
     */
    SqglSqdjGlryDO getSqglSqdjGlry(String id);

    /**
    * 获得实战平台-巡视管控-所情管理-所情登记关联人员分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情管理-所情登记关联人员分页
    */
    PageResult<SqglSqdjGlryDO> getSqglSqdjGlryPage(SqglSqdjGlryPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情管理-所情登记关联人员列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情管理-所情登记关联人员列表
    */
    List<SqglSqdjGlryDO> getSqglSqdjGlryList(SqglSqdjGlryListReqVO listReqVO);


}
