package com.rs.module.acp.service.pm;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.CnpFaceDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.CnpFaceDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-仓内外屏人脸信息维护 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CnpFaceServiceImpl extends BaseServiceImpl<CnpFaceDao, CnpFaceDO> implements CnpFaceService {

    @Resource
    private CnpFaceDao cnpFaceDao;

    @Override
    public String createCnpFace(CnpFaceSaveReqVO createReqVO) {
        // 插入
        CnpFaceDO cnpFace = BeanUtils.toBean(createReqVO, CnpFaceDO.class);
        cnpFaceDao.insert(cnpFace);
        // 返回
        return cnpFace.getId();
    }

    @Override
    public void updateCnpFace(CnpFaceSaveReqVO updateReqVO) {
        // 校验存在
        validateCnpFaceExists(updateReqVO.getId());
        // 更新
        CnpFaceDO updateObj = BeanUtils.toBean(updateReqVO, CnpFaceDO.class);
        cnpFaceDao.updateById(updateObj);
    }

    @Override
    public void deleteCnpFace(String id) {
        // 校验存在
        validateCnpFaceExists(id);
        // 删除
        cnpFaceDao.deleteById(id);
    }

    private void validateCnpFaceExists(String id) {
        if (cnpFaceDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-仓内外屏人脸信息维护数据不存在");
        }
    }

    @Override
    public CnpFaceDO getCnpFace(String id) {
        return cnpFaceDao.selectById(id);
    }

    @Override
    public PageResult<CnpFaceDO> getCnpFacePage(CnpFacePageReqVO pageReqVO) {
        return cnpFaceDao.selectPage(pageReqVO);
    }

    @Override
    public List<CnpFaceDO> getCnpFaceList(CnpFaceListReqVO listReqVO) {
        return cnpFaceDao.selectList(listReqVO);
    }


}
