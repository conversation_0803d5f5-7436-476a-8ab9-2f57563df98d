package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxSwhcsReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所务会初审会议号")
    private String swhcshyh;

    @ApiModelProperty("召开所务会进行初审研究日期")
    private Date zkswhjxcsyjrq;

    @ApiModelProperty("所务会初审结果（1、通过；2、未通过。）")
    private String swhcsjg;

    @ApiModelProperty("未通过原因")
    private String wtgyy;

    @ApiModelProperty("告知申请人未通过原因时间")
    private Date gzsqrwtgyysj;

}
