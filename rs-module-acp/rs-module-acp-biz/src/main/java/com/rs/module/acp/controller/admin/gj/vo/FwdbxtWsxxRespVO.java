package com.rs.module.acp.controller.admin.gj.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-法务待办协同文书信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FwdbxtWsxxRespVO extends BaseDO implements TransPojo {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("法务待办协同ID")
    private String fwdbxtId;
    @ApiModelProperty("文书编号")
    private String wsbh;
    @ApiModelProperty("文书文号")
    private String wswh;
    @ApiModelProperty("文书名称")
    private String wsmc;
    @ApiModelProperty("存放路径")
    private String cflj;
    @ApiModelProperty("文件大小")
    private Integer wjdx;
    @ApiModelProperty("显示顺序")
    private Integer xssx;
    @ApiModelProperty("创建时间")
    private Date cjsj;

}
