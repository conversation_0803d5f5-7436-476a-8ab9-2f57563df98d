package com.rs.module.acp.service.zh;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkSaveReqVO;
import com.rs.module.acp.entity.zh.ReportJschmrqkDO;

/**
 * 早850监所晨会每日情况 Service 接口
 *
 * <AUTHOR>
 */
public interface ReportJschmrqkService extends IBaseService<ReportJschmrqkDO>{

    /**
     * 创建早850监所晨会每日情况
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createReportJschmrqk(@Valid ReportJschmrqkSaveReqVO createReqVO);

    /**
     * 更新早850监所晨会每日情况
     *
     * @param updateReqVO 更新信息
     */
    void updateReportJschmrqk(@Valid ReportJschmrqkSaveReqVO updateReqVO);

    /**
     * 删除早850监所晨会每日情况
     *
     * @param id 编号
     */
    void deleteReportJschmrqk(String id);

    /**
     * 获得早850监所晨会每日情况
     *
     * @param id 编号
     * @return 早850监所晨会每日情况
     */
    ReportJschmrqkDO getReportJschmrqk(String id);

    /**
    * 获得早850监所晨会每日情况分页
    *
    * @param pageReqVO 分页查询
    * @return 早850监所晨会每日情况分页
    */
    PageResult<ReportJschmrqkDO> getReportJschmrqkPage(ReportJschmrqkPageReqVO pageReqVO);

    /**
    * 获得早850监所晨会每日情况列表
    *
    * @param listReqVO 查询条件
    * @return 早850监所晨会每日情况列表
    */
    List<ReportJschmrqkDO> getReportJschmrqkList(ReportJschmrqkListReqVO listReqVO);

    /**
     * 根据报告日期获取晨会报告
     * @param reportDate Date 报告日期
     * @return ReportJschmrqkDO
     */
    public ReportJschmrqkDO getByReportDate(Date reportDate);
    
    /**
     * 获取前24小时统计数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    public JSONObject getDataOf24Hours(String orgCode, String reportDate);

    /**
     * 截至目前统计数据
     * @param orgCode String 机构代码
     * @return JSONObject
     */
    public JSONObject getDataOfUpToNow(String orgCode);
    
    /**
     * 获取前24小时提讯数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    public JSONObject getDataOf24Tx(String orgCode, String reportDate);
    
    /**
     * 获取前24小时提解数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    public JSONObject getDataOf24Tj(String orgCode, String reportDate);
    
    /**
     * 获取前24小时律师会见数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    public JSONObject getDataOf24Lshj(String orgCode, String reportDate);
    
    /**
     * 获取前24小时收押情况-入所人员数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getSyqk24Rsry(String orgCode, String reportDate);
    
    /**
     * 获取前24小时收押情况-出所人员数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getSyqk24Csry(String orgCode, String reportDate);
    
    /**
     * 获取前24小时在押情况-65岁以上人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getZyqk24Above65Years(String orgCode, String reportDate);
    
    /**
     * 获取前24小时在押情况-加戴械具人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getZyqk24Jdxj(String orgCode, String reportDate);
    
    /**
     * 获取前24小时在押情况-单独关押
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getZyqk24Ddgy(String orgCode, String reportDate);
    
    /**
     * 获取前24小时在押情况-关注群体人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    public List<Map<String, Object>> getZyqk24Gzqtry(String orgCode, String reportDate);
    
    /**
     * 获取前24小时在押情况-精神异常人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getZyqk24Jsyc(String orgCode, String reportDate);
    
    /**
     * 获取前24小时在押情况-吞食异物人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getZyqk24Tsyw(String orgCode, String reportDate);
    
    /**
     * 获取前24小时在押情况-特殊身份
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getZyqk24Tssf(String orgCode, String reportDate);
    
    /**
     * 外籍人员管控情况-国籍分布情况
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getWjrygkGjfb(String orgCode, String reportDate);
    
    /**
     * 外籍人员管控情况-涉嫌犯罪类型
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getWjrygkSxfzlx(String orgCode, String reportDate);
    
    /**
     * 服务办案工作情况-提讯
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getFwbagzqk24Tx(String orgCode, String reportDate);
    
    /**
     * 服务办案工作情况-提解
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getFwbagzqk24Tj(String orgCode, String reportDate);
    
    /**
     * 服务办案工作情况-律师接待
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getFwbagzqk24Lsjd(String orgCode, String reportDate);
    
    /**
     * 服务办案工作情况-接待家属
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getFwbagzqk24Jdjs(String orgCode, String reportDate);
    
    /**
     * 服务办案工作情况-交付执行
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getFwbagzqk24Jfzx(String orgCode, String reportDate);
    
    /**
     * 服务办案工作情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getFwbagzqk24Sghj(String orgCode, String reportDate);
    
    /**
     * 重点风险人员管控情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    Map<String, Object> getZdfxry24Sjfx(String orgCode, String reportDate);
    
    /**
     * 重点风险人员管控情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> getZdfxry24Zyqk(String orgCode, String reportDate);
    
    /**
     * 其他基础数据-巡诊发药
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    Integer getJcsj24Xzfy(String orgCode, String reportDate);
    
    /**
     * 其他基础数据-所内治疗
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    Integer getJcsj24Snzl(String orgCode, String reportDate);
    
    /**
     * 其他基础数据-以绝食、拒绝医疗等方式对抗监管情况
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    Integer getJcsj24Dkjg(String orgCode, String reportDate);
    
    /**
     * 其他基础数据-一级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> getJcsj24Yj(String orgCode, String reportDate);
    
    /**
     * 其他基础数据-二级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> getJcsj24Ej(String orgCode, String reportDate);
    
    /**
     * 其他基础数据-三级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> getJcsj24Sj(String orgCode, String reportDate);
}
