package com.rs.module.acp.controller.admin.gj.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.RyxxVO;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-随身物品登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GjPersonalEffectsRespVO extends RyxxVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    @Format(service = PrisonerService.class, method = "getPrisonerByJgrybm", value = "jgrybm", toBean = PrisonerVwRespVO.class, isTile = true)
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("物品名称")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SSWPDJ_WPMC")
    private String name;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("经办时间")
    private Date operateTime;
    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;
    @ApiModelProperty("经办民警")
    private String operatePolice;
    @ApiModelProperty("归还时间")
    private Date returnTime;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SSWPDJ_WPZT")
    private String status;
}
