package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjtsdxListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjtsdxPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjtsdxSaveReqVO;
import com.rs.module.acp.entity.pi.SqglSqdjtsdxDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情管理-所情登记推送对象 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglSqdjtsdxService extends IBaseService<SqglSqdjtsdxDO>{

    /**
     * 创建实战平台-巡视管控-所情管理-所情登记推送对象
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSqglSqdjtsdx(@Valid SqglSqdjtsdxSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情管理-所情登记推送对象
     *
     * @param updateReqVO 更新信息
     */
    void updateSqglSqdjtsdx(@Valid SqglSqdjtsdxSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情管理-所情登记推送对象
     *
     * @param id 编号
     */
    void deleteSqglSqdjtsdx(String id);

    /**
     * 获得实战平台-巡视管控-所情管理-所情登记推送对象
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-所情登记推送对象
     */
    SqglSqdjtsdxDO getSqglSqdjtsdx(String id);

    /**
    * 获得实战平台-巡视管控-所情管理-所情登记推送对象分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情管理-所情登记推送对象分页
    */
    PageResult<SqglSqdjtsdxDO> getSqglSqdjtsdxPage(SqglSqdjtsdxPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情管理-所情登记推送对象列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情管理-所情登记推送对象列表
    */
    List<SqglSqdjtsdxDO> getSqglSqdjtsdxList(SqglSqdjtsdxListReqVO listReqVO);


}
