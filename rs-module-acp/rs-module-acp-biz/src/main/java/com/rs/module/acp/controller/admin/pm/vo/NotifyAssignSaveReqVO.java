package com.rs.module.acp.controller.admin.pm.vo;

import com.bsp.sdk.msg.model.ReceiveUser;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.pm.NotifyAssignRecipientDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-通知交办新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class NotifyAssignSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("通知标题")
    @NotEmpty(message = "通知标题不能为空")
    private String title;

    @ApiModelProperty("发布类型(01:交办事项,02:通知公告)")
    @NotEmpty(message = "发布类型不能为空")
    private String publishType;

    @ApiModelProperty("正文内容")
    @NotEmpty(message = "正文内容不能为空")
    private String content;

    @ApiModelProperty("是否全警")
    @NotNull(message = "是否全警不能为空")
    private Short isFullPolice;

//    @ApiModelProperty("被考核对象类型，01:岗位、02：部门、03：用户")
//    @NotEmpty(message = "被考核对象类型，01:岗位、02：部门、03：用户不能为空")
//    private String handleObjectType;

//    @ApiModelProperty("被考核对象ID")
//    @NotEmpty(message = "被考核对象ID不能为空")
//    private String handleObjectId;
//
//    @ApiModelProperty("被考核对象名称")
//    @NotEmpty(message = "被考核对象名称不能为空")
//    private String handleObjectName;

    @ApiModelProperty("接收人")
    private List<NotifyAssignRecipientReqVO> userList;

    @ApiModelProperty("附件地址")
    private String attUrl;

//    @ApiModelProperty("状态")
//    @NotEmpty(message = "状态不能为空")
//    private String status;

}
