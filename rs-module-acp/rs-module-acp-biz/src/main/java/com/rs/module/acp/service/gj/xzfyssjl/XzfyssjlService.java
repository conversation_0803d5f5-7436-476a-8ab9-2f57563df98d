package com.rs.module.acp.service.gj.xzfyssjl;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlUpdateReqVO;
import com.rs.module.acp.entity.gj.XzfyssjlDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-行政复议讼诉记录 Service 接口
 *
 * <AUTHOR>
 */
public interface XzfyssjlService extends IBaseService<XzfyssjlDO>{

    /**
     * 创建实战平台-管教业务-行政复议讼诉记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createXzfyssjl(@Valid XzfyssjlSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-行政复议讼诉记录
     *
     * @param updateReqVO 更新信息
     */
    void updateXzfyssjl(@Valid XzfyssjlUpdateReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-行政复议讼诉记录
     *
     * @param id 编号
     */
    void deleteXzfyssjl(String id);

    /**
     * 获得实战平台-管教业务-行政复议讼诉记录
     *
     * @param id 编号
     * @return 实战平台-管教业务-行政复议讼诉记录
     */
    XzfyssjlDO getXzfyssjl(String id);

    /**
    * 获得实战平台-管教业务-行政复议讼诉记录分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-行政复议讼诉记录分页
    */
    PageResult<XzfyssjlDO> getXzfyssjlPage(XzfyssjlPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-行政复议讼诉记录列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-行政复议讼诉记录列表
    */
    List<XzfyssjlDO> getXzfyssjlList(XzfyssjlListReqVO listReqVO);


    PageResult<XzfyssjlDO> getAppXzfyssjlPage(int pageNo, int pageSize, String jgrybm, String type);

    void forward(String id);
}
