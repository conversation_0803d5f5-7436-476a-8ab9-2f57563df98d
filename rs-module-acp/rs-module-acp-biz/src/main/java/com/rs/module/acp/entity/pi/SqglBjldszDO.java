package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-报警联动设置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_bjldsz")
@KeySequence("acp_pi_sqgl_bjldsz_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_bjldsz")
public class SqglBjldszDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所请来源，字典：
     */
    private String eventSrc;
    /**
     * 是否启用
     */
    private Short isEnabled;
    /**
     * 备注
     */
    private String remark;
    /**
     * 可选联动配置，逗号分隔	字典：
     */
    private String optionalLinkageSettings;
    /**
     * 提示音，字典：
     */
    private String promptSound;

}
