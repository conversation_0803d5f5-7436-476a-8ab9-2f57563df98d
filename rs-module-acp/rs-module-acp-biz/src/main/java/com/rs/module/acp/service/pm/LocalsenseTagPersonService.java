package com.rs.module.acp.service.pm;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.pm.dto.AreaTreeNodeDTO;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-监管管理-定位标签与人员绑定 Service 接口
 *
 * <AUTHOR>
 */
public interface LocalsenseTagPersonService extends IBaseService<LocalsenseTagPersonDO>{

    /**
     * 创建实战平台-监管管理-定位标签与人员绑定
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLocalsenseTagPerson(@Valid LocalsenseTagPersonSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-定位标签与人员绑定
     *
     * @param updateReqVO 更新信息
     */
    void updateLocalsenseTagPerson(@Valid LocalsenseTagPersonSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-定位标签与人员绑定
     *
     * @param id 编号
     */
    void deleteLocalsenseTagPerson(String id);

    /**
     * 获得实战平台-监管管理-定位标签与人员绑定
     *
     * @param id 编号
     * @return 实战平台-监管管理-定位标签与人员绑定
     */
    LocalsenseTagPersonDO getLocalsenseTagPerson(String id);

    String rsCreateLocalsenseTagPerson(LocalsenseTagPersonRsSaveReqVO reqVO) throws Exception;

    String znwdCreateLocalsenseTagPerson(LocalsenseTagPersonZnwdSaveReqVO reqVO) throws Exception;

    String mjCreateLocalsenseTagPerson(LocalsenseTagPersonMjSaveReqVO reqVO) throws Exception;

    //查询bindPersonId 是否已经绑定过手环
    List<LocalsenseTagPersonDO> getLocalsenseTagPersonByBindPersonIds(String bindPersonIds);

    //根据tagId查询是否已正在绑定
    List<LocalsenseTagPersonDO> getLocalsenseTagPersonByTagIds(String tagIds);

    String unbind(LocalsenseTagPersonUnbindSaveReqVO reqVO) throws Exception;

    List<LocalsenseTagPersonDO> getLocalsenseTagPersonHistoryList(String bindPersonId);

    //查询标签所有绑定记录
    List<LocalsenseTagPersonDO> getLocalsenseTagPersonHistoryListByTagId(String tagId);

    List<JSONObject> getLocalSenseInfo(String tagIds);

    //查询指定监室下人员绑定记录
    List<LocalsenseTagPersonRespVO> getBindPersonRoomList(String orgCode, String roomId);

    List<Map<String, Object>> selectTagJgryCountByRoom(String orgCode, String roomId);

    List<Map<String, Object>> selectTagPersonCountByRoom(String orgCode, String roomId);

    boolean setHandBandVitalSignPushInterval(LocalsenseHeartRateSaveReqVO saveReqVO) throws Exception;
}
