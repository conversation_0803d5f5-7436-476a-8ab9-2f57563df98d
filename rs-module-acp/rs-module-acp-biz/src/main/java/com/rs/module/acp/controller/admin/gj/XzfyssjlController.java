package com.rs.module.acp.controller.admin.gj;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.XzfyssjlDO;
import com.rs.module.acp.service.gj.xzfyssjl.XzfyssjlService;

@Api(tags = "实战平台-管教业务-行政复议讼诉记录")
@RestController
@RequestMapping("/acp/gj/xzfyssjl")
@Validated
public class XzfyssjlController {

    @Resource
    private XzfyssjlService xzfyssjlService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-行政复议讼诉记录")
    public CommonResult<String> createXzfyssjl(@Valid @RequestBody XzfyssjlSaveReqVO createReqVO) {
        return success(xzfyssjlService.createXzfyssjl(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-行政复议讼诉记录")
    public CommonResult<Boolean> updateXzfyssjl(@Valid @RequestBody XzfyssjlUpdateReqVO updateReqVO) {
        xzfyssjlService.updateXzfyssjl(updateReqVO);
        return success(true);
    }

    @PostMapping("/forward")
    @ApiOperation(value = "转递")
    public CommonResult<Boolean> forward(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        Assert.notNull(jsonObject, "入参不能为空");
        String id = jsonObject.getString("id");
        Assert.notEmpty(id, "id不能为空");
        xzfyssjlService.forward(id);
        return success(true);
    }


    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-行政复议讼诉记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteXzfyssjl(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           xzfyssjlService.deleteXzfyssjl(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-行政复议讼诉记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<XzfyssjlRespVO> getXzfyssjl(@RequestParam("id") String id) {
        XzfyssjlDO xzfyssjl = xzfyssjlService.getXzfyssjl(id);
        return success(BeanUtils.toBean(xzfyssjl, XzfyssjlRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-行政复议讼诉记录分页")
    public CommonResult<PageResult<XzfyssjlRespVO>> getXzfyssjlPage(@Valid @RequestBody XzfyssjlPageReqVO pageReqVO) {
        PageResult<XzfyssjlDO> pageResult = xzfyssjlService.getXzfyssjlPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, XzfyssjlRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-行政复议讼诉记录列表")
    public CommonResult<List<XzfyssjlRespVO>> getXzfyssjlList(@Valid @RequestBody XzfyssjlListReqVO listReqVO) {
        List<XzfyssjlDO> list = xzfyssjlService.getXzfyssjlList(listReqVO);
        return success(BeanUtils.toBean(list, XzfyssjlRespVO.class));
    }
}
