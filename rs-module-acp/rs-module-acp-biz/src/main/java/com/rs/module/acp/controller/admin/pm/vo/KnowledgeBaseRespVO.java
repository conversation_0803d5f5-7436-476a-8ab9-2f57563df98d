package com.rs.module.acp.controller.admin.pm.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-知识库 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class KnowledgeBaseRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("创建时间")
    private Date addTime;
    @ApiModelProperty("创建人")
    private String addUserName;
    @ApiModelProperty("知识库名称")
    private String knowledgeName;
    @ApiModelProperty("知识库类型")
    private String knowledgeType;
    @ApiModelProperty("摘要")
    private String summary;
    @ApiModelProperty("附件地址")
    private String attUrl;
}
