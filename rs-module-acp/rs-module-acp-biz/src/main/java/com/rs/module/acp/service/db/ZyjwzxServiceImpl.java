package com.rs.module.acp.service.db;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.util.StringUtil;
import com.rs.module.acp.controller.admin.db.vo.zyjwzx.*;
import com.rs.module.acp.controller.admin.zh.vo.MeetingRecordsRespVO;
import com.rs.module.acp.entity.zh.MeetingRecordsDO;
import com.rs.module.acp.enums.db.ZyjwzxStatusEnum;
import com.rs.module.acp.service.zh.MeetingRecordsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.db.ZyjwzxDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.ZyjwzxDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-暂予监外执行 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZyjwzxServiceImpl extends BaseServiceImpl<ZyjwzxDao, ZyjwzxDO> implements ZyjwzxService {

    @Resource
    private ZyjwzxDao zyjwzxDao;

    @Resource
    private MeetingRecordsService meetingRecordsService;

    @Override
    public String createZyjwzx(ZyjwzxSaveReqVO createReqVO) {
        // 插入
        ZyjwzxDO zyjwzx = BeanUtils.toBean(createReqVO, ZyjwzxDO.class);
        zyjwzxDao.insert(zyjwzx);
        // 返回
        return zyjwzx.getId();
    }

    @Override
    public void updateZyjwzx(ZyjwzxSaveReqVO updateReqVO) {
        // 校验存在
        validateZyjwzxExists(updateReqVO.getId());
        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(updateReqVO, ZyjwzxDO.class);
        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void deleteZyjwzx(String id) {
        // 校验存在
        validateZyjwzxExists(id);
        // 删除
        zyjwzxDao.deleteById(id);
    }

    private ZyjwzxDO validateZyjwzxExists(String id) {
        ZyjwzxDO zyjwzxDO = zyjwzxDao.selectById(id);
        if (zyjwzxDO == null) {
            throw new ServerException("实战平台-收押业务-暂予监外执行数据不存在");
        }
        return zyjwzxDO;
    }

    @Override
    public ZyjwzxRespVO getZyjwzx(String id) {
        ZyjwzxDO zyjwzxDO = zyjwzxDao.selectById(id);
        ZyjwzxRespVO zyjwzxRespVO = BeanUtils.toBean(zyjwzxDO, ZyjwzxRespVO.class);
        if (Objects.nonNull(zyjwzxRespVO)) {
            if (StringUtils.isNotEmpty(zyjwzxRespVO.getSwhcshyh())) {
                MeetingRecordsDO cshyxx = meetingRecordsService.getOne(Wrappers.lambdaQuery(MeetingRecordsDO.class)
                        .eq(MeetingRecordsDO::getId, zyjwzxRespVO.getSwhcshyh()));
                zyjwzxRespVO.setSwhcshyxx(BeanUtils.toBean(cshyxx, MeetingRecordsRespVO.class));
            }
            if (StringUtils.isNotEmpty(zyjwzxRespVO.getSwhsyhyh())) {
                MeetingRecordsDO shhyxx = meetingRecordsService.getOne(Wrappers.lambdaQuery(MeetingRecordsDO.class)
                        .eq(MeetingRecordsDO::getId, zyjwzxRespVO.getSwhsyhyh()));
                zyjwzxRespVO.setSwhsyxx(BeanUtils.toBean(shhyxx, MeetingRecordsRespVO.class));
            }
        }
        return zyjwzxRespVO;
    }

    @Override
    public PageResult<ZyjwzxDO> getZyjwzxPage(ZyjwzxPageReqVO pageReqVO) {
        return zyjwzxDao.selectPage(pageReqVO);
    }

    @Override
    public List<ZyjwzxDO> getZyjwzxList(ZyjwzxListReqVO listReqVO) {
        return zyjwzxDao.selectList(listReqVO);
    }

    @Override
    public String dj(ZyjwzxDjReqVO djReqVO) {
        // 插入
        djReqVO.setId(null);
        ZyjwzxDO zyjwzx = BeanUtils.toBean(djReqVO, ZyjwzxDO.class);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        zyjwzx.setOperateTime(new Date());
        zyjwzx.setOperatePolice(sessionUser.getName());
        zyjwzx.setOperatePoliceSfzh(sessionUser.getIdCard());
        zyjwzx.setStatus(ZyjwzxStatusEnum.DSWHCS.getCode());
        zyjwzxDao.insert(zyjwzx);

        return zyjwzx.getId();
    }

    @Override
    public void swhcs(ZyjwzxSwhcsReqVO swhcsReqVO) {

        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(swhcsReqVO.getId());
        if (!ZyjwzxStatusEnum.DSWHCS.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DSWHCS.getName().substring(1) + "】操作");
        }
        MeetingRecordsDO one = meetingRecordsService.getOne(Wrappers.lambdaQuery(MeetingRecordsDO.class)
                .eq(MeetingRecordsDO::getId, swhcsReqVO.getSwhcshyh()));
        if (Objects.isNull(one)) {
            throw new ServerException("该所务会议不存在，会议编号：" + swhcsReqVO.getSwhcshyh());
        }
        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(swhcsReqVO, ZyjwzxDO.class);

        // 所务会初审结果（1、通过；2、未通过。）
        if ("2".equals(swhcsReqVO.getSwhcsjg())) {
            updateObj.setStatus(ZyjwzxStatusEnum.YBJ.getCode());
        } else {
            updateObj.setStatus(ZyjwzxStatusEnum.DZDJDJC.getCode());
        }

        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void zdjdjc(ZyjwzxZdjdjcReqVO zdjdjcReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(zdjdjcReqVO.getId());
        if (!ZyjwzxStatusEnum.DZDJDJC.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DZDJDJC.getName().substring(1) + "】操作");
        }

        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(zdjdjcReqVO, ZyjwzxDO.class);

        // 诊断、鉴定或者检查结论（1、通过；2、未通过。）
        if ("2".equals(zdjdjcReqVO.getZdjdhzjcjl())) {
            updateObj.setStatus(ZyjwzxStatusEnum.YBJ.getCode());
        } else {
            updateObj.setStatus(ZyjwzxStatusEnum.DBWJYCX.getCode());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setZdjdhzjcjbmjsfzh(sessionUser.getIdCard());
        updateObj.setZdjdhzjcjbmjxm(sessionUser.getName());
        updateObj.setZdjdhzjcjbsj(new Date());
        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void bwjycx(ZyjwzxBwjycxReqVO bwjycxReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(bwjycxReqVO.getId());
        if (!ZyjwzxStatusEnum.DBWJYCX.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DBWJYCX.getName().substring(1) + "】操作");
        }

        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(bwjycxReqVO, ZyjwzxDO.class);

        // 保外就医审查结论（1、通过；2、未通过。）
        if ("2".equals(bwjycxReqVO.getBwjyscjl())) {
            updateObj.setStatus(ZyjwzxStatusEnum.YBJ.getCode());
        } else {
            updateObj.setStatus(ZyjwzxStatusEnum.DHSJZD.getCode());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setBwjyscjbmjsfzh(sessionUser.getIdCard());
        updateObj.setBwjyscjbmjxm(sessionUser.getName());
        updateObj.setBwjyscjbsj(new Date());
        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void hsjzd(ZyjwzxHsjzdReqVO hsjzdReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(hsjzdReqVO.getId());
        if (!ZyjwzxStatusEnum.DHSJZD.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DHSJZD.getName().substring(1) + "】操作");
        }

        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(hsjzdReqVO, ZyjwzxDO.class);

        // 核实居住地调查评估意见（1、通过；2、未通过。）
        if ("2".equals(hsjzdReqVO.getHsjzddcpgyj())) {
            updateObj.setStatus(ZyjwzxStatusEnum.YBJ.getCode());
        } else {
            updateObj.setStatus(ZyjwzxStatusEnum.DSWHSY.getCode());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setHsjzdjbmjsfzh(sessionUser.getIdCard());
        updateObj.setHsjzdjbmjxm(sessionUser.getName());
        updateObj.setHsjzdjbsj(new Date());
        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void swhsy(ZyjwzxSwhsyReqVO swhsyReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(swhsyReqVO.getId());
        if (!ZyjwzxStatusEnum.DSWHSY.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DSWHSY.getName().substring(1) + "】操作");
        }
        MeetingRecordsDO one = meetingRecordsService.getOne(Wrappers.lambdaQuery(MeetingRecordsDO.class)
                .eq(MeetingRecordsDO::getId, swhsyReqVO.getSwhsyhyh()));
        if (Objects.isNull(one)) {
            throw new ServerException("该所务会议不存在，会议编号：" + swhsyReqVO.getSwhsyhyh());
        }
        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(swhsyReqVO, ZyjwzxDO.class);

        // 所务会审议结果（1、通过；2、未通过。）
        if ("2".equals(swhsyReqVO.getSwhsyjg())) {
            updateObj.setStatus(ZyjwzxStatusEnum.YBJ.getCode());
        } else {
            updateObj.setStatus(ZyjwzxStatusEnum.DSNGS.getCode());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setSwhsyjbmjsfzh(sessionUser.getIdCard());
        updateObj.setSwhsyjbmjxm(sessionUser.getName());
        updateObj.setSwhsyjbsj(new Date());
        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void sngs(ZyjwzxSngsReqVO sngsReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(sngsReqVO.getId());
        if (!ZyjwzxStatusEnum.DSNGS.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DSNGS.getName().substring(1) + "】操作");
        }

        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(sngsReqVO, ZyjwzxDO.class);

        updateObj.setStatus(ZyjwzxStatusEnum.DTZSH.getCode());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setSngsjbmjsfzh(sessionUser.getIdCard());
        updateObj.setSngsjbmjxm(sessionUser.getName());
        updateObj.setSngsjbsj(new Date());
        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void tzsh(ZyjwzxTzshReqVO tzshReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(tzshReqVO.getId());
        if (!ZyjwzxStatusEnum.DTZSH.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DTZSH.getName().substring(1) + "】操作");
        }

        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(tzshReqVO, ZyjwzxDO.class);

        updateObj.setStatus(ZyjwzxStatusEnum.DZSJCSJD.getCode());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setTzshjbmjsfzh(sessionUser.getIdCard());
        updateObj.setTzshjbmjxm(sessionUser.getName());
        updateObj.setTzshjbsj(new Date());

        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void zsjcsjd(ZyjwzxZsjcsjdReqVO zsjcsjdReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(zsjcsjdReqVO.getId());
        if (!ZyjwzxStatusEnum.DZSJCSJD.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DZSJCSJD.getName().substring(1) + "】操作");
        }

        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(zsjcsjdReqVO, ZyjwzxDO.class);

        updateObj.setStatus(ZyjwzxStatusEnum.DGAJGGS.getCode());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setZsjcsjdjbmjsfzh(sessionUser.getIdCard());
        updateObj.setZsjcsjdjbmjxm(sessionUser.getName());
        updateObj.setZsjcsjdjbsj(new Date());

        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void gajggs(ZyjwzxGajggsReqVO gajggsReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(gajggsReqVO.getId());
        if (!ZyjwzxStatusEnum.DGAJGGS.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DGAJGGS.getName().substring(1) + "】操作");
        }

        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(gajggsReqVO, ZyjwzxDO.class);

        updateObj.setStatus(ZyjwzxStatusEnum.DGAJGSP.getCode());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setGajgzwzgsjbmjsfzh(sessionUser.getIdCard());
        updateObj.setGajgzwzgsjbmjxm(sessionUser.getName());
        updateObj.setGajgzwzgsjdjbsj(new Date());

        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void gajgsp(ZyjwzxGajgspReqVO gajgspReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(gajgspReqVO.getId());
        if (!ZyjwzxStatusEnum.DGAJGSP.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DGAJGSP.getName().substring(1) + "】操作");
        }

        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(gajgspReqVO, ZyjwzxDO.class);

        // 公安机关审批结果（1、同意；2、不同意。）
        if ("2".equals(gajgspReqVO.getGajgspjg())) {
            updateObj.setStatus(ZyjwzxStatusEnum.YBJ.getCode());
        } else {
            updateObj.setStatus(ZyjwzxStatusEnum.DZFJFZX.getCode());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        updateObj.setGajgspjbmjsfzh(sessionUser.getIdCard());
        updateObj.setGajgspjbmjxm(sessionUser.getName());
        updateObj.setGajgspjdjbsj(new Date());
        zyjwzxDao.updateById(updateObj);
    }

    @Override
    public void zfjfzx(ZyjwzxZfjfzxReqVO zfjfzxReqVO) {
        // 校验存在
        ZyjwzxDO zyjwzxDO = validateZyjwzxExists(zfjfzxReqVO.getId());
        if (!ZyjwzxStatusEnum.DZFJFZX.getCode().equals(zyjwzxDO.getStatus())) {
            throw new ServerException("当前状态为【" + ZyjwzxStatusEnum.getByCode(zyjwzxDO.getStatus()).getName() + "】，" +
                    "不能进行【" + ZyjwzxStatusEnum.DZFJFZX.getName().substring(1) + "】操作");
        }
        // 更新
        ZyjwzxDO updateObj = BeanUtils.toBean(zfjfzxReqVO, ZyjwzxDO.class);
        updateObj.setStatus(ZyjwzxStatusEnum.YBJ.getCode());
        zyjwzxDao.updateById(updateObj);
    }


}
