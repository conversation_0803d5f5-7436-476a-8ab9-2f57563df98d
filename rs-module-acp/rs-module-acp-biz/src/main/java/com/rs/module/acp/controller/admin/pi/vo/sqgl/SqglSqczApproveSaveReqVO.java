package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情处置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqczApproveSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所情登记ID")
    @NotEmpty(message = "所情登记ID不能为空")
    private String sqdjId;

    @ApiModelProperty("审批情况列表")
    List<SqglSqczApproveChildSaveReqVO> approveInfoList;

}
