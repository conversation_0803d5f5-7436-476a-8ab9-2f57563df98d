package com.rs.module.acp.service.gj.riskassmt;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.*;
import com.rs.module.acp.dao.gj.GjRiskAssmtDao;
import com.rs.module.acp.dao.gj.RiskAssmtTodoDao;
import com.rs.module.acp.entity.gj.GjRiskAssmtDO;
import com.rs.module.acp.entity.gj.RiskAssmtTodoDO;
import com.rs.module.acp.enums.gj.GjRiskAssmtStatusEnum;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.enums.BjgrylxEnum;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.util.MsgUtil;
import com.rs.util.DicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

/**
 * 实战平台-管教业务-风险评估 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class GjRiskAssmtServiceImpl extends BaseServiceImpl<GjRiskAssmtDao, GjRiskAssmtDO> implements GjRiskAssmtService {

    @Resource
    private GjRiskAssmtDao gjRiskAssmtDao;

    private final String BUS_TYPE = "10";

    private String defKey = "guanjiaoyewufengxianpinggu";

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private BusTraceService busTraceService;

    @Resource
    private RiskAssmtTodoDao riskAssmtTodoDao;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createGjRiskAssmt(GjRiskAssmtSaveRegVO createReqVO) {
        // 插入
        GjRiskAssmtDO gjRiskAssmt = BeanUtils.toBean(createReqVO, GjRiskAssmtDO.class);
        gjRiskAssmt.setStatus(GjRiskAssmtStatusEnum.YDJ.getCode());
        gjRiskAssmtDao.insert(gjRiskAssmt);

        // 待前端-提供
        String msgUrl = StrUtil.format("/#/discipline/riskRank/index?curId={}&saveType=approve", gjRiskAssmt.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", gjRiskAssmt.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_FXPG.getCode());

        JSONObject result = BspApprovalUtil.commonStartProcess(defKey, gjRiskAssmt.getId(), "【审批】风险评估审核", msgUrl, variables, HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            gjRiskAssmt.setActInstId(bpmTrail.getString("actInstId"));
            gjRiskAssmt.setTaskId(bpmTrail.getString("taskId"));
            gjRiskAssmt.setStatus(GjRiskAssmtStatusEnum.DSP.getCode());
            gjRiskAssmtDao.updateById(gjRiskAssmt);
        } else {
            throw new ServerException("流程启动失败");
        }

        if(StrUtil.isNotBlank(createReqVO.getRiskAssmtTodoId())){
            RiskAssmtTodoDO riskAssmtTodo = riskAssmtTodoDao.selectById( createReqVO.getRiskAssmtTodoId());
            if(riskAssmtTodo == null){
                throw new ServerException("待评估id传入异常");
            }
            riskAssmtTodo.setStatus("1");
            riskAssmtTodoDao.updateById(riskAssmtTodo);
        }
        //待评估数据
        return gjRiskAssmt.getId();
    }

    @Override
    public void updateGjRiskAssmt(GjRiskAssmtSaveReqVO updateReqVO) {
        // 校验存在
        validateGjRiskAssmtExists(updateReqVO.getId());
        // 更新
        GjRiskAssmtDO updateObj = BeanUtils.toBean(updateReqVO, GjRiskAssmtDO.class);
        updateObj.setStatus(GjRiskAssmtStatusEnum.DSP.getCode());
        gjRiskAssmtDao.updateById(updateObj);
    }

    @Override
    public void deleteGjRiskAssmt(String id) {
        // 校验存在
        validateGjRiskAssmtExists(id);
        // 删除
        gjRiskAssmtDao.deleteById(id);
    }

    private void validateGjRiskAssmtExists(String id) {
        if (gjRiskAssmtDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-风险评估数据不存在");
        }
    }

    @Override
    public GjRiskAssmtRespVO getGjRiskAssmt(String id) {
        GjRiskAssmtDO gjRiskAssmtDO = gjRiskAssmtDao.selectById(id);
        if(gjRiskAssmtDO == null){
            return null;
        }
        GjRiskAssmtRespVO gjRiskAssmtRespVO = BeanUtils.toBean(gjRiskAssmtDO, GjRiskAssmtRespVO.class);
        if(HttpUtils.getAppCode().contains("kss") && StrUtil.isNotBlank(gjRiskAssmtRespVO.getAssmtReason() )){
            gjRiskAssmtRespVO.setAssmtReasonName(DicUtils.translate("GJ_FXPG_KSS_REASON", gjRiskAssmtRespVO.getAssmtReason()));
        } else if(  StrUtil.isNotBlank(gjRiskAssmtRespVO.getAssmtReason() )) {
            gjRiskAssmtRespVO.setAssmtReasonName(DicUtils.translate("GJ_FXPG_JLS_REASON", gjRiskAssmtRespVO.getAssmtReason()));
        }

        if(gjRiskAssmtRespVO.getApprovalResult() != null && BspApproceStatusEnum.PASSED_END.getCode() == Short.parseShort( gjRiskAssmtRespVO.getApprovalResult())){
            gjRiskAssmtRespVO.setApprovalResultName("同意");
        } else if(gjRiskAssmtRespVO.getApprovalResult() != null) {
            gjRiskAssmtRespVO.setApprovalResultName("不同意");
        }
        return gjRiskAssmtRespVO;
    }

    @Override
    public PageResult<GjRiskAssmtDO> getGjRiskAssmtPage(GjRiskAssmtPageReqVO pageReqVO) {
        return gjRiskAssmtDao.selectPage(pageReqVO);
    }

    @Override
    public List<GjRiskAssmtDO> getGjRiskAssmtList(GjRiskAssmtListReqVO listReqVO) {
        return gjRiskAssmtDao.selectList(listReqVO);
    }

    @Override
    public List<GjRiskAssmtRespVO> getGjRiskAssmtRespVOByJgrybm(String jgrybm) {
        List<GjRiskAssmtDO>  list = gjRiskAssmtDao.selectList(new LambdaQueryWrapperX<GjRiskAssmtDO>().eq(GjRiskAssmtDO::getJgrybm, jgrybm).orderByDesc(GjRiskAssmtDO::getAddTime));
        if(CollUtil.isNotEmpty(list)){
            return BeanUtils.toBean(list, GjRiskAssmtRespVO.class);
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderApprove(GjApproveReqVO gjApproveReqVO) {
        // 领导审批
        GjRiskAssmtDO entity =  gjRiskAssmtDao.selectById(gjApproveReqVO.getId());;
        if (entity == null) {
            throw new ServerException("信息不存在！");
        }
        GjRiskAssmtStatusEnum statusEnum = GjRiskAssmtStatusEnum.getByCode(entity.getStatus());
        if (!GjRiskAssmtStatusEnum.DSP.getCode().equals(entity.getStatus())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }
        BeanUtil.copyProperties(gjApproveReqVO, entity);
        String approvalResult = gjApproveReqVO.getApprovalResult();
        String status = null;
        String apprName = "风险评估", statusName = "已通过审批", passStatus =  "审批通过";
        String moduleCode = "GJ_RISK_ASSMT";
        String msgType = MsgBusTypeEnum.GJ_FXPG.getCode();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            status = GjRiskAssmtStatusEnum.SPTG.getCode();
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            //审批不通过，还是布建中
            status = GjRiskAssmtStatusEnum.SPBTG.getCode();
            statusName = "审批不通过";
            passStatus =  statusName;
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
        }

        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", entity.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_FXPG.getCode());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(defKey, entity.getActInstId(), entity.getTaskId(), entity.getId(),
                bspApproceStatusEnum, entity.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败");
        }

        //消息标题
        String title = apprName + passStatus ;
        entity.setStatus(status);
        MsgAddVO msg = new MsgAddVO();
        //TODO 等前端路由封装
        String msgUrl = StrUtil.format("/#/discipline/riskRank/index?curId={}&saveType=approve", entity.getId());
        msg.setUrl(msgUrl);
        msg.setModuleCode(moduleCode);
        //消息类型代码 字典编码对应：ZD_MSG_BUSTYP
        msg.setMsgType(msgType);
        msg.setTitle(title);
        msg.setOrgName(sessionUser.getOrgName());
        msg.setToOrgCode(sessionUser.getOrgCode());
        msg.setBusinessId(entity.getId());
        msg.setJgrybm(entity.getJgrybm());

        Map<String, Object> contentData = new HashMap<>();
        contentData.put("addTime", DateUtil.formatDateTime(entity.getAddTime()));
        contentData.put("addUserName", entity.getAddUserName());
        contentData.put("statusName", statusName);
        if(StrUtil.isNotBlank( entity.getRiskType())){
            contentData.put("riskTypeName",   DicUtils.translate("ZD_GJ_FXPG_STRICTLY", entity.getRiskType()));
        }
        if(StrUtil.isNotBlank(entity.getRiskLevel())){
            contentData.put("riskLevelName",   DicUtils.translate("ZD_GJ_FXPG_LEVEL", entity.getRiskLevel()));
        }
        msg.setContentData(contentData);
        msg.setPcid(entity.getId());
        MsgUtil.sendMsg(msg);
        entity.setStatus(status);

        entity.setApprovalComments(gjApproveReqVO.getApprovalComments());
        entity.setApproverXm(sessionUser.getName());
        entity.setApproverSfzh(sessionUser.getIdCard());
        entity.setApprovalResult(gjApproveReqVO.getApprovalResult());
        entity.setApproverTime(new Date());

        gjRiskAssmtDao.updateById(entity);


        if( BspApproceStatusEnum.PASSED_END.equals(bspApproceStatusEnum)){
            PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( entity.getJgrybm());
            String tableName;
            if (BjgrylxEnum.ZYRY.getCode().equals(prisoner.getBjgrylx())) {
                tableName = "acp_pm_prisoner_kss_in";
            } else if (BjgrylxEnum.ZJRY.getCode().equals(prisoner.getBjgrylx())) {
                tableName = "acp_pm_prisoner_jls_in";
            } else if (BjgrylxEnum.JDRY.getCode().equals(prisoner.getBjgrylx())) {
                tableName = "acp_pm_prisoner_jds_in";
            } else {
                throw new ServerException("非法监管人员类型");
            }
            gjRiskAssmtDao.updateRiskLevelByJgrybm(tableName, entity.getRiskLevel(), entity.getJgrybm());
            //被监管人员类型（01：在押人员，02：被拘人员，03：戒毒人员，99：其他）
            busTraceService.saveBusTrace(BusTypeEnum.YEWU_XJSY, GjBusTraceUtil.buildGjRiskAssmtBusTraceContent(entity,prisoner.getXm(), "02".equals( prisoner.getBjgrylx()) ),
                    entity.getJgrybm(),
                    SessionUserUtil.getSessionUser().getOrgCode(),
                   entity.getId());
        }

    }


    @Override
    public Boolean updateProcessStatus(String id, String status, String actInstId, String taskId) {
        GjRiskAssmtDO gjRiskAssmtDO = gjRiskAssmtDao.selectById(id);
        if(gjRiskAssmtDO == null){
            throw new ServerException("更新失败！");
        }
        gjRiskAssmtDO.setActInstId(actInstId);
        gjRiskAssmtDO.setStatus(status);
        gjRiskAssmtDO.setTaskId(taskId);
        if (gjRiskAssmtDao.updateById(gjRiskAssmtDO)> 0) {
            return true;
        }
        throw new ServerException("更新失败！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createReqVOList(List<GjRiskAssmtSaveRegVO> createReqVOList) {
        GjRiskAssmtService service = SpringUtils.getBean(GjRiskAssmtService.class);
        createReqVOList.forEach(service::createGjRiskAssmt);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchApprove(List<GjApproveReqVO> approveReqVOList) {
        GjRiskAssmtService service = SpringUtils.getBean(GjRiskAssmtService.class);
        approveReqVOList.forEach(service::leaderApprove);
    }



}
