package com.rs.module.acp.controller.admin.gj.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-专业法律指导反馈 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyflzdFeedbackReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("反馈内容")
    private String fknr;

    @ApiModelProperty("反馈附件")
    private String fkfj;


}
