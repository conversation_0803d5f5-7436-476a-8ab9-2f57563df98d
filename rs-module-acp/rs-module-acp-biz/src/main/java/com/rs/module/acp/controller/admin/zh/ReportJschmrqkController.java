package com.rs.module.acp.controller.admin.zh;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkRespVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkSaveReqVO;
import com.rs.module.acp.entity.zh.ReportJschmrqkDO;
import com.rs.module.acp.service.zh.ReportJschmrqkService;
import com.rs.util.DicUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "监所晨会每日情况")
@RestController
@RequestMapping("/acp/zh/reportJschmrqk")
@Validated
public class ReportJschmrqkController {

    @Resource
    private ReportJschmrqkService reportJschmrqkService;
    
    @Resource
    private FileStorageService fileStorageService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所晨会每日情况")
    public CommonResult<String> createReportJschmrqk(@Valid @RequestBody ReportJschmrqkSaveReqVO createReqVO) {
    	ReportJschmrqkDO reportJschmrqkDO = reportJschmrqkService.getByReportDate(createReqVO.getReportDate());
    	if(reportJschmrqkDO == null) {
    		return success(reportJschmrqkService.createReportJschmrqk(createReqVO));
    	}
    	else {
    		String reportDate = DateUtil.getDateFormat(reportJschmrqkDO.getReportDate(), "yyyy-MM-dd");
    		return CommonResult.error("【" + reportDate +  "】已经创建晨会每日情况，不允许重复创建！");
    	}        
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所晨会每日情况")
    public CommonResult<Boolean> updateReportJschmrqk(@Valid @RequestBody ReportJschmrqkSaveReqVO updateReqVO) {
    	ReportJschmrqkDO reportJschmrqkDO = reportJschmrqkService.getByReportDate(updateReqVO.getReportDate());
    	if(reportJschmrqkDO != null && !reportJschmrqkDO.getId().equals(updateReqVO.getId())) {
    		String reportDate = DateUtil.getDateFormat(reportJschmrqkDO.getReportDate(), "yyyy-MM-dd");
    		return CommonResult.error("【" + reportDate +  "】已经创建晨会每日情况，不允许重复创建！");
    	}
    	else {
    		reportJschmrqkService.updateReportJschmrqk(updateReqVO);
            return success(true);
    	}        
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所晨会每日情况")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteReportJschmrqk(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           reportJschmrqkService.deleteReportJschmrqk(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所晨会每日情况")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ReportJschmrqkRespVO> getReportJschmrqk(@RequestParam("id") String id) {
        ReportJschmrqkDO reportJschmrqk = reportJschmrqkService.getReportJschmrqk(id);
        return success(BeanUtils.toBean(reportJschmrqk, ReportJschmrqkRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所晨会每日情况分页")
    public CommonResult<PageResult<ReportJschmrqkRespVO>> getReportJschmrqkPage(@Valid @RequestBody ReportJschmrqkPageReqVO pageReqVO) {
        PageResult<ReportJschmrqkDO> pageResult = reportJschmrqkService.getReportJschmrqkPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ReportJschmrqkRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所晨会每日情况列表")
    public CommonResult<List<ReportJschmrqkRespVO>> getReportJschmrqkList(@Valid @RequestBody ReportJschmrqkListReqVO listReqVO) {
        List<ReportJschmrqkDO> list = reportJschmrqkService.getReportJschmrqkList(listReqVO);
        return success(BeanUtils.toBean(list, ReportJschmrqkRespVO.class));
    }
    
    @PostMapping("/buildReportJschmrqk")
    @ApiOperation(value = "生成监所晨会每日情况报告")
    @ApiImplicitParam(name = "id", value = "主键")
    public CommonResult<?> buildReportJschmrqk(@RequestParam("id") String id) {
    	try {
    		ReportJschmrqkDO reportJschmrqk = reportJschmrqkService.getReportJschmrqk(id);
    		if(reportJschmrqk != null) {
    			Date reportDate = reportJschmrqk.getReportDate();
    			String reportDateStr = DateUtil.getDateStr(reportDate, 2);
    			String orgCode = reportJschmrqk.getOrgCode();
    			
    			//初始化数据
    			JSONObject formData = buildBasicData(reportDate);
    			JSONObject subData = new JSONObject();
    			formData.put("mrqk", reportJschmrqk);
    			
    			//获取前24小时数据和截止当前数据
    			JSONObject dataOf24 = reportJschmrqkService.getDataOf24Hours(orgCode, reportDateStr);
    			JSONObject dataOfUpToNow = reportJschmrqkService.getDataOfUpToNow(orgCode);    			
    			JSONObject dataOf24Tj = reportJschmrqkService.getDataOf24Tj(orgCode, reportDateStr);
    			JSONObject dataOf24Tx = reportJschmrqkService.getDataOf24Tx(orgCode, reportDateStr);
    			JSONObject dataOf24Lshj = reportJschmrqkService.getDataOf24Lshj(orgCode, reportDateStr);
    			formData.putAll(dataOf24);
    			formData.putAll(dataOfUpToNow);
    			formData.putAll(dataOf24Tj);
    			formData.putAll(dataOf24Tx);
    			formData.putAll(dataOf24Lshj);
    			
    			//收押情况-出入所人员    			
    			List<Map<String, Object>> syqkRsry = reportJschmrqkService.getSyqk24Rsry(orgCode, reportDateStr);
    			List<Map<String, Object>> syqkCsry = reportJschmrqkService.getSyqk24Csry(orgCode, reportDateStr);
    			subData.put("syqkRsrySub", syqkRsry);
    			subData.put("syqkCsrySub", syqkCsry);
    			formData.put("syqkRsryZs", syqkRsry.size());
    			formData.put("syqkCsryZs", syqkCsry.size());
    			
    			//在押情况-65岁以上人员、加戴械具、单独关押、关注群体人员、精神异常、吞食异物、特殊身份
    			List<Map<String, Object>> zyqkAbove65 = reportJschmrqkService.getZyqk24Above65Years(orgCode, reportDateStr);
    			List<Map<String, Object>> zyqkJdxj = reportJschmrqkService.getZyqk24Jdxj(orgCode, reportDateStr);
    			List<Map<String, Object>> zyqkDdgy = reportJschmrqkService.getZyqk24Ddgy(orgCode, reportDateStr);
    			List<Map<String, Object>> zyqkGzqtry = reportJschmrqkService.getZyqk24Gzqtry(orgCode, reportDateStr);
    			List<Map<String, Object>> zyqkJsyc = reportJschmrqkService.getZyqk24Jsyc(orgCode, reportDateStr);
    			List<Map<String, Object>> zyqkTsyw = reportJschmrqkService.getZyqk24Tsyw(orgCode, reportDateStr);
    			List<Map<String, Object>> zyqkTssf = reportJschmrqkService.getZyqk24Tssf(orgCode, reportDateStr);
    			formData.put("zyqkAbove65Zs", zyqkAbove65.size());
    			formData.put("zyqkAbove65Data", buildZyryData(zyqkAbove65));
    			formData.put("zyqkJdxjZs", zyqkJdxj.size());
    			formData.put("zyqkJdxjData", buildZyryData(zyqkJdxj));
    			formData.put("zyqkDdgyZs", zyqkDdgy.size());
    			formData.put("zyqkDdgyData", buildZyryData(zyqkDdgy));
    			formData.put("zyqkGzqtryZs", zyqkGzqtry.size());
    			formData.put("zyqkGzqtryData", buildZyryData(zyqkGzqtry));
    			formData.put("zyqkJsycZs", zyqkJsyc.size());
    			formData.put("zyqkJsycData", buildZyryData(zyqkJsyc));
    			formData.put("zyqkTsywZs", zyqkTsyw.size());
    			formData.put("zyqkTsywData", buildZyryData(zyqkTsyw));
    			formData.put("zyqkTssfZs", zyqkTssf.size());
    			formData.put("zyqkTssfData", buildZyryData(zyqkTssf));
    			
    			//外籍人员管控情况-国籍分布情况、涉嫌犯罪类型
    			List<Map<String, Object>> wjrygkGjfb = reportJschmrqkService.getWjrygkGjfb(orgCode, reportDateStr);
    			List<Map<String, Object>> wjrygkSxfzlx = reportJschmrqkService.getWjrygkSxfzlx(orgCode, reportDateStr);
    			formData.put("wjrygkGjfbData", buildWjryData(wjrygkGjfb, "gj"));
    			formData.put("wjrygkSxfzlxData", buildWjryData(wjrygkSxfzlx, "sxzm"));
    			
    			//服务办案工作情况-提讯、提解、律师接待、使馆会见
    			List<Map<String, Object>> fwbagzqk24Tx = reportJschmrqkService.getFwbagzqk24Tx(orgCode, reportDateStr);
    			List<Map<String, Object>> fwbagzqk24Tj = reportJschmrqkService.getFwbagzqk24Tj(orgCode, reportDateStr);
    			List<Map<String, Object>> fwbagzqk24Lsjd = reportJschmrqkService.getFwbagzqk24Lsjd(orgCode, reportDateStr);
    			List<Map<String, Object>> fwbagzqk24Sghj = reportJschmrqkService.getFwbagzqk24Sghj(orgCode, reportDateStr);
    			formData.put("fwbagzqk24TxZs", fwbagzqk24Tx.size());
    			formData.put("fwbagzqk24TjZs", fwbagzqk24Tj.size());
    			formData.put("fwbagzqk24LsjdZs", fwbagzqk24Lsjd.size());
    			formData.put("fwbagzqk24SghjZs", fwbagzqk24Sghj.size());
    			
    			//重点风险人员管控情况-三级风险、医疗重点人员及住院人员具体情况
    			Map<String, Object> zdfxry24Sjfx = reportJschmrqkService.getZdfxry24Sjfx(orgCode, reportDateStr);
    			List<Map<String, Object>> zdfxry24Zyqk = reportJschmrqkService.getZdfxry24Zyqk(orgCode, reportDateStr);
    			Long yjfxZs = (Long)zdfxry24Sjfx.get("yjfx");
    			Long ejfxZs = (Long)zdfxry24Sjfx.get("ejfx");
    			Long sjfxZs = (Long)zdfxry24Sjfx.get("sjfx");    			
    			formData.put("zdfxry24YjfxZs", yjfxZs);
    			formData.put("zdfxry24EjfxZs", ejfxZs);
    			formData.put("zdfxry24SjfxZs", sjfxZs);
    			formData.put("zdfxry24FxZs", yjfxZs + ejfxZs + sjfxZs);
    			subData.put("zdfxry24ZyqkSub", zdfxry24Zyqk);
    			
    			//其他基础数据-巡诊发药、所内治疗、以绝食、拒绝医疗等方式对抗监管情况、一级人员、二级人员、三级人员
    			Integer jcsj24XzfyZs = reportJschmrqkService.getJcsj24Xzfy(orgCode, reportDateStr);
    			Integer jcsj24SnzlZs =  reportJschmrqkService.getJcsj24Snzl(orgCode, reportDateStr);
    			Integer jcsj24DkjgZs =  reportJschmrqkService.getJcsj24Dkjg(orgCode, reportDateStr);
    			List<Map<String, Object>> jcsj24Yj = reportJschmrqkService.getJcsj24Yj(orgCode, reportDateStr);
    			List<Map<String, Object>> jcsj24Ej = reportJschmrqkService.getJcsj24Ej(orgCode, reportDateStr);
    			List<Map<String, Object>> jcsj24Sj = reportJschmrqkService.getJcsj24Sj(orgCode, reportDateStr);
    			formData.put("jcsj24XzfyZs", jcsj24XzfyZs);
    			formData.put("jcsj24SnzlZs", jcsj24SnzlZs);
    			formData.put("jcsj24DkjgZs", jcsj24DkjgZs);
    			formData.put("jcsj24YjZs", jcsj24Yj.size());
    			formData.put("jcsj24EjZs", jcsj24Ej.size());
    			formData.put("jcsj24SjZs", jcsj24Sj.size());
    			subData.put("jcsj24YjSub", jcsj24Yj);
    			subData.put("jcsj24EjSub", jcsj24Ej);
    			subData.put("jcsj24SjSub", jcsj24Sj);
    			formData.put("subData", subData);
    			
        		return CommonResult.success(formData);
    		}
    		else {
    			return CommonResult.error("【监所晨会每日情况】生成报告异常！异常信息：找不到业务数据");
    		}
    	}
    	catch(Exception e) {
    		return CommonResult.error("【监所晨会每日情况】生成报告异常！异常信息：" + e.getMessage());
    	}
    }
    
    @PostMapping("/uploadReportJschmrqk")
    @ApiOperation(value = "上传监所晨会每日情况报告")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "id", value = "主键"),
    	@ApiImplicitParam(name = "isPdf", value = "是否pdf(true:pdf,false:word)"),
    	@ApiImplicitParam(name = "file", value = "报告文件")
    })    
    public CommonResult<?> uploadReportJschmrqk(@RequestParam("id") String id,
    		@RequestParam(value = "isPdf", defaultValue = "false") boolean isPdf, 
    		@RequestParam("file") MultipartFile file) {
    	try {
    		byte[] reportBytes = file.getBytes();
    		if(reportBytes != null && reportBytes.length > 0) {
    			ReportJschmrqkDO reportJschmrqk = reportJschmrqkService.getReportJschmrqk(id);
        		if(reportJschmrqk != null) {
        			String fileId = StringUtil.getGuid32();
        			String fileName = fileId + (isPdf ? ".pdf" : ".docx");
        			FileInfo fileInfo = fileStorageService.of(reportBytes)
        					.setSaveFilename(fileName)
        					.setObjectId(fileId)
        					.upload();
        			reportJschmrqk.setStatus("1");
        			reportJschmrqk.setWordUrl(fileInfo.getUrl());
        			reportJschmrqkService.updateById(reportJschmrqk);
        			
        			return CommonResult.success("【上传监所晨会每日情况报告】上传成功！");
        		}
        		else {
        			return CommonResult.error("【上传监所晨会每日情况报告】上传失败！异常信息：找不到记录");
        		}	
    		}
    		else {
    			return CommonResult.error("【上传监所晨会每日情况报告】上传失败！异常信息：上传文件为空");
    		}
    		
    	}
    	catch(Exception e) {
    		return CommonResult.error("【监所晨会每日情况报告】上传失败！异常信息：" + e.getMessage());
    	}
    }
    
    /**
     * 构建基础数据
     * @param reportDate Date 报告日期
     * @return JSONObject
     */
    private JSONObject buildBasicData(Date reportDate) {
		JSONObject jsonData = new JSONObject();
		
		//时间变量
		Calendar c = Calendar.getInstance();
		c.setTime(reportDate);
		int dayOfYear = c.get(Calendar.DAY_OF_YEAR);
		jsonData.put("reportDayOfYear", dayOfYear);
		jsonData.put("reportDateEnd", DateUtil.doFormatDate("M月d日", c.getTime()));
		jsonData.put("reportDateC", DateUtil.doFormatDate("yyyy年M月d日", c.getTime()));
		c.add(Calendar.DAY_OF_YEAR, -1);
		jsonData.put("reportDateStart", DateUtil.doFormatDate("M月d日", c.getTime()));
		
		return jsonData;
    }
    
    /**
     * 构建在押人员数据
     * @param listMap List<Map<String, Object>> 结果集集合
     * @return String
     */
    private String buildZyryData(List<Map<String, Object>> listMap) {
    	StringBuffer sb = new StringBuffer();
    	if(CollectionUtil.isNotNull(listMap)) {
    		for(Map<String, Object> map : listMap) {
    			if(map.containsKey("xm")) {
    				if(StringUtil.isNotEmpty(sb.toString())) {
    					sb.append(",");
    				}
    				sb.append(map.get("xm"));
    				if(map.containsKey("room_name")) {
    					String roomName = (String)map.get("room_name");
    					if(StringUtil.isNotEmpty(roomName)) {
    						sb.append("(" + roomName + ")");
    					}
    				}
    			}
    		}
    	}
    	
    	return sb.toString();
    }
    
    /**
     * 构建外籍人员数据
     * @param listMap List<Map<String, Object>> 结果集集合
     * @param propName String 属性名称
     * @return String
     */
    private String buildWjryData(List<Map<String, Object>> listMap, String propName) {
    	StringBuffer sb = new StringBuffer();
    	if(CollectionUtil.isNotNull(listMap)) {
    		for(Map<String, Object> map : listMap) {
    			if(map.containsKey(propName) && map.containsKey("zs")) {
    				String propValue = (String)map.get(propName);
    				Long zs = (Long)map.get("zs");
    				if(StringUtil.isNotEmpty(propValue)) {
    					if(propName.equalsIgnoreCase("gj")) {
    						propValue = DicUtils.translate("ZD_GABBZ_GJ", propValue);
    						if(StringUtil.isEmpty(propValue)) {
    							propValue = DicUtils.translate("ZD_GAJGSJHJBZ_GJ", propValue);
    						}
    					}
    					if(StringUtil.isNotEmpty(sb.toString())) {
        					sb.append(",");
        				}
    					sb.append(propValue + zs + "人");
    				}
    			}
    		}
    	}
    	
    	return sb.toString();
    }
}
