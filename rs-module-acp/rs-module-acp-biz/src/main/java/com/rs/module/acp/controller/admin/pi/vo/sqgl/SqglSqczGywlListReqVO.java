package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情处置关联业务列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqczGywlListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所情处置ID")
    private String sqczId;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("业务主表id")
    private String businessId;

}
