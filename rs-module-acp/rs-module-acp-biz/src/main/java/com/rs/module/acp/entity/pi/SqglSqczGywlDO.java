package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-所情处置关联业务 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_sqcz_gywl")
@KeySequence("acp_pi_sqgl_sqcz_gywl_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_sqcz_gywl")
public class SqglSqczGywlDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所情处置ID
     */
    private String sqczId;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务主表id
     */
    private String businessId;

}
