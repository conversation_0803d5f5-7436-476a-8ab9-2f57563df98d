package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.FwdbxtSaveReqVO;
import com.rs.module.acp.dao.gj.FwdbxtDao;
import com.rs.module.acp.dao.gj.FwdbxtWsxxDao;
import com.rs.module.acp.entity.gj.FwdbxtDO;
import com.rs.module.acp.entity.gj.FwdbxtWsxxDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 实战平台-管教业务-法务待办协同 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FwdbxtServiceImpl extends BaseServiceImpl<FwdbxtDao, FwdbxtDO> implements FwdbxtService {

    @Resource
    private FwdbxtDao fwdbxtDao;
    @Resource
    private FwdbxtWsxxDao fwdbxtWsxxDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createFwdbxt(FwdbxtSaveReqVO createReqVO) {
        // 插入
        FwdbxtDO fwdbxt = BeanUtils.toBean(createReqVO, FwdbxtDO.class);
        fwdbxtDao.insert(fwdbxt);

        // 插入子表
        createFwdbxtWsxxList(fwdbxt.getId(), createReqVO.getFwdbxtWsxxs());
        // 返回
        return fwdbxt.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFwdbxt(FwdbxtSaveReqVO updateReqVO) {
        // 校验存在
        validateFwdbxtExists(updateReqVO.getId());
        // 更新
        FwdbxtDO updateObj = BeanUtils.toBean(updateReqVO, FwdbxtDO.class);
        fwdbxtDao.updateById(updateObj);

        // 更新子表
        updateFwdbxtWsxxList(updateReqVO.getId(), updateReqVO.getFwdbxtWsxxs());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFwdbxt(String id) {
        // 校验存在
        validateFwdbxtExists(id);
        // 删除
        fwdbxtDao.deleteById(id);

        // 删除子表
        deleteFwdbxtWsxxByFwdbxtId(id);
    }

    private void validateFwdbxtExists(String id) {
        if (fwdbxtDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-法务待办协同数据不存在");
        }
    }

    @Override
    public FwdbxtDO getFwdbxt(String id) {
        return fwdbxtDao.selectById(id);
    }

    // ==================== 子表（实战平台-管教业务-法务待办协同关联文书信息） ====================

    @Override
    public List<FwdbxtWsxxDO> getFwdbxtWsxxListByFwdbxtId(String fwdbxtId) {
        return fwdbxtWsxxDao.selectListByFwdbxtId(fwdbxtId);
    }

    private void createFwdbxtWsxxList(String fwdbxtId, List<FwdbxtWsxxDO> list) {
        list.forEach(o -> o.setFwdbxtId(fwdbxtId));
        list.forEach(o -> fwdbxtWsxxDao.insert(o));
    }

    private void updateFwdbxtWsxxList(String fwdbxtId, List<FwdbxtWsxxDO> list) {
        deleteFwdbxtWsxxByFwdbxtId(fwdbxtId);
		list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createFwdbxtWsxxList(fwdbxtId, list);
    }

    private void deleteFwdbxtWsxxByFwdbxtId(String fwdbxtId) {
        fwdbxtWsxxDao.deleteByFwdbxtId(fwdbxtId);
    }

}
