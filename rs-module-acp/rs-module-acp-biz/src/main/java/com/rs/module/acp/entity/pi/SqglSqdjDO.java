package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-所情登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_sqdj")
@KeySequence("acp_pi_sqgl_sqdj_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_sqdj")
public class SqglSqdjDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所情编号
     */
    private String eventCode;
    /**
     * 所情等级，字典：
     */
    private String eventLevel;
    /**
     * 所情地点
     */
    private String areaId;
    /**
     * 所情地点名称
     */
    private String areaName;
    /**
     * 发生时间
     */
    private Date happenTime;
    /**
     * event_template_id
     */
    private String eventTemplateId;
    /**
     * 所情名称
     */
    private String eventName;
    /**
     * 所情类型
     */
    private String eventType;
    /**
     * 所情开始时间（精确时间-开始）
     */
    private Date eventStartTime;
    /**
     * 所情结束时间（精确时间-结束）
     */
    private Date eventEndTime;
    /**
     * 所情详情
     */
    private String eventDetails;
    /**
     * 推送对象，存JSON
     */
    private String pushObject;
    /**
     * 状态，字典：
     */
    private String status;
    /**
     * 所请来源，字典：
     */
    private String eventSrc;

    /**
     * 巡控岗位处置人名称
     */
    private String handleUserName;
}
