package com.rs.module.acp.controller.admin.gj.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-错误信息更正新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CwxxgzSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监管人员身份证号")
    private String jgrysfzh;

    @ApiModelProperty("数据类型(01:推送,02:接收)")
    private String type;

    @ApiModelProperty("错误项")
    private String cwx;

    @ApiModelProperty("错误明细")
    private String cwmx;

    @ApiModelProperty("登记人身份证号")
    private String djrsfzh;

    @ApiModelProperty("登记人姓名")
    private String djrxm;

    @ApiModelProperty("登记人联系方式")
    private String djrlxdh;

    @ApiModelProperty("登记时间")
    private Date djsj;

    @ApiModelProperty("接收时间")
    private Date jssj;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("附件地址")
    private String attUrl;

}
