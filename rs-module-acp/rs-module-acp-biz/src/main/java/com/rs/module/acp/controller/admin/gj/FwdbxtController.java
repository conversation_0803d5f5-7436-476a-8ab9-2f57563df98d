package com.rs.module.acp.controller.admin.gj;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.cons.CommonConstants;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.FwdbxtRespVO;
import com.rs.module.acp.controller.admin.gj.vo.FwdbxtSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.FwdbxtSignReqVO;
import com.rs.module.acp.controller.admin.gj.vo.FwdbxtWsxxRespVO;
import com.rs.module.acp.entity.gj.FwdbxtDO;
import com.rs.module.acp.entity.gj.FwdbxtWsxxDO;
import com.rs.module.acp.service.gj.FwdbxtService;
import com.rs.module.base.vo.FileReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管教业务-法务待办协同")
@RestController
@RequestMapping("/acp/gj/fwdbxt")
@Validated
public class FwdbxtController {

    @Resource
    private FwdbxtService fwdbxtService;

    @PostMapping("/create")
    @ApiOperation(value = "创建法务待办协同")
    public CommonResult<String> createFwdbxt(@Valid @RequestBody FwdbxtSaveReqVO createReqVO) {
        return success(fwdbxtService.createFwdbxt(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新法务待办协同")
    public CommonResult<Boolean> updateFwdbxt(@Valid @RequestBody FwdbxtSaveReqVO updateReqVO) {
        fwdbxtService.updateFwdbxt(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除法务待办协同")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteFwdbxt(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           fwdbxtService.deleteFwdbxt(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得法务待办协同")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<FwdbxtRespVO> getFwdbxt(@RequestParam("id") String id) {
        FwdbxtDO fwdbxt = fwdbxtService.getFwdbxt(id);
        FwdbxtRespVO respVO = BeanUtils.toBean(fwdbxt, FwdbxtRespVO.class);
        List<FwdbxtWsxxDO> wsxxList = fwdbxtService.getFwdbxtWsxxListByFwdbxtId(id);
        respVO.setWsxxList(BeanUtils.toBean(wsxxList, FwdbxtWsxxRespVO.class));
        respVO.setHcwsList(JSONObject.parseArray(fwdbxt.getHcws(), FileReqVO.class));
        return success(respVO);
    }

    @GetMapping("/signFor")
    @ApiOperation(value = "签收")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<Boolean> signFor(@RequestParam("id") String id) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        fwdbxtService.update(new LambdaUpdateWrapper<FwdbxtDO>()
                .eq(FwdbxtDO::getId, id)
                .set(FwdbxtDO::getQssj, new Date())
                .set(FwdbxtDO::getQsjbmjsfzh, sessionUser.getIdCard())
                .set(FwdbxtDO::getQsjbmjxm, sessionUser.getName())
                .set(FwdbxtDO::getStatus, "02"));
        return success(true);
    }

    @PostMapping("/backHaul")
    @ApiOperation(value = "回传")
    public CommonResult<Boolean> backHaul(@Valid @RequestBody FwdbxtSignReqVO reqVO) {
        FwdbxtDO fwdbxtDO = BeanUtils.toBean(reqVO, FwdbxtDO.class);
        if (CommonConstants.CONSTANTS_FASLE.equals(reqVO.getSfjq()))
            fwdbxtDO.setStatus("03");
        else
            fwdbxtDO.setStatus("04");
        fwdbxtService.updateById(fwdbxtDO);
        return success(true);
    }

    // ==================== 子表（实战平台-管教业务-法务待办协同关联文书信息） ====================

//    @GetMapping("/fwdbxt-wsxx/list-by-fwdbxt-id")
//    @ApiOperation(value = "获得实战平台-管教业务-法务待办协同关联文书信息列表")
//    @ApiImplicitParam(name = "fwdbxtId", value = "法务待办协同ID")
//    public CommonResult<List<FwdbxtWsxxDO>> getFwdbxtWsxxListByFwdbxtId(@RequestParam("fwdbxtId") String fwdbxtId) {
//        return success(fwdbxtService.getFwdbxtWsxxListByFwdbxtId(fwdbxtId));
//    }

}
