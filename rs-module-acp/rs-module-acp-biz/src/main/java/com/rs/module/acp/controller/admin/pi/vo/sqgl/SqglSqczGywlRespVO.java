package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情处置关联业务 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqczGywlRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所情处置ID")
    private String sqczId;
    @ApiModelProperty("业务类型，字典：ZD_JJKS_GLYW")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_GLYW")
    private String businessType;
    @ApiModelProperty("业务主表id")
    private String businessId;
}
