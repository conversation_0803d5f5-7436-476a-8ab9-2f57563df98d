package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.pm.vo.NotifyAssignSaveReqVO;
import com.rs.module.acp.entity.pm.NotifyAssignDO;
import com.rs.module.acp.entity.pm.NotifyAssignRecipientDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-通知交办 Service 接口
 *
 * <AUTHOR>
 */
public interface NotifyAssignService extends IBaseService<NotifyAssignDO>{

    /**
     * 创建实战平台-监管管理-通知交办
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createNotifyAssign(@Valid NotifyAssignSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-通知交办
     *
     * @param updateReqVO 更新信息
     */
    void updateNotifyAssign(@Valid NotifyAssignSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-通知交办
     *
     * @param id 编号
     */
    void deleteNotifyAssign(String id);

    /**
     * 获得实战平台-监管管理-通知交办
     *
     * @param id 编号
     * @return 实战平台-监管管理-通知交办
     */
    NotifyAssignDO getNotifyAssign(String id);

    /**
     * 消息处理
     * @param id
     * @param content
     */
    void dispose(String id, String content);


    // ==================== 子表（实战平台-监管管理-通知交办与收件人关联） ====================

    /**
     * 获得实战平台-监管管理-通知交办与收件人关联列表
     *
     * @param notifyAssignId 通知交办ID
     * @return 实战平台-监管管理-通知交办与收件人关联列表
     */
    List<NotifyAssignRecipientDO> getRecipientByNotifyId(String notifyAssignId);

}
