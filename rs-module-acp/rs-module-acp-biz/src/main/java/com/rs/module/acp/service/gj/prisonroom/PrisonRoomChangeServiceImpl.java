package com.rs.module.acp.service.gj.prisonroom;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.framework.common.util.validation.ValidationUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.*;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListReqVO;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListVO;
import com.rs.module.acp.dao.gj.PrisonRoomChangeDao;
import com.rs.module.acp.entity.gj.PrisonRoomChangeDO;
import com.rs.module.acp.enums.gj.PrisonRoomStatusEnum;
import com.rs.module.acp.enums.gj.TransitionRoomStatusEnum;
import com.rs.module.acp.service.ds.DSPrisonRoomChangeService;
import com.rs.module.acp.service.gj.InOutRecordsService;
import com.rs.module.acp.service.gj.transitionroom.TransitionRoomService;
import com.rs.module.acp.util.EquipmentUseUtil;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.AgeCalculatorUtil;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.util.MsgUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实战平台-管教业务--监室调整 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class PrisonRoomChangeServiceImpl extends BaseServiceImpl<PrisonRoomChangeDao, PrisonRoomChangeDO> implements PrisonRoomChangeService {

    @Resource
    private PrisonRoomChangeDao prisonRoomChangeDao;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private InOutRecordsService inOutRecordsService;

    @Resource
    private BusTraceService busTraceService;

    @Resource
    private TransitionRoomService transitionRoomService;

    @Resource
    @Lazy
    private DSPrisonRoomChangeService dsPrisonRoomChangeService;
    private String defKey = "jianshitiaozheng";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPrisonRoomChange(PrisonRoomChangeSaveReqVO createReqVO) {
        // 插入
        PrisonRoomChangeDO prisonRoomChange = BeanUtils.toBean(createReqVO, PrisonRoomChangeDO.class);
        prisonRoomChange.setStatus(PrisonRoomStatusEnum.YDJ.getCode());
        prisonRoomChange.setRoomChangeTime(new Date());
        prisonRoomChangeDao.insert(prisonRoomChange);

        //启动流程审批跳转链接
        String msgUrl = StrUtil.format("/#/discipline/adjustmentRoom?curId={}&saveType=approve", prisonRoomChange.getId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", prisonRoomChange.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_JSTZ.getCode());
        JSONObject result = BspApprovalUtil.commonStartProcess(defKey, prisonRoomChange.getId(), "【审批】监室调整", msgUrl, variables, HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") != HttpStatus.OK.value()) {
            throw new ServerException("流程启动失败"+result.getString("msg"));
        }
        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        prisonRoomChange.setActInstId(bpmTrail.getString("actInstId"));
        prisonRoomChange.setTaskId(bpmTrail.getString("taskId"));
        prisonRoomChange.setStatus(PrisonRoomStatusEnum.DSP.getCode());
        prisonRoomChange.setRoomChangeTime(new Date());
        prisonRoomChangeDao.updateById(prisonRoomChange);

        return prisonRoomChange.getId();
    }

    @Override
    public void updatePrisonRoomChange(PrisonRoomChangeSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonRoomChangeExists(updateReqVO.getId());
        // 更新
        PrisonRoomChangeDO updateObj = BeanUtils.toBean(updateReqVO, PrisonRoomChangeDO.class);
        prisonRoomChangeDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonRoomChange(String id) {
        // 校验存在
        validatePrisonRoomChangeExists(id);
        // 删除
        prisonRoomChangeDao.deleteById(id);
    }

    private void validatePrisonRoomChangeExists(String id) {
        if (prisonRoomChangeDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务--监室调整数据不存在");
        }
    }

    @Override
    public PrisonRoomChangeDO getPrisonRoomChange(String id) {
        return prisonRoomChangeDao.selectById(id);
    }

    @Override
    public PageResult<PrisonRoomChangeDO> getPrisonRoomChangePage(PrisonRoomChangePageReqVO pageReqVO) {
        return prisonRoomChangeDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonRoomChangeDO> getPrisonRoomChangeList(PrisonRoomChangeListReqVO listReqVO) {
        return prisonRoomChangeDao.selectList(listReqVO);
    }

    @Override
    public Boolean updateProcessStatus(PrisonRoomChangeProcessVO processVO) {
        PrisonRoomChangeDO prisonRoomChangeDO = prisonRoomChangeDao.selectById(processVO.getId());
        if(prisonRoomChangeDO == null){
            throw new ServerException("更新失败！");
        }
        prisonRoomChangeDO.setActInstId(processVO.getActInstId());
        prisonRoomChangeDO.setStatus(processVO.getStatus());
        prisonRoomChangeDO.setTaskId(processVO.getTaskId());
        if (prisonRoomChangeDao.updateById(prisonRoomChangeDO)> 0) {
            return true;
        }
        throw new ServerException("更新失败！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchCreatePrisonRoomChangeList(List<PrisonRoomChangeSaveReqVO> createReqVOList) {
        if(CollectionUtil.isNotEmpty(createReqVOList)){
            //List<PrisonRoomChangeDO> prisonRoomChangeDOList = BeanUtils.toBean(createReqVOList, PrisonRoomChangeDO.class);
            //prisonRoomChangeDOList.forEach( e-> e.setStatus(PrisonRoomStatusEnum.DSP.getCode()));
            List<String> list = new ArrayList<>();
            createReqVOList.forEach( createReqVO -> {

                PrisonRoomChangeDO prisonRoomChange = BeanUtils.toBean(createReqVO, PrisonRoomChangeDO.class);
                prisonRoomChange.setStatus(PrisonRoomStatusEnum.YDJ.getCode());
                prisonRoomChange.setRoomChangeTime(new Date());
                prisonRoomChange.setAddTime(new Date());
                prisonRoomChange.setId(null);
                prisonRoomChange.setRoomChangeTime(new Date());
                prisonRoomChangeDao.insert(prisonRoomChange);

                //启动流程审批跳转链接
                String msgUrl = StrUtil.format("/#/discipline/adjustmentRoom?curId={}&saveType=approve", prisonRoomChange.getId());
                Map<String, Object> variables = new HashMap<>();
                variables.put("ywbh", prisonRoomChange.getId());
                variables.put("busType", MsgBusTypeEnum.GJ_JSTZ.getCode());
                JSONObject result = BspApprovalUtil.commonStartProcess(defKey, prisonRoomChange.getId(), "【审批】监室调整", msgUrl, variables,HttpUtils.getAppCode());
                log.info("==========result:{}", result);
                if (result.getIntValue("code") == HttpStatus.OK.value()) {
                    JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
                    prisonRoomChange.setActInstId(bpmTrail.getString("actInstId"));
                    prisonRoomChange.setTaskId(bpmTrail.getString("taskId"));
                    prisonRoomChange.setStatus(PrisonRoomStatusEnum.DSP.getCode());
                    transitionRoomService.updateStatus(prisonRoomChange.getJgrybm(), TransitionRoomStatusEnum.JSDZZ);

                    prisonRoomChangeDao.updateById(prisonRoomChange);
                } else {
                    throw new ServerException("流程启动失败");
                }
                list.add(prisonRoomChange.getId());

            });
            return list;
        }
        return null;
    }

    @Override
    public Boolean batchUpdatePrisonRoomChange(List<PrisonRoomChangeSaveReqVO> updateReqVOList) {
        if(CollectionUtil.isNotEmpty(updateReqVOList)){
            updateReqVOList.forEach( e->{
                ValidationUtils.validate(e, PrisonRoomChangeSaveReqVO.class);
            });
        }
        List<String> ids = updateReqVOList.stream().map(PrisonRoomChangeSaveReqVO::getId).collect(Collectors.toList());
        List<PrisonRoomChangeDO> prisonRoomChangeDOList = prisonRoomChangeDao.selectList(new LambdaQueryWrapper<PrisonRoomChangeDO>().eq(PrisonRoomChangeDO::getId, ids));
        Map<String, PrisonRoomChangeDO> map = prisonRoomChangeDOList.stream().collect(Collectors.toMap(PrisonRoomChangeDO::getId, Function.identity(), (o1, o2) -> o1));
        for (PrisonRoomChangeSaveReqVO reqVO : updateReqVOList) {
            PrisonRoomChangeDO prisonRoomChangeDOTemp = map.get(reqVO.getId());
            Assert.notNull(reqVO, "批量更新id有误,{}", reqVO.getId());
            BeanUtils.copyProperties(reqVO, prisonRoomChangeDOTemp );
        }
        return this.saveBatch(prisonRoomChangeDOList);
    }

    @Override
    public Boolean batchUpdateProcessStatus(List<PrisonRoomChangeProcessVO> processVOList) {
        //校验
        if(CollectionUtil.isNotEmpty(processVOList)){
            processVOList.forEach( e->{
                ValidationUtils.validate(e, PrisonRoomChangeProcessVO.class);
            });
        }
        List<String> ids = processVOList.stream().map(PrisonRoomChangeProcessVO::getId).collect(Collectors.toList());
        List<PrisonRoomChangeDO> prisonRoomChangeDOList = prisonRoomChangeDao.selectList(new LambdaQueryWrapper<PrisonRoomChangeDO>().eq(PrisonRoomChangeDO::getId, ids));
        Map<String, PrisonRoomChangeDO> map = prisonRoomChangeDOList.stream().collect(Collectors.toMap(PrisonRoomChangeDO::getId, Function.identity(), (o1, o2) -> o1));
        for (PrisonRoomChangeProcessVO reqVO : processVOList) {
            PrisonRoomChangeDO prisonRoomChangeDOTemp = map.get(reqVO.getId());
            Assert.notNull(reqVO, "批量更新id有误,{}", reqVO.getId());
            BeanUtils.copyProperties(reqVO,prisonRoomChangeDOTemp );
        }
        return true;
    }

    @Override
    public PageResult<PrisonRoomChangeDO> getPrisonRoomChangeByJgrybm(String jgrybm,Long pageNo,Long pageSize) {
        Page<PrisonRoomChangeDO> page = new Page(pageNo, pageSize);
        Page<PrisonRoomChangeDO> roomChangeDOList = prisonRoomChangeDao.selectPage(page,
                new LambdaQueryWrapper<PrisonRoomChangeDO>().eq(PrisonRoomChangeDO::getJgrybm, jgrybm).eq(PrisonRoomChangeDO::getStatus, PrisonRoomStatusEnum.YWC.getCode()).orderByDesc(PrisonRoomChangeDO::getUpdateTime));
        return new PageResult<PrisonRoomChangeDO>(roomChangeDOList.getRecords(), roomChangeDOList.getTotal());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderApprove(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PrisonRoomChangeDO entity =  prisonRoomChangeDao.selectById(approveReqVO.getId());;
        if (entity == null) {
            throw new ServerException("监控信息不存在！");
        }
        PrisonRoomStatusEnum statusEnum = PrisonRoomStatusEnum.getByCode(entity.getStatus());
        if (!PrisonRoomStatusEnum.DSP.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }
        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
        }
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        entity.setApprovalComments(approveReqVO.getApprovalComments());
        entity.setApproverXm(sessionUser.getName());
        entity.setApproverSfzh(sessionUser.getIdCard());
        entity.setApprovalResult(approveReqVO.getApprovalResult());
        entity.setApproverTime(new Date());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", entity.getId());
        variables.put("busType", MsgBusTypeEnum.GJ_JSTZ.getCode());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(defKey, entity.getActInstId(), entity.getTaskId(), entity.getId(),
                bspApproceStatusEnum, entity.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败");
        }

        handleApprove(approveReqVO,entity);
        prisonRoomChangeDao.updateById(entity);

        if(bspApproceStatusEnum.getCode() == BspApproceStatusEnum.PASSED_END.getCode()){
            inOutRecordsService.initOutRecords(entity.getJgrybm(), entity.getOldRoomId(), "08", entity.getId());
            //过渡监室管理  更新状态信息 改为已解除
            transitionRoomService.updateStatus(entity.getJgrybm(), TransitionRoomStatusEnum.YJC);
        } else {
            transitionRoomService.updateStatus(entity.getJgrybm(), TransitionRoomStatusEnum.GDZ);
        }





    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchLeaderApprove(List<GjApproveReqVO> approveReqList) {
        //以最快完成的速度有限，性能暂时不考虑
        PrisonRoomChangeService service = SpringUtils.getBean(PrisonRoomChangeService.class);
        approveReqList.forEach(service::leaderApprove);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bringIntakeOutReg(PrisonRoomChangeInOutSaveReqVO changeInOutSaveReqVO) {
        PrisonRoomChangeDO prisonRoomChangeDO = prisonRoomChangeDao.selectById(changeInOutSaveReqVO.getId());
        if(prisonRoomChangeDO == null){
            throw new ServerException("带入带出失败 id传入有误！");
        }
        prisonRoomChangeDO.setIsChange(1);
        BeanUtil.copyProperties(changeInOutSaveReqVO,prisonRoomChangeDO );
        prisonRoomChangeDO.setStatus(PrisonRoomStatusEnum.YWC.getCode());
        if (prisonRoomChangeDao.updateById(prisonRoomChangeDO) == 0) {
            throw new ServerException("更新失败！");
        }

        InOutRecordsSaveReqVO inOutRecordsSaveReqVO = BeanUtils.toBean(changeInOutSaveReqVO, InOutRecordsSaveReqVO.class);
        inOutRecordsSaveReqVO.setBusinessId(prisonRoomChangeDO.getId());
        inOutRecordsSaveReqVO.setBusinessType("08");
        inOutRecordsSaveReqVO.setJgrybm(prisonRoomChangeDO.getJgrybm());
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerSelectCompomenOne(prisonRoomChangeDO.getJgrybm(), PrisonerQueryRyztEnum.ZS);
        inOutRecordsSaveReqVO.setJgryxm(vwRespVO.getXm());
        inOutRecordsSaveReqVO.setRoomId(prisonRoomChangeDO.getOldRoomId());
        inOutRecordsSaveReqVO.setInoutReason("08");
        //inOutRecordsSaveReqVO.setBusinessType("0800");
        inOutRecordsService.acpSaveInOutRecords(inOutRecordsSaveReqVO, prisonRoomChangeDO.getNewRoomId(),prisonRoomChangeDO.getReturnTime());

        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( prisonRoomChangeDO.getJgrybm());
        saveBusTraces(prisonRoomChangeDO, prisoner.getXm(),"");

        updatePersonRoomInfo(prisonRoomChangeDO, prisoner.getBjgrylx());

        try {
            inOutRecordsSaveReqVO.setInoutTime(prisonRoomChangeDO.getEscortingTime());
            dsPrisonRoomChangeService.updatePrisonRoomChangeForOutPerson(inOutRecordsSaveReqVO);
            inOutRecordsSaveReqVO.setInoutTime(prisonRoomChangeDO.getReturnTime());
            dsPrisonRoomChangeService.insertPrisonRoomChangeForOutPerson(inOutRecordsSaveReqVO,prisonRoomChangeDO.getNewRoomName(), StringUtil.getGuid());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInOutRecords(InOutRecordsACPSaveVO saveReqVO) {
        PrisonRoomChangeInOutSaveReqVO changeInOutSaveReqVO = BeanUtils.toBean(saveReqVO, PrisonRoomChangeInOutSaveReqVO.class);
        PrisonRoomChangeDO prisonRoomChangeDO = getPrisonRoomChangeBySourceBusinessId(saveReqVO.getId());
        if(prisonRoomChangeDO == null || StringUtil.isEmpty(prisonRoomChangeDO.getId()))
            throw new ServerException("根据业务ID查询监室调整记录异常！");
        //根据业务id 查询 监室调整记录ID
        changeInOutSaveReqVO.setId(prisonRoomChangeDO.getId());
        changeInOutSaveReqVO.setEscortingPolice(saveReqVO.getInoutPolice());
        changeInOutSaveReqVO.setEscortingTime(saveReqVO.getInoutTime());
        changeInOutSaveReqVO.setEscortingPoliceSfzh(saveReqVO.getInoutPoliceSfzh());
        changeInOutSaveReqVO.setReturnPolice(saveReqVO.getInoutPolice());
        changeInOutSaveReqVO.setReturnPoliceSfzh(saveReqVO.getInoutPoliceSfzh());
        changeInOutSaveReqVO.setReturnTime(saveReqVO.getInTime());

        prisonRoomChangeDO.setIsChange(1);
        BeanUtil.copyProperties(changeInOutSaveReqVO,prisonRoomChangeDO );
        prisonRoomChangeDO.setStatus(PrisonRoomStatusEnum.YWC.getCode());
        if (prisonRoomChangeDao.updateById(prisonRoomChangeDO) == 0) {
            throw new ServerException("更新失败！");
        }

        InOutRecordsSaveReqVO inOutRecordsSaveReqVO = BeanUtils.toBean(changeInOutSaveReqVO, InOutRecordsSaveReqVO.class);
        inOutRecordsSaveReqVO.setBusinessId(prisonRoomChangeDO.getId());
        inOutRecordsSaveReqVO.setBusinessType("08");
        inOutRecordsSaveReqVO.setJgrybm(prisonRoomChangeDO.getJgrybm());
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerSelectCompomenOne(prisonRoomChangeDO.getJgrybm(), PrisonerQueryRyztEnum.ZS);
        inOutRecordsSaveReqVO.setJgryxm(vwRespVO.getXm());
        inOutRecordsSaveReqVO.setRoomId(prisonRoomChangeDO.getOldRoomId());
        inOutRecordsSaveReqVO.setInoutReason("08");
        inOutRecordsService.acpSaveInOutRecords(inOutRecordsSaveReqVO, prisonRoomChangeDO.getNewRoomId(),prisonRoomChangeDO.getReturnTime());

        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( prisonRoomChangeDO.getJgrybm());
        saveBusTraces(prisonRoomChangeDO, prisoner.getXm(),"");

        updatePersonRoomInfo(prisonRoomChangeDO, prisoner.getBjgrylx());

        try {
            inOutRecordsSaveReqVO.setInoutTime(prisonRoomChangeDO.getEscortingTime());
            dsPrisonRoomChangeService.updatePrisonRoomChangeForOutPerson(inOutRecordsSaveReqVO);
            inOutRecordsSaveReqVO.setInoutTime(prisonRoomChangeDO.getReturnTime());
            inOutRecordsSaveReqVO.setRoomId(prisonRoomChangeDO.getNewRoomId());//设置为新监室
            dsPrisonRoomChangeService.insertPrisonRoomChangeForOutPerson(inOutRecordsSaveReqVO,prisonRoomChangeDO.getNewRoomName(), StringUtil.getGuid());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void appOutPerson(InOutRecordsSaveReqVO outRecordsSaveReqVO){
        PrisonRoomChangeDO prisonRoomChangeDO = prisonRoomChangeDao.selectById(outRecordsSaveReqVO.getBusinessId());
        if(prisonRoomChangeDO == null){
            throw new ServerException("带入带出失败 id传入有误！");
        }
        BeanUtil.copyProperties(outRecordsSaveReqVO,prisonRoomChangeDO);
        outRecordsSaveReqVO.setRoomId(prisonRoomChangeDO.getOldRoomId());
        if (prisonRoomChangeDao.updateById(prisonRoomChangeDO) == 0) {
            throw new ServerException("更新失败！");
        }
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( prisonRoomChangeDO.getJgrybm());
        saveBusTraces(prisonRoomChangeDO, prisoner.getXm(),"01");
        try {
            dsPrisonRoomChangeService.updatePrisonRoomChangeForOutPerson(outRecordsSaveReqVO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void appInPerson(InOutRecordsSaveReqVO inRecordsSaveReqVO) {
        PrisonRoomChangeDO prisonRoomChangeDO = prisonRoomChangeDao.selectById(inRecordsSaveReqVO.getBusinessId());
        if(prisonRoomChangeDO == null){
            throw new ServerException("带入带出失败 id传入有误！");
        }
        prisonRoomChangeDO.setIsChange(1);
        BeanUtil.copyProperties(inRecordsSaveReqVO,prisonRoomChangeDO);
        prisonRoomChangeDO.setStatus(PrisonRoomStatusEnum.YWC.getCode());
        if (prisonRoomChangeDao.updateById(prisonRoomChangeDO) == 0) {
            throw new ServerException("更新失败！");
        }
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm( prisonRoomChangeDO.getJgrybm());
        saveBusTraces(prisonRoomChangeDO, prisoner.getXm(),"02");
        updatePersonRoomInfo(prisonRoomChangeDO, prisoner.getBjgrylx());
        inRecordsSaveReqVO.setRoomId(prisonRoomChangeDO.getNewRoomId());
        try {
            dsPrisonRoomChangeService.insertPrisonRoomChangeForOutPerson(inRecordsSaveReqVO,prisonRoomChangeDO.getNewRoomName(), StringUtil.getGuid());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void saveBusTraces(PrisonRoomChangeDO prisonRoomChangeDO, String prisonerName,String types) {
        busTraceService.saveBusTrace(BusTypeEnum.YEWU_JSTZ, GjBusTraceUtil.buildRoomChangeBusTraceContent(prisonRoomChangeDO, prisonerName),
                prisonRoomChangeDO.getJgrybm(),
                SessionUserUtil.getSessionUser().getOrgCode(),
                prisonRoomChangeDO.getId());
        //写入调入监室记录
        if(!"01".equals(types))
        busTraceService.saveBusTrace(BusTypeEnum.YEWU_TRJS, GjBusTraceUtil.buildRoomChangeBusTraceContent(prisonRoomChangeDO, prisonerName),
                prisonRoomChangeDO.getJgrybm(),
                SessionUserUtil.getSessionUser().getOrgCode(),
                prisonRoomChangeDO.getId());
        //写入调出监室记录
        if(!"02".equals(types))
        busTraceService.saveBusTrace(BusTypeEnum.YEWU_TCJS, GjBusTraceUtil.buildRoomChangeBusTraceContent(prisonRoomChangeDO, prisonerName),
                prisonRoomChangeDO.getJgrybm(),
                SessionUserUtil.getSessionUser().getOrgCode(),
                prisonRoomChangeDO.getId());
    }

    private void updatePersonRoomInfo(PrisonRoomChangeDO prisonRoomChangeDO, String bjgrylx) {
        AreaPrisonRoomDO areaPrisonRoomDONew = areaPrisonRoomService.getById(prisonRoomChangeDO.getNewRoomId());
        String areaId = areaPrisonRoomDONew != null ? areaPrisonRoomDONew.getAreaId() : null;

        switch (bjgrylx) {
            case "01":
                prisonRoomChangeDao.updatePersonRoomInfoKss(prisonRoomChangeDO.getNewRoomId(), prisonRoomChangeDO.getNewRoomName(), prisonRoomChangeDO.getJgrybm(), areaId);
                break;
            case "02":
                prisonRoomChangeDao.updatePersonRoomInfoJls(prisonRoomChangeDO.getNewRoomId(), prisonRoomChangeDO.getNewRoomName(), prisonRoomChangeDO.getJgrybm(), areaId);
                break;
            case "03":
                prisonRoomChangeDao.updatePersonRoomInfoJds(prisonRoomChangeDO.getNewRoomId(), prisonRoomChangeDO.getNewRoomName(), prisonRoomChangeDO.getJgrybm(), areaId);
                break;
            default:
                prisonRoomChangeDao.updatePersonRoomInfoKss(prisonRoomChangeDO.getNewRoomId(), prisonRoomChangeDO.getNewRoomName(), prisonRoomChangeDO.getJgrybm(), areaId);
                prisonRoomChangeDao.updatePersonRoomInfoJls(prisonRoomChangeDO.getNewRoomId(), prisonRoomChangeDO.getNewRoomName(), prisonRoomChangeDO.getJgrybm(), areaId);
                prisonRoomChangeDao.updatePersonRoomInfoJds(prisonRoomChangeDO.getNewRoomId(), prisonRoomChangeDO.getNewRoomName(), prisonRoomChangeDO.getJgrybm(), areaId);
                break;
        }
    }

    @Override
    public List<GjApprovalTraceVO> getApproveTrack(String id) {

        List<GjApprovalTraceVO> result = new ArrayList<>();
        PrisonRoomChangeDO entity = prisonRoomChangeDao.selectById(id);
        if(entity == null){
            return result;
        }
        //戒具使用呈批-start
        GjApprovalTraceVO approvalTraceVOJjsysp = new GjApprovalTraceVO();
        approvalTraceVOJjsysp.setNodeKey("jstzsp");
        approvalTraceVOJjsysp.setNodeName("监室调整呈批");
        approvalTraceVOJjsysp.setNodeStatus(1);
        approvalTraceVOJjsysp.setNodeCreateTime(DateUtil.formatDateTime(entity.getAddTime()));
        List<GjApprovalTraceVO.TraceVO> nodeInfoList = new ArrayList<>();
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("调整原因").val(entity.getChangeReason()).build());
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("原监室").val(entity.getOldRoomName()).build());
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("新监室").val(entity.getNewRoomName()).build());
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("申请人").val(entity.getAddUserName()).build());
        nodeInfoList.add(GjApprovalTraceVO.TraceVO.builder().key("申请调整时间").val( DateUtil.formatDateTime(entity.getAddTime())).build());
        approvalTraceVOJjsysp.setNodeInfo(nodeInfoList);
        result.add(approvalTraceVOJjsysp);

        // 调整审批
        List<GjApprovalTraceVO> apprJjsyList = EquipmentUseUtil.converBspApprovalTrack(entity.getActInstId());
        if(CollUtil.isNotEmpty( apprJjsyList)){
            result.addAll(apprJjsyList);
        }

        if(PrisonRoomStatusEnum.YWC.getCode().equals(entity.getStatus())){
            GjApprovalTraceVO traceVO = new GjApprovalTraceVO();
            traceVO.setNodeKey("dcdrdj");
            traceVO.setNodeName("带出带入登记");
            traceVO.setNodeStatus(1);
            traceVO.setNodeCreateTime(DateUtil.formatDateTime(entity.getUpdateTime()));
            List<GjApprovalTraceVO.TraceVO> nodeInfoListTemp = new ArrayList<>();
            nodeInfoListTemp.add(GjApprovalTraceVO.TraceVO.builder().key("带出监室时间").val(DateUtil.formatDateTime(entity.getEscortingTime())).build());
            nodeInfoListTemp.add(GjApprovalTraceVO.TraceVO.builder().key("带出民警").val(entity.getOldRoomName()).build());
            nodeInfoListTemp.add(GjApprovalTraceVO.TraceVO.builder().key("带入时间").val( DateUtil.formatDateTime(entity.getReturnTime()) ).build());
            nodeInfoListTemp.add(GjApprovalTraceVO.TraceVO.builder().key("检查人").val(entity.getInspector()).build());
            nodeInfoListTemp.add(GjApprovalTraceVO.TraceVO.builder().key("检查结果").val( "1".equals(entity.getInspectionResult()) ? "正常" : "异常").build());
            traceVO.setNodeInfo(nodeInfoListTemp);
            result.add(traceVO);

        }


        return result;
    }

    @Override
    public void automaticGenerationRecord(PrisonRoomChangAutoRecordVO vo) {
        // 插入
        PrisonRoomChangeDO prisonRoomChange = BeanUtils.toBean(vo, PrisonRoomChangeDO.class);
        prisonRoomChange.setStatus(PrisonRoomStatusEnum.SPTGDTJ.getCode());
        prisonRoomChangeDao.insert(prisonRoomChange);
        inOutRecordsService.initOutRecords(prisonRoomChange.getJgrybm(), prisonRoomChange.getOldRoomId(), "08", prisonRoomChange.getSourceBusinessId());
    }

    @Override
    public PageResult<PrisonRoomChangeAppListVO> getPrisonRoomChangeAppList(PrisonRoomChangeAppListReqVO pageReqVO) {
        Page<PrisonRoomChangeAppListVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Date startTime = null,endTime = null;
        if(pageReqVO.getType() != null){
           Date [] array =  com.rs.util.DateUtil.getTimeRange(pageReqVO.getType());
            startTime = array[0];
            endTime = array[1];
        }
        IPage<PrisonRoomChangeAppListVO> listVOIPage = prisonRoomChangeDao.getPrisonRoomChangeAppList(page,sessionUser.getOrgCode(),startTime, endTime, pageReqVO.getRoomId() );
        for (PrisonRoomChangeAppListVO record : listVOIPage.getRecords()) {
            if(record.getCsrq() != null){
                record.setAge(AgeCalculatorUtil.calculateAge(record.getCsrq()));
            }

        }
        return new PageResult<>(listVOIPage.getRecords(), listVOIPage.getTotal());
    }

    /**
     * 处理审批信息
     * <AUTHOR>
     * @date 2025/5/29 10:42
     * @param [approveReqVO, entity]
     * @return void
     */
    private void handleApprove(GjApproveReqVO approveReqVO,PrisonRoomChangeDO entity){
        String approvalResult = approveReqVO.getApprovalResult();
        String status = null;
        String title = "监室调整审批通过", statusName = "已通过审批";
        String moduleCode = "GJ_PRISON_ROOM_CHANGE_APPR";
        if (String.valueOf( BspApproceStatusEnum.PASSED_END.getCode()).equals(approvalResult)) {
            status = PrisonRoomStatusEnum.SPTGDTJ.getCode();
        } else {
            status = PrisonRoomStatusEnum.SPBTG.getCode();
            statusName = "审批不通过";
            title = "监室调整审批不通过";
        }
        //String content = "预警消息内容";//消息内容
        entity.setStatus(status);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        entity.setStatus(status);
        MsgAddVO msg = new MsgAddVO();
        //msg.setUrl("");
        String msgUrl = StrUtil.format("/#/discipline/adjustmentRoom?curId={}&saveType=approve", entity.getId());
        msg.setUrl(msgUrl);
        msg.setModuleCode(moduleCode);
        //消息类型代码 字典编码对应：ZD_MSG_BUSTYP
        msg.setMsgType(MsgBusTypeEnum.GJ_JSTZ.getCode());
        msg.setTitle(title);
        msg.setOrgName(sessionUser.getOrgName());
        msg.setToOrgCode(sessionUser.getOrgCode());
        msg.setBusinessId(entity.getId());
        msg.setJgrybm(entity.getJgrybm());
        Map<String, Object> contentData = new HashMap<>();
        contentData.put("addTime", DateUtil.formatDateTime(entity.getAddTime()));
        contentData.put("addUserName", entity.getAddUserName());
        contentData.put("statusName", statusName);
        msg.setContentData(contentData);
        msg.setPcid(entity.getId());
        MsgUtil.sendMsg(msg);

    }

    //根据 sourceBusinessId 进行查询监室调整记录
    @Override
    public PrisonRoomChangeDO getPrisonRoomChangeBySourceBusinessId(String sourceBusinessId) {
        return prisonRoomChangeDao.selectOne(new LambdaQueryWrapper<>(PrisonRoomChangeDO.class)
                .eq(PrisonRoomChangeDO::getSourceBusinessId, sourceBusinessId)
                .eq(PrisonRoomChangeDO::getIsDel, 0));
    }

}
