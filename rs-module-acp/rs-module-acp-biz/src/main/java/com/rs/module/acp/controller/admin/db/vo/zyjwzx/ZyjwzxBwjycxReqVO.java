package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxBwjycxReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("保证人提出或者推荐人（1、亲属或者监护人；2、执行地村（居）民委员会；3、所在单位或者社区矫正机构）")
    private String bzrtshctjr;

    @ApiModelProperty("保证人证件类型:ZD_GABBZ_ZJZL")
    private String bzrzjlx;

    @ApiModelProperty("保证人证件号码")
    private String bzrzjhm;

    @ApiModelProperty("保证人姓名")
    private String bzrxm;

    @ApiModelProperty("保证人联系电话")
    private String bzrlxdh;

    @ApiModelProperty("保外就医审查结论（1、通过；2、未通过。）")
    private String bwjyscjl;

    @ApiModelProperty("保外就医审查备注")
    private String bwjyscbz;

    @ApiModelProperty("保外就医审查材料")
    private String bwjysccl;

}
