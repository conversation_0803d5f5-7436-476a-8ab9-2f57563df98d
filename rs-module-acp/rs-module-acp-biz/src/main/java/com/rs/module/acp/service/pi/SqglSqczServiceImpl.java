package com.rs.module.acp.service.pi;

import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglSqczDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.SqglSqczDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情管理-所情处置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqglSqczServiceImpl extends BaseServiceImpl<SqglSqczDao, SqglSqczDO> implements SqglSqczService {

    @Resource
    private SqglSqczDao sqglSqczDao;

    @Override
    public String createSqglSqcz(SqglSqczSaveReqVO createReqVO) {
        // 插入
        SqglSqczDO sqglSqcz = BeanUtils.toBean(createReqVO, SqglSqczDO.class);
        sqglSqczDao.insert(sqglSqcz);
        // 返回
        return sqglSqcz.getId();
    }

    @Override
    public void updateSqglSqcz(SqglSqczSaveReqVO updateReqVO) {
        // 校验存在
        validateSqglSqczExists(updateReqVO.getId());
        // 更新
        SqglSqczDO updateObj = BeanUtils.toBean(updateReqVO, SqglSqczDO.class);
        sqglSqczDao.updateById(updateObj);
    }

    @Override
    public void deleteSqglSqcz(String id) {
        // 校验存在
        validateSqglSqczExists(id);
        // 删除
        sqglSqczDao.deleteById(id);
    }

    private void validateSqglSqczExists(String id) {
        if (sqglSqczDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情管理-所情处置数据不存在");
        }
    }

    @Override
    public SqglSqczDO getSqglSqcz(String id) {
        return sqglSqczDao.selectById(id);
    }

    @Override
    public PageResult<SqglSqczDO> getSqglSqczPage(SqglSqczPageReqVO pageReqVO) {
        return sqglSqczDao.selectPage(pageReqVO);
    }

    @Override
    public List<SqglSqczDO> getSqglSqczList(SqglSqczListReqVO listReqVO) {
        return sqglSqczDao.selectList(listReqVO);
    }

}
