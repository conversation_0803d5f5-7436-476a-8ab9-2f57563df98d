package com.rs.module.acp.controller.admin.db;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.util.StringUtil;
import com.rs.module.acp.cons.ThirdBiometricTypeEnum;
import com.rs.module.acp.controller.admin.db.vo.third.haixin.FingerVO;
import com.rs.module.acp.controller.admin.db.vo.third.haixin.IrisVO;
import com.rs.module.acp.controller.admin.db.vo.third.haixin.PhotoVO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.BiometricInfoDO;
import com.rs.module.acp.service.db.BiometricInfoService;

@Api(tags = "实战平台-收押业务-生物特征信息")
@RestController
@RequestMapping("/acp/db/biometricInfo")
@Validated
public class BiometricInfoController {

    @Resource
    private BiometricInfoService biometricInfoService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-生物特征信息")
    public CommonResult<String> createBiometricInfo(@Valid @RequestBody BiometricInfoSaveReqVO createReqVO) {
        return success(biometricInfoService.createBiometricInfo(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-生物特征信息")
    public CommonResult<Boolean> updateBiometricInfo(@Valid @RequestBody BiometricInfoSaveReqVO updateReqVO) {
        biometricInfoService.updateBiometricInfo(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-生物特征信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBiometricInfo(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           biometricInfoService.deleteBiometricInfo(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-生物特征信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BiometricInfoRespVO> getBiometricInfo(@RequestParam("id") String id) {
        BiometricInfoDO biometricInfo = biometricInfoService.getBiometricInfo(id);
        return success(BeanUtils.toBean(biometricInfo, BiometricInfoRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-收押业务-生物特征信息分页")
    public CommonResult<PageResult<BiometricInfoRespVO>> getBiometricInfoPage(@Valid @RequestBody BiometricInfoPageReqVO pageReqVO) {
        PageResult<BiometricInfoDO> pageResult = biometricInfoService.getBiometricInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BiometricInfoRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-收押业务-生物特征信息列表")
    public CommonResult<List<BiometricInfoRespVO>> getBiometricInfoList(@Valid @RequestBody BiometricInfoListReqVO listReqVO) {
        List<BiometricInfoDO> list = biometricInfoService.getBiometricInfoList(listReqVO);
        return success(BeanUtils.toBean(list, BiometricInfoRespVO.class));
    }

    /**
     * 更新手环绑定信息
     */
    @PostMapping("/addInformation")
    @ApiOperation(value = "生物信息采集-提交/暂存")
    public CommonResult<ApprovalRespVO> updateWristbandInformation(@Valid @RequestBody BraceletBindingSaveReqVO updateReqVO) {
        boolean isApproval = biometricInfoService.updateWristbandInformation(updateReqVO);
        ApprovalRespVO approvalRespVO = new ApprovalRespVO();
        approvalRespVO.setIsApproval(isApproval);
        return success(approvalRespVO);
    }

    /**
     * 快速入所，更新手环绑定信息
     */
    @PostMapping("/quickInaddInformation")
    @ApiOperation(value = "快速入所-生物信息采集-提交/暂存")
    public CommonResult<ApprovalRespVO> updateWristbandInformationQuickIn(@Valid @RequestBody BraceletBindingSaveReqVO updateReqVO) {
        boolean isApproval = biometricInfoService.updateWristbandInfoQuick(updateReqVO);
        ApprovalRespVO approvalRespVO = new ApprovalRespVO();
        approvalRespVO.setIsApproval(isApproval);
        return success(approvalRespVO);
    }

    /**
     * 根据人员编号查询生物特征信息
     */
    @GetMapping("/getBiometricInfoByRybh")
    @ApiOperation(value = "根据人员编号查询生物特征信息")
    public CommonResult<List<BiometricInfoRespVO>> getBiometricInfoByRybh(@RequestParam("rybh") String rybh) {
        List<BiometricInfoDO> list = biometricInfoService.getBiometricInfoByRybh(rybh);
        List<BiometricInfoRespVO> respVOList = new ArrayList<>();
        for (BiometricInfoDO biometricInfoDO : list) {
            List<JSONObject> dataList = new ArrayList<>();
            BiometricInfoRespVO respVO = BeanUtils.toBean(biometricInfoDO, BiometricInfoRespVO.class);
            if (StringUtil.isNotEmpty(biometricInfoDO.getSwtz())) {
                ThirdBiometricTypeEnum dataType = ThirdBiometricTypeEnum.getByCode(biometricInfoDO.getCjxmlx());
                dataList = JSONObject.parseArray(biometricInfoDO.getSwtz(), JSONObject.class);
                switch (dataType) {
                    case FINGER:
                        for (JSONObject data : dataList) {
                            data.put("forceFlagName", DicUtil.translate("ZD_TYSFDM", data.getString("forceFlag")));
                            data.put("qsqkdmName", DicUtil.translate("ZD_HX_ZWCJ_QZQK", data.getString("qsqkdm")));
                            data.put("zwdmName", DicUtil.translate("ZD_HX_ZWCJ_ZWDM", data.getString("zwdm")));
                        }
                        break;
                    case IRIS:
                        for (JSONObject data : dataList) {
                            data.put("hmqsqkdmName", DicUtil.translate("ZD_HX_HMCJ_HMQSQK", data.getString("hmqsqkdm")));
                            data.put("hmywdmName", DicUtil.translate("ZD_HX_HMCJ_YWLX", data.getString("hmywdm")));
                            data.put("qzcjbsName", DicUtil.translate("ZD_TYSFDM", data.getString("qzcjbs")));
                        }
                        break;
                    case PHOTO:
                        for (JSONObject data : dataList) {
                            if ("31".equals(data.getString("ryzplxdm")))
                                continue;
                            data.put("forceCodeName", DicUtil.translate("ZD_HX_RXCJ_QZTGLX", data.getString("forceCode")));
                            data.put("ryzplxdmName", DicUtil.translate("ZD_HX_RXCJ_RXLX", data.getString("ryzplxdm")));
                        }
                        break;
                }
            }
            respVO.setBiometric(dataList);
            respVOList.add(respVO);
        }

        return success(respVOList);
    }

    /**
     * 获取待入所人员基本信息，包括看守所、拘留所、戒毒所
     * @param prisonerInfoReqVO
     * @return CollectedPersonDetailVO
     */
    @GetMapping("/getCollectedPersonDetail")
    public CommonResult<CollectedPersonDetailVO> getCollectedPersonDetail(@Validated prisonerInfoReqVO prisonerInfoReqVO) {
        CollectedPersonDetailVO collectedPersonDetailVO = biometricInfoService.getCollectedPersonDetail(prisonerInfoReqVO);

        return success(collectedPersonDetailVO);
    }

    /**
     * 回传信息接口
     */
    @PostMapping("/cjxx/upload")
    public ResponseEntity<Map<String, Object>> uploadCollectedData(@RequestBody Map<String, Object> request) {
        long startTime = System.currentTimeMillis(); // ⏰ 记录开始时间

        Map<String, Object> response = new HashMap<>();
        try {
            // 提取请求参数
            String jzrybh = (String) request.get("JZRYBH");
            String dataType = (String) request.get("dataType");
            Object data = request.get("data");
            String collectTimeStr = (String) request.get("collectTime");

            // 校验必要参数
            if (jzrybh == null || jzrybh.isEmpty()) {
                response.put("code", "400");
                response.put("message", "JZRYBH 不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            if (dataType == null || dataType.isEmpty()) {
                response.put("code", "400");
                response.put("message", "dataType 不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            if (data == null) {
                response.put("code", "400");
                response.put("message", "data 不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            if (collectTimeStr == null || collectTimeStr.isEmpty()) {
                response.put("code", "400");
                response.put("message", "collectTime 不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 解析采集时间
            LocalDateTime collectTime = LocalDateTime.parse(collectTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (collectTime == null) {
                response.put("code", "400");
                response.put("message", "collectTime 格式不正确，应为 yyyy-MM-dd HH:mm:ss");
                return ResponseEntity.badRequest().body(response);
            }

            // 业务处理（例如存储到数据库或调用服务层）
            biometricInfoService.uploadCollectedData(request);

            // 构造响应
            response.put("code", "200");
            long duration = System.currentTimeMillis() - startTime; // ⏱️ 计算耗时
            response.put("message", "上传用时" + duration + "毫秒");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("code", "500");
            response.put("message", "服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

}
