package com.rs.module.acp.dao.pi;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.entity.pi.SqglSqdjTbpzDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

import java.util.List;

/**
 * 实战平台-巡视管控-所情管理-所情登记-同步配置 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface SqglSqdjTbpzDao extends IBaseDao<SqglSqdjTbpzDO> {

    List<JSONObject> getSyncList(String sql);

}
