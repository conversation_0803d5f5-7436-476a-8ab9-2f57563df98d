package com.rs.module.acp.controller.admin.zh.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 早850监所晨会每日情况列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportJschmrqkListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("汇报日期")
    private Date[] reportDate;

    @ApiModelProperty("汇报人")
    private String reportUser;

    @ApiModelProperty("汇报单位")
    private String reportOrg;

    @ApiModelProperty("工作落实情况")
    private String gzlsqk;

    @ApiModelProperty("汇报突出情况")
    private String tcqk;

    @ApiModelProperty("word地址")
    private String wordUrl;

    @ApiModelProperty("pdf地址")
    private String pdfUrl;

    @ApiModelProperty("生成状态(0否1是)")
    private String status;

}
