package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxZdjdjcReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;


    @ApiModelProperty("诊断、鉴定或者检查类型（1、病情诊断；2、女性妊娠检查；3、正在哺乳自己婴儿的妇女证明；4、生活不能自理鉴别；5、短期内有生命危险诊断或者评估；6、久治不愈疾病诊断或者评估；7、严重功能障碍评估；8、精神疾病诊断或者评估）")
    private String zdjdhzjclx;

    @ApiModelProperty("诊断、鉴定或者检查结论（1、通过；2、未通过。）")
    private String zdjdhzjcjl;

    @ApiModelProperty("诊断、鉴定或者检查备注")
    private String zdjdhzjcbz;

    @ApiModelProperty("诊断、鉴定或者检查材料")
    private String zdjdhzjccl;


}
