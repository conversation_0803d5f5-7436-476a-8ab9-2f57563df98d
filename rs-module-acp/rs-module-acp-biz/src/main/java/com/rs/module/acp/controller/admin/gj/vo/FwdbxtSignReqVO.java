package com.rs.module.acp.controller.admin.gj.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.gj.FwdbxtWsxxDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-法务待办协同签收 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FwdbxtSignReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("是否拒签(0:否,1:是)")
    private String sfjq;

    @ApiModelProperty("拒签日期")
    private Date jqrq;

    @ApiModelProperty("拒签理由")
    private String jqly;

    @ApiModelProperty("回传文书")
    private String hcws;

}
