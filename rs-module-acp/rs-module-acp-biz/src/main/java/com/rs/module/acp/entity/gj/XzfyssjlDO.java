package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-行政复议讼诉记录 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_xzfyssjl")
@KeySequence("acp_gj_xzfyssjl_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_xzfyssjl")
public class XzfyssjlDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 被监管人编码
     */
    private String jgrybm;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private String status;
    /**
     * 经办人身份证号
     */
    private String handlePoliceSfzh;
    /**
     * 经办人
     */
    private String handlePoliceXm;
    /**
     * 经办时间
     */
    private Date handlePoliceTime;
    /**
     * 转递时间
     */
    private Date forwardTime;

}
