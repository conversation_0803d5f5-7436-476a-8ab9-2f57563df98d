package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-模板配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglMbpzRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所请来源，字典：ZD_JJKS_SQLY")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQLY")
    private String eventSrc;
    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("告警类型")
    private String alarmType;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("模板编号")
    private String templateCode;

    @ApiModelProperty("所情类型配置列表")
    private List<SqglMbpzSqlxRespVO> eventTypeSettingList;

    @ApiModelProperty("处置情况模板列表")
    private List<SqglMbpzSqczRespVO> handlingSituationList;

    @ApiModelProperty("推送对象配置列表")
    private List<SqglMbpzTsdxRespVO> pushObjectSettingList;
}
