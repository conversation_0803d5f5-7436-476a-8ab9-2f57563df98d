package com.rs.module.acp.service.db;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.db.vo.CsfwfSaveReqVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfInfoReqVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfPrisonerVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfRespVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfStatisticsVO;
import com.rs.module.acp.entity.db.CsfwfDO;

import java.util.List;

/**
 * 实战平台-收押业务-出所防误放 Service 接口
 *
 * <AUTHOR>
 */
public interface CsfwfService extends IBaseService<CsfwfDO> {


    /**
     * 看守所出所防误放
     *
     * @param jgrybm
     * @param businessId
     * @param businessType
     * @param businessReason
     * @return
     */
    String createKssCsFwf(String jgrybm, String businessId, String businessType, String businessReason);

    /**
     * 创建实战平台-收押业务-出所防误放
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCsfwf(CsfwfSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-出所防误放
     *
     * @param updateReqVO 更新信息
     */
    void updateCsfwf(CsfwfSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-出所防误放
     *
     * @param id 编号
     */
    void deleteCsfwf(String id);

    /**
     * 获得实战平台-收押业务-出所防误放
     *
     * @param id 编号
     * @return 实战平台-收押业务-出所防误放
     */
    CsfwfDO getCsfwf(String id);

    /**
     * 获取监所的待防误放统计数据
     *
     * @param orgCode
     * @param businessType
     * @return
     */
    List<AppCsFwfStatisticsVO> getCsfwfStatistics(String orgCode, String businessType);

    /**
     * 获取人员信息通过人员ID
     *
     * @param prisonerId
     * @return
     */
    AppCsFwfPrisonerVO getPrisonerByPrisonerId(String prisonerId);

    /**
     * 接收对比结果
     *
     * @param prisonerId
     * @param checkModel
     * @param checkResult
     */
    String receiveCompareResult(String prisonerId, String checkModel, String checkResult);

    /**
     * 完成对比
     *
     * @param reqVO
     * @return
     */
    String completeCompare(AppCsFwfInfoReqVO reqVO);

    /**
     * 获取实战平台-收押业务-出所防误放台账
     *
     * @param timeType
     * @param businessType
     * @param orgCode
     * @return
     */
    List<AppCsFwfRespVO> listFwfInfo(String timeType, String businessType, String orgCode);

    /**
     * 获取待核验人员列表
     *
     * @param orgCode
     * @return
     */
    List<AppCsFwfPrisonerVO> getWaitCheckPrisonerList(String orgCode);

    /**
     * 获取实战平台-收押业务-出所防误放， 通过业务ID
     * @param businessId
     * @return
     */
    CsfwfDO getCsfwfByBusinessId(String businessId);
}
