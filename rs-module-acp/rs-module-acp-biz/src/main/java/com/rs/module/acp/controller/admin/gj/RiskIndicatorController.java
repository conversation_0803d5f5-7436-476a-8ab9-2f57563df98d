package com.rs.module.acp.controller.admin.gj;

import cn.hutool.core.lang.Assert;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.*;
import io.swagger.annotations.ApiImplicitParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.RiskIndicatorDO;
import com.rs.module.acp.service.gj.riskIndicator.RiskIndicatorService;

@Api(tags = "实战平台-管教业务-风险指标")
@RestController
@RequestMapping("/acp/gj/riskIndicator")
@Validated
public class RiskIndicatorController {

    @Resource
    private RiskIndicatorService riskIndicatorService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-风险指标")
    public CommonResult<String> createRiskIndicator(@Valid @RequestBody RiskIndicatorSaveReqVO createReqVO) {
        return success(riskIndicatorService.createRiskIndicator(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-风险指标")
    public CommonResult<Boolean> updateRiskIndicator(@Valid @RequestBody RiskIndicatorSaveReqVO updateReqVO) {
        riskIndicatorService.updateRiskIndicator(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-风险指标")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteRiskIndicator(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            riskIndicatorService.deleteRiskIndicator(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-风险指标")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<RiskIndicatorRespVO> getRiskIndicator(@RequestParam("id") String id) {
        RiskIndicatorDO riskIndicator = riskIndicatorService.getRiskIndicator(id);
        return success(BeanUtils.toBean(riskIndicator, RiskIndicatorRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-风险指标分页")
    public CommonResult<PageResult<RiskIndicatorRespVO>> getRiskIndicatorPage(@Valid @RequestBody RiskIndicatorPageReqVO pageReqVO) {
        PageResult<RiskIndicatorDO> pageResult = riskIndicatorService.getRiskIndicatorPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RiskIndicatorRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-风险指标列表")
    public CommonResult<List<RiskIndicatorRespVO>> getRiskIndicatorList(@Valid @RequestBody RiskIndicatorListReqVO listReqVO) {
        if (StringUtils.isEmpty(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<RiskIndicatorDO> list = riskIndicatorService.getRiskIndicatorList(listReqVO);
        return success(BeanUtils.toBean(list, RiskIndicatorRespVO.class));
    }

    @GetMapping("/getReportByJgrybm")
    @ApiOperation(value = "获取报告")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
            @ApiImplicitParam(name = "type", value = "1 风险指标 2 加分指标 3 完整报告")
    })
    public CommonResult<RiskIndicatorAllInfoRespVO> getReportByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                    @RequestParam(value = "type", defaultValue = "1") String type) {
        Assert.notEmpty(jgrybm, "jgrybm不能为空");
        return success(riskIndicatorService.getReportByJgrybm(jgrybm, type));
    }


    @GetMapping("/getBjgryfxzbmo")
    @ApiOperation(value = "被监管人员风险指标模型")
    public CommonResult<List<RiskIndicatorBgRespVO>> getBjgryfxzbmo() {
        return success(riskIndicatorService.getBjgryfxzbmo());
    }
}
