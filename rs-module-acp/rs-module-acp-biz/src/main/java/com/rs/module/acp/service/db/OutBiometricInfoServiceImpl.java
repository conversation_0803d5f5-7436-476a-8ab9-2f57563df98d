package com.rs.module.acp.service.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.db.vo.OutBiometricInfoListReqVO;
import com.rs.module.acp.controller.admin.db.vo.OutBiometricInfoPageReqVO;
import com.rs.module.acp.controller.admin.db.vo.OutBiometricInfoRespVO;
import com.rs.module.acp.controller.admin.db.vo.OutBiometricInfoSaveReqVO;
import com.rs.module.acp.dao.db.OutBiometricInfoDao;
import com.rs.module.acp.entity.db.OutBiometricInfoDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * 实战平台-收押业务-出所生物特征信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutBiometricInfoServiceImpl extends BaseServiceImpl<OutBiometricInfoDao, OutBiometricInfoDO> implements OutBiometricInfoService {

    @Resource
    private OutBiometricInfoDao outBiometricInfoDao;

    @Override
    public String createOutBiometricInfo(OutBiometricInfoSaveReqVO createReqVO) {
        // 插入
        OutBiometricInfoDO outBiometricInfo = BeanUtils.toBean(createReqVO, OutBiometricInfoDO.class);
        outBiometricInfoDao.insert(outBiometricInfo);
        // 返回
        return outBiometricInfo.getId();
    }

    @Override
    public void updateOutBiometricInfo(OutBiometricInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateOutBiometricInfoExists(updateReqVO.getId());
        // 更新
        OutBiometricInfoDO updateObj = BeanUtils.toBean(updateReqVO, OutBiometricInfoDO.class);
        outBiometricInfoDao.updateById(updateObj);
    }

    @Override
    public void deleteOutBiometricInfo(String id) {
        // 校验存在
        validateOutBiometricInfoExists(id);
        // 删除
        outBiometricInfoDao.deleteById(id);
    }

    private void validateOutBiometricInfoExists(String id) {
        if (outBiometricInfoDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-出所生物特征信息数据不存在");
        }
    }

    @Override
    public OutBiometricInfoDO getOutBiometricInfo(String id) {
        return outBiometricInfoDao.selectById(id);
    }

    @Override
    public PageResult<OutBiometricInfoDO> getOutBiometricInfoPage(OutBiometricInfoPageReqVO pageReqVO) {
        return outBiometricInfoDao.selectPage(pageReqVO);
    }

    @Override
    public List<OutBiometricInfoDO> getOutBiometricInfoList(OutBiometricInfoListReqVO listReqVO) {
        return outBiometricInfoDao.selectList(listReqVO);
    }

    @Override
    public List<OutBiometricInfoRespVO> getByJgrybm(String jgrybm) {
        List<OutBiometricInfoDO> outBiometricInfo = outBiometricInfoDao.getByJgrybm(jgrybm);
        return BeanUtils.toBean(outBiometricInfo, OutBiometricInfoRespVO.class);
    }

    @Override
    public List<OutBiometricInfoDO> getByJgrybmAndHyjg(String jgrybm, String hyjg) {
        LambdaQueryWrapper<OutBiometricInfoDO> query = Wrappers.lambdaQuery(OutBiometricInfoDO.class);
        query.eq(OutBiometricInfoDO::getJgrybm, jgrybm);
        if (StrUtil.isNotBlank(hyjg)) {
            query.eq(OutBiometricInfoDO::getHyjg, hyjg);
        }
        List<OutBiometricInfoDO> outBiometricInfoDOS = outBiometricInfoDao.selectList(query);
        if (CollUtil.isNotEmpty(outBiometricInfoDOS)) {
            return outBiometricInfoDOS;
        }
        return Collections.emptyList();
    }


}
