package com.rs.module.acp.controller.admin.pi;

import com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor.FixedScreenMonitorRespVO;
import com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor.FixedScreenMonitorSaveReqVO;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.pi.FixedScreenMonitorDO;
import com.rs.module.acp.service.pi.FixedScreenMonitorService;

@Api(tags = "实战平台-巡视管控-定屏监控")
@RestController
@RequestMapping("/acp/pi/fixedScreenMonitor")
@Validated
public class FixedScreenMonitorController {

    @Resource
    private FixedScreenMonitorService fixedScreenMonitorService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-巡视管控-定屏监控")
    public CommonResult<String> createFixedScreenMonitor(@Valid @RequestBody FixedScreenMonitorSaveReqVO createReqVO) {
        return success(fixedScreenMonitorService.createFixedScreenMonitor(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-巡视管控-定屏监控")
    public CommonResult<Boolean> updateFixedScreenMonitor(@Valid @RequestBody FixedScreenMonitorSaveReqVO updateReqVO) {
        fixedScreenMonitorService.updateFixedScreenMonitor(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-巡视管控-定屏监控")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteFixedScreenMonitor(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           fixedScreenMonitorService.deleteFixedScreenMonitor(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-巡视管控-定屏监控")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<FixedScreenMonitorRespVO> getFixedScreenMonitor(@RequestParam("id") String id) {
        FixedScreenMonitorDO fixedScreenMonitor = fixedScreenMonitorService.getFixedScreenMonitor(id);
        return success(BeanUtils.toBean(fixedScreenMonitor, FixedScreenMonitorRespVO.class));
    }
    @PostMapping("/onScreen")
    @ApiOperation(value = "实战平台-巡视管控-定屏监控-上墙")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "ids", value = "编号 多个逗号分隔"),
            @ApiImplicitParam(name = "operaTime", value = "操作时间"),
    })
    public CommonResult<String> onScreen(@RequestParam("ids") String ids,@RequestParam(value = "id",required = false) Date operaTime) {
        fixedScreenMonitorService.updateStatusWithTimes(ids,"1",operaTime);
        return success("上墙成功！");
    }
    @PostMapping("/offScreen")
    @ApiOperation(value = "实战平台-巡视管控-定屏监控-下墙")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "id", value = "编号 多个逗号分隔"),
            @ApiImplicitParam(name = "operaTime", value = "操作时间"),
    })
    public CommonResult<String> offScreen(@RequestParam("ids") String ids,@RequestParam(value = "id",required = false) Date operaTime) {
        fixedScreenMonitorService.updateStatusWithTimes(ids,"1",operaTime);
        return success("上墙成功！");
    }

    @GetMapping("/testOnScreenBus")
    @ApiOperation(value = "实战平台-巡视管控-定屏监控-上墙测试")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "roomId", value = "监室编号"),
            @ApiImplicitParam(name = "dlpId", value = "电视墙ID"),
    })
    public CommonResult<String> testOnScreenBus(@RequestParam("roomId") String roomId,@RequestParam("dlpId") Integer dlpId) {
        try {
            fixedScreenMonitorService.onScreenBus(roomId,dlpId);
        } catch (Exception e) {
            return error(e.getMessage());
        }
        return success("上墙测试成功！");
    }
}
