package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.pm.vo.KnowledgeBaseSaveReqVO;
import com.rs.module.acp.entity.pm.KnowledgeBaseDO;

import javax.validation.Valid;

/**
 * 实战平台-监管管理-知识库 Service 接口
 *
 * <AUTHOR>
 */
public interface KnowledgeBaseService extends IBaseService<KnowledgeBaseDO>{

    /**
     * 创建实战平台-监管管理-知识库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createKnowledgeBase(@Valid KnowledgeBaseSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-知识库
     *
     * @param updateReqVO 更新信息
     */
    void updateKnowledgeBase(@Valid KnowledgeBaseSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-知识库
     *
     * @param id 编号
     */
    void deleteKnowledgeBase(String id);

    /**
     * 获得实战平台-监管管理-知识库
     *
     * @param id 编号
     * @return 实战平台-监管管理-知识库
     */
    KnowledgeBaseDO getKnowledgeBase(String id);


}
