package com.rs.module.acp.controller.app.pm;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfInfoReqVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfPrisonerVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfRespVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfStatisticsVO;
import com.rs.module.acp.service.db.CsfwfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @Date 2025/8/13 21:47
 */
@Api(tags = "实战平台-防误放终端")
@RestController
@RequestMapping("/app/acp/fwf")
@Validated
public class AppCsfwfController {

    @Resource
    private CsfwfService csfwfService;

    /**
     * 获取监所的待防误放统计数据
     */
    @GetMapping("/getCsfwfStatistics")
    @ApiOperation(value = "防误放-获取监所的待防误放统计数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码"),
            @ApiImplicitParam(name = "businessType", value = "业务类型（1：出所、2：入所）")
    })
    public CommonResult<List<AppCsFwfStatisticsVO>> getCsfwfStatistics(@RequestParam("orgCode") String orgCode,
                                                                       @RequestParam("businessType") String businessType) {
        return success(csfwfService.getCsfwfStatistics(orgCode, businessType));
    }

    /**
     * 验证通过后，获取人员信息通过人员ID
     */
    @GetMapping("/getPrisonerByPrisonerId")
    @ApiOperation(value = "防误放-验证通过后，获取人员信息通过人员ID")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "prisonerId", value = "人员ID")
    })
    public CommonResult<AppCsFwfPrisonerVO> getPrisonerByPrisonerId(@RequestParam("prisonerId") String prisonerId) {
        return success(csfwfService.getPrisonerByPrisonerId(prisonerId));
    }

    /**
     * 登录后获取待验证的人员信息
     */
    @GetMapping("/getWaitCheckPrisonerList")
    @ApiOperation(value = "防误放-登录后获取待验证的人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码")
    })
    public CommonResult<List<AppCsFwfPrisonerVO>> getWaitCheckPrisonerList(@RequestParam("orgCode") String orgCode) {
        return success(csfwfService.getWaitCheckPrisonerList(orgCode));
    }

//    /**
//     * 接收对比结果
//     */
//    @GetMapping("/receiveCompareResult")
//    @ApiOperation(value = "接收对比结果")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "prisonerId", value = "人员ID"),
//            @ApiImplicitParam(name = "checkModel", value = "检查模式（指掌纹信息: 01, 虹膜信息: 02, 人像信息: 03, DNA信息: 04）"),
//            @ApiImplicitParam(name = "checkResult", value = "检查结果(0：失败，1：成功)")
//
//    })
//    public CommonResult<String> receiveCompareResult(@RequestParam("prisonerId") String prisonerId,
//                                                     @RequestParam("checkModel") String checkModel,
//                                                     @RequestParam("checkResult") String checkResult) {
//        return success(csfwfService.receiveCompareResult(prisonerId, checkModel, checkResult));
//    }

    /**
     * 完成对比
     */
    @PostMapping("/completeCompare")
    @ApiOperation(value = "防误放-完成对比接口")
    public CommonResult<String> completeCompare(@Valid @RequestBody AppCsFwfInfoReqVO reqVO) {
        return success(csfwfService.completeCompare(reqVO));
    }

    @PostMapping("/list")
    @ApiOperation(value = "防误放-台账")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "timeType", value = "时间段：1 全部，2 今天，3 昨天，4 近一周", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "businessType", value = "业务类型（1：出所、2：入所）", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "orgCode", value = "机构编号", dataType = "String", paramType = "query", required = true)
    })
    public CommonResult<List<AppCsFwfRespVO>> list(@RequestParam(value = "timeType") String timeType,
                                                   @RequestParam(value = "businessType", required = false) String businessType,
                                                   @RequestParam("orgCode") String orgCode) {
        List<AppCsFwfRespVO> resultList = csfwfService.listFwfInfo(timeType, businessType, orgCode);
        return success(resultList);
    }


}
