package com.rs.module.acp.service.pm;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.pm.vo.DeviceDoorListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.DoorEventVO;
import com.rs.module.acp.entity.pm.DeviceDoorDO;
import com.rs.third.api.dto.haikang.DoorControlEventDTO;
import com.rs.third.api.enums.AccessControlEventType;
import com.rs.third.api.enums.DoorControlTypeEnum;
import com.rs.third.api.model.DoorControlEventPageReq;
import com.rs.third.api.model.PageApiResult;

import java.util.List;

/**
 * 实战平台-监管管理-门禁点管理 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceDoorService extends IBaseService<DeviceDoorDO>{

    /**
     * 获得实战平台-监管管理-门禁点管理
     *
     * @param id 编号
     * @return 实战平台-监管管理-门禁点管理
     */
    DeviceDoorDO getDeviceDoor(String id);

    /**
    * 获得实战平台-监管管理-门禁点管理列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-门禁点管理列表
    */
    List<DeviceDoorDO> getDeviceDoorList(DeviceDoorListReqVO listReqVO);

    /**
     * 同步海康门禁点信息
     */
    void syncDeviceDoor();

    /**
     * 绑定监室
     * @param id
     * @param roomId
     * @param roomName
     * @param bindRoomStatus
     */
    void bindRoom(String id, String roomId, String roomName, String bindRoomStatus);

    /**
     * 获取门禁点状态
     * @param doorIndexCode
     */
    JSONObject getDeviceDoorStatus(String doorIndexCode);

    /**
     * 控制门禁
     * @param doorIndexCode
     * @param controlType
     * @return
     */
    JSONObject controlDoor(String doorIndexCode, DoorControlTypeEnum controlType);

    /**
     * 控制监室门禁
     * @param roomId
     * @param controlType
     * @return
     */
    JSONObject controlRoomDoor(String roomId, DoorControlTypeEnum controlType);

    /**
     * 获取门禁事件列表
     * @param pageReq
     * @return
     */
    PageApiResult<DoorControlEventDTO> getDoorControlEventList(DoorControlEventPageReq pageReq);

    /**
     * 获取最新一次门禁事件
     * @param doorIndexCode
     * @param eventType
     * @return
     */
    DoorControlEventDTO getLastDoorControlEvent(String doorIndexCode, AccessControlEventType eventType);

    /**
     * 获取最新的几次门禁关门时间
     * @param roomId
     * @param num
     * @return
     */
    List<DoorEventVO> getLastDoorCloseControlEvent(String roomId, int num);


}
