package com.rs.module.acp.controller.admin.pm;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.NotifyAssignRespVO;
import com.rs.module.acp.controller.admin.pm.vo.NotifyAssignSaveReqVO;
import com.rs.module.acp.entity.pm.NotifyAssignDO;
import com.rs.module.acp.service.pm.NotifyAssignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合业务-通知交办")
@RestController
@RequestMapping("/acp/pm/notifyAssign")
@Validated
public class NotifyAssignController {

    @Resource
    private NotifyAssignService notifyAssignService;

    @PostMapping("/create")
    @ApiOperation(value = "创建通知交办")
    public CommonResult<String> createNotifyAssign(@Valid @RequestBody NotifyAssignSaveReqVO createReqVO) {
        return success(notifyAssignService.createNotifyAssign(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新通知交办")
    public CommonResult<Boolean> updateNotifyAssign(@Valid @RequestBody NotifyAssignSaveReqVO updateReqVO) {
        notifyAssignService.updateNotifyAssign(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除通知交办")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteNotifyAssign(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           notifyAssignService.deleteNotifyAssign(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得通知交办信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<NotifyAssignRespVO> getNotifyAssign(@RequestParam("id") String id) {
        NotifyAssignDO notifyAssign = notifyAssignService.getNotifyAssign(id);
        NotifyAssignRespVO respVO = BeanUtils.toBean(notifyAssign, NotifyAssignRespVO.class);
//        respVO.setUserList(notifyAssignService.getRecipientByNotifyId(id));
        return success(respVO);
    }

    @PostMapping("/dispose")
    @ApiOperation(value = "信息处理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号"),
            @ApiImplicitParam(name = "message", value = "处理内容")
    })
    public CommonResult<Boolean> dispose(@Valid @RequestBody JSONObject params) {
        notifyAssignService.dispose(params.getString("id"), params.getString("message"));
        return success(true);
    }

}
