package com.rs.module.acp.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 实战平台-监管管理-通知交办 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_notify_assign")
@KeySequence("acp_pm_notify_assign_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_notify_assign")
public class NotifyAssignDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 通知标题
     */
    private String title;
    /**
     * 发布类型
     */
    private String publishType;
    /**
     * 正文内容
     */
    private String content;
    /**
     * 是否全警
     */
    private Short isFullPolice;
    /**
     * 被考核对象类型，01:岗位、02：部门、03：用户
     */
//    private String handleObjectType;
    /**
     * 被考核对象ID
     */
    private String handleObjectId;
    /**
     * 被考核对象名称
     */
    private String handleObjectName;
    /**
     * 附件地址
     */
    private String attUrl;
    /**
     * 状态
     */
//    private String status;

}
