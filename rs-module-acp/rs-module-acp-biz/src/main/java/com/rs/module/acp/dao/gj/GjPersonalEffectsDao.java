package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.gj.GjPersonalEffectsDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.gj.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-随身物品登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GjPersonalEffectsDao extends IBaseDao<GjPersonalEffectsDO> {


    default PageResult<GjPersonalEffectsDO> selectPage(GjPersonalEffectsPageReqVO reqVO) {
        Page<GjPersonalEffectsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<GjPersonalEffectsDO> wrapper = new LambdaQueryWrapperX<GjPersonalEffectsDO>()
            .eqIfPresent(GjPersonalEffectsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GjPersonalEffectsDO::getJgryxm, reqVO.getJgryxm())
            .likeIfPresent(GjPersonalEffectsDO::getName, reqVO.getName())
            .eqIfPresent(GjPersonalEffectsDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(GjPersonalEffectsDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(GjPersonalEffectsDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(GjPersonalEffectsDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(GjPersonalEffectsDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(GjPersonalEffectsDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(GjPersonalEffectsDO::getAddTime);
        }
        Page<GjPersonalEffectsDO> gjPersonalEffectsPage = selectPage(page, wrapper);
        return new PageResult<>(gjPersonalEffectsPage.getRecords(), gjPersonalEffectsPage.getTotal());
    }
    default List<GjPersonalEffectsDO> selectList(GjPersonalEffectsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GjPersonalEffectsDO>()
            .eqIfPresent(GjPersonalEffectsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(GjPersonalEffectsDO::getJgryxm, reqVO.getJgryxm())
            .likeIfPresent(GjPersonalEffectsDO::getName, reqVO.getName())
            .eqIfPresent(GjPersonalEffectsDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(GjPersonalEffectsDO::getOperateTime, reqVO.getOperateTime())
            .eqIfPresent(GjPersonalEffectsDO::getOperatePoliceSfzh, reqVO.getOperatePoliceSfzh())
            .eqIfPresent(GjPersonalEffectsDO::getOperatePolice, reqVO.getOperatePolice())
            .betweenIfPresent(GjPersonalEffectsDO::getReturnTime, reqVO.getReturnTime())
            .eqIfPresent(GjPersonalEffectsDO::getStatus, reqVO.getStatus())
        .orderByDesc(GjPersonalEffectsDO::getAddTime));    }


    }
