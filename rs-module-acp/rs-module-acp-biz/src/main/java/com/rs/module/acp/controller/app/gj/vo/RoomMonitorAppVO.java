package com.rs.module.acp.controller.app.gj.vo;


import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 实战平台-管教业务--监室调整记录-app列表 Request VO")
@Data
public class RoomMonitorAppVO extends BaseVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

}
