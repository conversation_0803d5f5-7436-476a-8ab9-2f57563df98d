package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-模板配置所情处置情况新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglMbpzSqczSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;


    @ApiModelProperty("处置名称")
    private String handleName;

    @ApiModelProperty("处置内容")
    private String handleContent;

    @ApiModelProperty("排序")
    private Integer orderId;
}
