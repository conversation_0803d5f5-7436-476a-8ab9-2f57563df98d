package com.rs.module.acp.controller.app.pm.vo;

import com.rs.module.acp.enums.db.VerificationMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 防误放完成信息请求参数
 *
 * <AUTHOR>
 * @Date 2025/8/14 15:35
 */
@Data
@ApiModel(description = "防误放完成信息请求参数")
public class AppCsFwfInfoReqVO {

    @ApiModelProperty("人员ID")
    private String prisonerId;
    @ApiModelProperty("强制类型（1：强制带出，2：强制带入）")
    private String mandatoryType;
    @ApiModelProperty("强制原因")
    private String mandatoryReason;
    @ApiModelProperty("验证方式")
    @NotNull(message = "验证方式不能为空")
    private List<VerificationMode> verificationModes;
}
