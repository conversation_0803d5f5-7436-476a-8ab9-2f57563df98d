package com.rs.module.acp.util;

public class QingyanLocalSenseUtil {
	public static String POS_KEY = "rs:acp:znwdqy:pos:%s";
	public static String CAP_KEY = "rs:acp:znwdqy:cap:%s";
	public static String VITALSIGN_KEY = "rs:acp:znwdqy:vitalasign:%s:%s";
	public static String getPosKey(long tagId) {
		return String.format(POS_KEY, tagId);
	}
	public static String getCapKey(long tagId) {
		return String.format(CAP_KEY, tagId);
	}
	public static String getVitalsignKey(long tagId,Integer txlx) {
		return String.format(VITALSIGN_KEY, tagId,txlx);
	}
	/**
	 * 体征类型  1:心率
	 */
	public static Integer TZLX_XL=1;
	/**
	 * 体征类型  2:血氧
	 */
	public static Integer TZLX_XY=2;
	/**
	 * 体征类型  3:体温
	 */
	public static Integer TZLX_TW=3;
}

