package com.rs.module.acp.controller.admin.pi;

import cn.hutool.core.util.ObjectUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglBjldszDO;
import com.rs.module.acp.service.pi.SqglBjldszService;

@Api(tags = "实战平台-巡视管控-所情管理-报警联动设置")
@RestController
@RequestMapping("/acp/pi/sqglBjldsz")
@Validated
public class SqglBjldszController {

    @Resource
    private SqglBjldszService sqglBjldszService;

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-巡视管控-所情管理-报警联动设置")
    @LogRecordAnnotation(bizModule = "acp:sqglBjldsz:update", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-更新报警联动设置",
            success = "实战平台-巡视管控-更新报警联动设置成功", fail = "实战平台-巡视管控-更新报警联动设置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateSqglBjldsz(@Valid @RequestBody SqglBjldszSaveReqVO updateReqVO) {
        sqglBjldszService.updateSqglBjldsz(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-巡视管控-所情管理-报警联动设置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglBjldsz:get", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获得报警联动设置详情",
            success = "实战平台-巡视管控-获得报警联动设置详情成功",
            fail = "实战平台-巡视管控-获得报警联动设置详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<SqglBjldszRespVO> getSqglBjldsz(@RequestParam("id") String id) {
        SqglBjldszDO sqglBjldsz = sqglBjldszService.getSqglBjldsz(id);
        return success(BeanUtils.toBean(sqglBjldsz, SqglBjldszRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-巡视管控-所情管理-报警联动设置分页")
    @LogRecordAnnotation(bizModule = "acp:sqglBjldsz:page", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获得报警联动设置分页",
            success = "实战平台-巡视管控-获得报警联动设置分页成功",
            fail = "实战平台-巡视管控-获得报警联动设置分页失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<SqglBjldszRespVO>> getSqglBjldszPage(@Valid @RequestBody SqglBjldszPageReqVO pageReqVO) {
        PageResult<SqglBjldszDO> pageResult = sqglBjldszService.getSqglBjldszPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SqglBjldszRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-巡视管控-所情管理-报警联动设置列表")
    @LogRecordAnnotation(bizModule = "acp:sqglBjldsz:list", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获得报警联动设置列表",
            success = "实战平台-巡视管控-获得报警联动设置列表成功",
            fail = "实战平台-巡视管控-获得报警联动设置列表失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<SqglBjldszRespVO>> getSqglBjldszList(@Valid @RequestBody SqglBjldszListReqVO listReqVO) {
        List<SqglBjldszDO> list = sqglBjldszService.getSqglBjldszList(listReqVO);
        return success(BeanUtils.toBean(list, SqglBjldszRespVO.class));
    }

    @GetMapping("/initByOrgCode")
    @ApiOperation(value = "根据单位编号，初始化报警联动配置")
    @ApiImplicitParam(name = "orgCode", value = "单位编号")
    @LogRecordAnnotation(bizModule = "acp:sqglBjldsz:init", operateType = LogOperateType.CREATE, title = "实战平台-巡视管控-根据单位编号，初始化报警联动配置",
            success = "实战平台-巡视管控-根据单位编号，初始化报警联动配置成功",
            fail = "实战平台-巡视管控-根据单位编号，初始化报警联动配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#orgCode}}")
    public CommonResult<Boolean> initByOrgCode(@RequestParam("orgCode") String orgCode) {
        if(ObjectUtil.isEmpty(orgCode)){
            throw new ServerException("单位编码不可为空");
        }
        return success(sqglBjldszService.initByOrgCode(orgCode));
    }

    @GetMapping("/getGjlxByEventSrc")
    @ApiOperation(value = "根据所情来源编码，获取该联动配置下的告警类型")
    @ApiImplicitParam(name = "eventSrc", value = "报警来源编码")
    @LogRecordAnnotation(bizModule = "acp:sqglBjldsz:getGjlxByEventSrc", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-根据所情来源编码，获取该联动配置下的告警类型",
            success = "实战平台-巡视管控-根据所情来源编码，获取该联动配置下的告警类型成功",
            fail = "实战平台-巡视管控-根据所情来源编码，获取该联动配置下的告警类型失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#eventSrc}}")
    public CommonResult<List<SqglGjlxpzRespVO>> getGjlxByEventSrc(@RequestParam("eventSrc") String eventSrc) {
        return success(sqglBjldszService.getGjlxByEventSrc(eventSrc));
    }


    @GetMapping("/changeStatus")
    @ApiOperation(value = "启用或禁用所情联动配置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglBjldsz:changeStatus", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-启用或禁用所情联动配置",
            success = "实战平台-巡视管控-启用或禁用所情联动配置成功", fail = "实战平台-巡视管控-启用或禁用所情联动配置失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<Boolean> changeStatus(@RequestParam("id")String id) {
        return success(sqglBjldszService.changeStatus(id));
    }
}
