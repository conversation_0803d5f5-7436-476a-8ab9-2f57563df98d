package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzRespVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzSaveReqVO;
import com.rs.module.acp.entity.pi.SqglMbpzDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情管理-模板配置 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglMbpzService extends IBaseService<SqglMbpzDO>{

    /**
     * 创建实战平台-巡视管控-所情管理-模板配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSqglMbpz(@Valid SqglMbpzSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情管理-模板配置
     *
     * @param updateReqVO 更新信息
     */
    void updateSqglMbpz(@Valid SqglMbpzSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情管理-模板配置
     *
     * @param id 编号
     */
    void deleteSqglMbpz(String id);

    /**
     * 获得实战平台-巡视管控-所情管理-模板配置
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-模板配置
     */
    SqglMbpzDO getSqglMbpz(String id);

    /**
    * 获得实战平台-巡视管控-所情管理-模板配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情管理-模板配置分页
    */
    PageResult<SqglMbpzDO> getSqglMbpzPage(SqglMbpzPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情管理-模板配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情管理-模板配置列表
    */
    List<SqglMbpzRespVO> getSqglMbpzList(SqglMbpzListReqVO listReqVO);

    /**
     * 获得实战平台-巡视管控-所情管理-模板配置
     * @param id
     * @return
     */
    SqglMbpzRespVO getSqglMbpzById(String id);

    /**
     * 启用或禁用所情模板配置
     * @param id
     * @return
     */
    boolean changeStatus(String id);
}
