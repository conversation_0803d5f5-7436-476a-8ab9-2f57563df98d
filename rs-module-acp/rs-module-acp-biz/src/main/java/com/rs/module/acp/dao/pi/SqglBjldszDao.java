package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglBjldszListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglBjldszPageReqVO;
import com.rs.module.acp.entity.pi.SqglBjldszDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情管理-报警联动设置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SqglBjldszDao extends IBaseDao<SqglBjldszDO> {


    default PageResult<SqglBjldszDO> selectPage(SqglBjldszPageReqVO reqVO) {
        Page<SqglBjldszDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SqglBjldszDO> wrapper = new LambdaQueryWrapperX<SqglBjldszDO>()
            .eqIfPresent(SqglBjldszDO::getEventSrc, reqVO.getEventSrc())
            .eqIfPresent(SqglBjldszDO::getIsEnabled, reqVO.getIsEnabled())
            .eqIfPresent(SqglBjldszDO::getRemark, reqVO.getRemark())
            .eqIfPresent(SqglBjldszDO::getOptionalLinkageSettings, reqVO.getOptionalLinkageSettings())
            .eqIfPresent(SqglBjldszDO::getPromptSound, reqVO.getPromptSound())
                .eqIfPresent(SqglBjldszDO::getOrgCode,reqVO.getOrgCode())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SqglBjldszDO::getAddTime);
        }
        Page<SqglBjldszDO> sqglBjldszPage = selectPage(page, wrapper);
        return new PageResult<>(sqglBjldszPage.getRecords(), sqglBjldszPage.getTotal());
    }
    default List<SqglBjldszDO> selectList(SqglBjldszListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SqglBjldszDO>()
            .eqIfPresent(SqglBjldszDO::getEventSrc, reqVO.getEventSrc())
            .eqIfPresent(SqglBjldszDO::getIsEnabled, reqVO.getIsEnabled())
            .eqIfPresent(SqglBjldszDO::getRemark, reqVO.getRemark())
            .eqIfPresent(SqglBjldszDO::getOptionalLinkageSettings, reqVO.getOptionalLinkageSettings())
            .eqIfPresent(SqglBjldszDO::getPromptSound, reqVO.getPromptSound())
                .eqIfPresent(SqglBjldszDO::getOrgCode,reqVO.getOrgCode())
        .orderByDesc(SqglBjldszDO::getAddTime));    }


    }
