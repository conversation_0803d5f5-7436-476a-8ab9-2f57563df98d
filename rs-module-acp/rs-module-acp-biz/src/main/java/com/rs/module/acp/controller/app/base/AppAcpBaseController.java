package com.rs.module.acp.controller.app.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.app.gj.vo.ApprovalVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerAppRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.AgeCalculatorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Validated
@RestController
@RequestMapping("/app/acp/base")
@Api(tags = "app端基础接口")
public class AppAcpBaseController {

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Resource
    private BpmApi bpmApi;

    @GetMapping("/getPrisonerList")
    @ApiOperation(value = "app端根据监室号查询人员信息")
    public CommonResult<List<PrisonerAppRespVO>> getPrisonerByJsh(@RequestParam("roomId") String roomId) {
        List<PrisonerVwRespVO> vwRespList = prisonerService.getPrisonerListByJsh(roomId);
        if (CollUtil.isNotEmpty(vwRespList)) {
            List<PrisonerAppRespVO> respVOList = BeanUtils.toBean(vwRespList, PrisonerAppRespVO.class);
            for (PrisonerAppRespVO prisonerVO : respVOList) {
                Integer age = AgeCalculatorUtil.calculateAge(prisonerVO.getCsrq());
                prisonerVO.setAge(age);
                if (prisonerVO.getRssj() != null) {
                    prisonerVO.setRsts(DateUtil.betweenDay(prisonerVO.getRssj(), new Date(), true));
                }
            }
            return success(respVOList);
        }
        return success(new ArrayList<>());
    }


    @PostMapping("/selectAreaPrisonRoom")
    @ApiOperation(value = "app端查询区域监室信息")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:page", operateType = LogOperateType.QUERY, title = "获得实战平台-监管管理-区域监室分页",
            success = "获得实战平台-监管管理-区域监室分页成功", fail = "获得实战平台-监管管理-区域监室分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<AreaPrisonRoomRespVO>> getAreaPrisonRoomPage(@Valid @RequestBody AreaPrisonRoomPageReqVO pageReqVO) {
        pageReqVO.setExcludeSex(true);
        PageResult<AreaPrisonRoomDO> pageResult = areaPrisonRoomService.getAreaPrisonRoomPage(pageReqVO);
//        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        PageResult<AreaPrisonRoomRespVO> result = BeanUtils.toBean(pageResult, AreaPrisonRoomRespVO.class);
//        if (result != null && CollUtil.isNotEmpty(result.getList())) {
//            for (AreaPrisonRoomRespVO room : result.getList()) {
//                room.setPrisonerNum(prisonerService.getPrisonerCount(sessionUser.getOrgCode(), room.getRoomCode()));
//            }
//        }
        return success(result);
    }

    @GetMapping("/getPrisoner")
    @ApiOperation(value = "通过监管人员编号查询详情")
    public CommonResult<PrisonerVwRespVO> getPrisoner(@RequestParam("jgrybm") String jgrybm) {
        PrisonerVwRespVO prisonerInVwRespVO = prisonerService.getPrisonerSelectCompomenOne(jgrybm, PrisonerQueryRyztEnum.ALL);
        return success(prisonerInVwRespVO);
    }

    @GetMapping("/approveTrack")
    @ApiOperation(value = "获取审批轨迹")
    @ApiImplicitParam(name = "actInstId", value = "ACT流程实例Id")
    public CommonResult<List<ApprovalVO>> approveTrack(@RequestParam("actInstId") String actInstId) {
        JSONObject result = bpmApi.approveTrack(actInstId);

        // 获取审批轨迹
        List<JSONObject> dataList = JSONObject.parseArray(JSONObject.toJSONString(result.get("data")), JSONObject.class);
        List<ApprovalVO> approveTrack = new ArrayList<>();
        for (JSONObject data : dataList) {
            ApprovalVO approvalVO = new ApprovalVO();
            approvalVO.setId(data.getString("id"));
            approvalVO.setTaskName(data.getString("taskName"));
            approvalVO.setExecuteUserName(data.getString("executeUserName"));
            approvalVO.setApprovalContent(data.getString("approvalContent"));
            approvalVO.setEndTime(com.bsp.common.util.DateUtil.parseDateTime(data.getString("endTime")));
            approvalVO.setStatus(data.getString("status"));
            approveTrack.add(approvalVO);
        }

        return success(approveTrack);
    }

    @GetMapping("/checkIsApproveAuthority")
    @ApiOperation(value = "判断当前用户是否具有权限审批")
    @ApiImplicitParam(name = "taskId", value = "流程任务Id")
    public CommonResult<Boolean> checkIsApproveAuthority(@RequestParam("taskId") String actInstId) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Boolean isApproveAuthority = bpmApi.checkIsApproveAuthority(actInstId, sessionUser.getIdCard());
        return success(isApproveAuthority);
    }


}
