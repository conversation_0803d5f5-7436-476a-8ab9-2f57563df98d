package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.db.vo.zyjwzx.*;
import com.rs.module.acp.entity.db.ZyjwzxDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-暂予监外执行 Service 接口
 *
 * <AUTHOR>
 */
public interface ZyjwzxService extends IBaseService<ZyjwzxDO>{

    /**
     * 创建实战平台-收押业务-暂予监外执行
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createZyjwzx(@Valid ZyjwzxSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-暂予监外执行
     *
     * @param updateReqVO 更新信息
     */
    void updateZyjwzx(@Valid ZyjwzxSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-暂予监外执行
     *
     * @param id 编号
     */
    void deleteZyjwzx(String id);

    /**
     * 获得实战平台-收押业务-暂予监外执行
     *
     * @param id 编号
     * @return 实战平台-收押业务-暂予监外执行
     */
    ZyjwzxRespVO getZyjwzx(String id);

    /**
    * 获得实战平台-收押业务-暂予监外执行分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-暂予监外执行分页
    */
    PageResult<ZyjwzxDO> getZyjwzxPage(ZyjwzxPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-暂予监外执行列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-暂予监外执行列表
    */
    List<ZyjwzxDO> getZyjwzxList(ZyjwzxListReqVO listReqVO);


    String dj(ZyjwzxDjReqVO djReqVO);

    void swhcs(ZyjwzxSwhcsReqVO swhcsReqVO);

    void zdjdjc(ZyjwzxZdjdjcReqVO zdjdjcReqVO);

    void bwjycx(ZyjwzxBwjycxReqVO bwjycxReqVO);

    void hsjzd(ZyjwzxHsjzdReqVO hsjzdReqVO);

    void swhsy(ZyjwzxSwhsyReqVO swhsyReqVO);

    void sngs(ZyjwzxSngsReqVO sngsReqVO);

    void tzsh(ZyjwzxTzshReqVO tzshReqVO);

    void zsjcsjd(ZyjwzxZsjcsjdReqVO zsjcsjdReqVO);

    void gajggs(ZyjwzxGajggsReqVO gajggsReqVO);

    void gajgsp(ZyjwzxGajgspReqVO gajgspReqVO);

    void zfjfzx(ZyjwzxZfjfzxReqVO zfjfzxReqVO);
}
