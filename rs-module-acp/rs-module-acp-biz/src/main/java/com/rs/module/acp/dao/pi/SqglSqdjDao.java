package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjPageReqVO;
import com.rs.module.acp.entity.pi.SqglSqdjDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情管理-所情登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SqglSqdjDao extends IBaseDao<SqglSqdjDO> {


    default PageResult<SqglSqdjDO> selectPage(SqglSqdjPageReqVO reqVO) {
        Page<SqglSqdjDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SqglSqdjDO> wrapper = new LambdaQueryWrapperX<SqglSqdjDO>()
            .eqIfPresent(SqglSqdjDO::getEventCode, reqVO.getEventCode())
            .eqIfPresent(SqglSqdjDO::getEventLevel, reqVO.getEventLevel())
            .eqIfPresent(SqglSqdjDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(SqglSqdjDO::getAreaName, reqVO.getAreaName())
            .betweenIfPresent(SqglSqdjDO::getHappenTime, reqVO.getHappenTime())
            .eqIfPresent(SqglSqdjDO::getEventTemplateId, reqVO.getEventTemplateId())
            .likeIfPresent(SqglSqdjDO::getEventName, reqVO.getEventName())
            .eqIfPresent(SqglSqdjDO::getEventType, reqVO.getEventType())
            .betweenIfPresent(SqglSqdjDO::getEventStartTime, reqVO.getEventStartTime())
            .betweenIfPresent(SqglSqdjDO::getEventEndTime, reqVO.getEventEndTime())
            .eqIfPresent(SqglSqdjDO::getEventDetails, reqVO.getEventDetails())
            .eqIfPresent(SqglSqdjDO::getPushObject, reqVO.getPushObject())
            .eqIfPresent(SqglSqdjDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SqglSqdjDO::getAddTime);
        }
        Page<SqglSqdjDO> sqglSqdjPage = selectPage(page, wrapper);
        return new PageResult<>(sqglSqdjPage.getRecords(), sqglSqdjPage.getTotal());
    }
    default List<SqglSqdjDO> selectList(SqglSqdjListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SqglSqdjDO>()
            .eqIfPresent(SqglSqdjDO::getEventCode, reqVO.getEventCode())
            .eqIfPresent(SqglSqdjDO::getEventLevel, reqVO.getEventLevel())
            .eqIfPresent(SqglSqdjDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(SqglSqdjDO::getAreaName, reqVO.getAreaName())
            .betweenIfPresent(SqglSqdjDO::getHappenTime, reqVO.getHappenTime())
            .eqIfPresent(SqglSqdjDO::getEventTemplateId, reqVO.getEventTemplateId())
            .likeIfPresent(SqglSqdjDO::getEventName, reqVO.getEventName())
            .eqIfPresent(SqglSqdjDO::getEventType, reqVO.getEventType())
            .betweenIfPresent(SqglSqdjDO::getEventStartTime, reqVO.getEventStartTime())
            .betweenIfPresent(SqglSqdjDO::getEventEndTime, reqVO.getEventEndTime())
            .eqIfPresent(SqglSqdjDO::getEventDetails, reqVO.getEventDetails())
            .eqIfPresent(SqglSqdjDO::getPushObject, reqVO.getPushObject())
            .eqIfPresent(SqglSqdjDO::getStatus, reqVO.getStatus())
        .orderByDesc(SqglSqdjDO::getAddTime));    }


    }
