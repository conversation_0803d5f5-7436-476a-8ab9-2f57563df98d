package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-管教业务-专业法律指导 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_zyflzd")
@KeySequence("acp_gj_zyflzd_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_zyflzd")
public class ZyflzdDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 具体内容
     */
    private String jtnr;
    /**
     * 联系电话
     */
    private String lxdh;
    /**
     * 附件
     */
    private String fj;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 办案民警
     */
    private String bamj;
    /**
     * 经办时间
     */
    private Date jbsj;
    /**
     * 签收经办民警身份证号
     */
    private String jbmjsfzh;
    /**
     * 签收经办民警
     */
    private String jbmjxm;
    /**
     * 反馈内容
     */
    private String fknr;
    /**
     * 反馈附件
     */
    private String fkfj;
    /**
     * 反馈时间
     */
    private Date fksj;
    /**
     * 反馈民警身份证号
     */
    private String fkjbmjsfzh;
    /**
     * 反馈民警姓名
     */
    private String fkjbmjxm;
    /**
     * 状态
     */
    private String status;
    /**
     * 类型(01:发起,02:接收)
     */
    private String type;

}
