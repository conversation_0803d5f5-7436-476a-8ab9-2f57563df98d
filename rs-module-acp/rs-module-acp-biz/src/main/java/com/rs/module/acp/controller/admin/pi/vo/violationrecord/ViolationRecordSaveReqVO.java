package com.rs.module.acp.controller.admin.pi.vo.violationrecord;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-违规登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ViolationRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    //@NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    //@NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("巡控室ID")
    @NotEmpty(message = "巡控室ID不能为空")
    private String patrolRoomId;

    @ApiModelProperty("巡控室名称")
    @NotEmpty(message = "巡控室名称不能为空")
    private String patrolRoomName;

    @ApiModelProperty("监室号")
    @NotEmpty(message = "监室号不能为空")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("违规内容")
    @NotEmpty(message = "违规内容不能为空")
    private String violationContent;
    @ApiModelProperty("违规内容html")
    private String violationContentHtml;
    @ApiModelProperty("处置情况")
    private String disposalSituation;

    @ApiModelProperty("是否岗位协同：1-是/0-否")
    @NotNull(message = "是否岗位协同：1-是/0-否不能为空")
    private Short isPostCoordination;

    @ApiModelProperty("岗位协同人员，多选项")
    private String coordinationPosts;

    @ApiModelProperty("岗位协同人员名称")
    private String coordinationPostsName;

    @ApiModelProperty("上传附件路径，存储附件文件路径")
    private String attachment;

    @ApiModelProperty("登记经办人")
    @NotEmpty(message = "登记经办人不能为空")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    @NotEmpty(message = "登记经办人姓名不能为空")
    private String operatorXm;

    @ApiModelProperty("登记经办时间")
    @NotNull(message = "登记经办时间不能为空")
    private Date operatorTime;
    @ApiModelProperty("岗位协同推送对象证件号码")
    private String pushTargetIdCard;
    @ApiModelProperty("岗位协同推送对象")
    private String pushTarget;
    @ApiModelProperty("岗位协同推送内容")
    private String pushContent;

}
