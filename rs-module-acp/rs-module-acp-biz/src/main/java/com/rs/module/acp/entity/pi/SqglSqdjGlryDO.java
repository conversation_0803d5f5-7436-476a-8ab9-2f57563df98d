package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-所情登记关联人员 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_sqdj_glry")
@KeySequence("acp_pi_sqgl_sqdj_glry_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_sqdj_glry")
public class SqglSqdjGlryDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所情登记ID
     */
    private String sqdjId;
    /**
     * 人员类型（1：在押人员，2：工作人员，3:外来人员，4：报警人员)
     */
    private String personnelType;
    /**
     * 人员ID
     */
    private String personnelId;
    /**
     * 人员姓名
     */
    private String personnelName;
    /**
     * 照片URL
     */
    private String photoUrl;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;

}
