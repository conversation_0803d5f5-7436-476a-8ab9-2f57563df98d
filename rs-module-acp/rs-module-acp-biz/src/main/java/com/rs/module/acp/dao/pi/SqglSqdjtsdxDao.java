package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjtsdxListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjtsdxPageReqVO;
import com.rs.module.acp.entity.pi.SqglSqdjtsdxDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情管理-所情登记推送对象 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SqglSqdjtsdxDao extends IBaseDao<SqglSqdjtsdxDO> {


    default PageResult<SqglSqdjtsdxDO> selectPage(SqglSqdjtsdxPageReqVO reqVO) {
        Page<SqglSqdjtsdxDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SqglSqdjtsdxDO> wrapper = new LambdaQueryWrapperX<SqglSqdjtsdxDO>()
            .eqIfPresent(SqglSqdjtsdxDO::getSqdjId, reqVO.getSqdjId())
            .eqIfPresent(SqglSqdjtsdxDO::getPushUserSfzh, reqVO.getPushUserSfzh())
            .likeIfPresent(SqglSqdjtsdxDO::getPushUserName, reqVO.getPushUserName())
            .betweenIfPresent(SqglSqdjtsdxDO::getPushTime, reqVO.getPushTime())
            .likeIfPresent(SqglSqdjtsdxDO::getPushPostName, reqVO.getPushPostName())
            .eqIfPresent(SqglSqdjtsdxDO::getPushPostCode, reqVO.getPushPostCode())
            .eqIfPresent(SqglSqdjtsdxDO::getSqczId, reqVO.getSqczId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SqglSqdjtsdxDO::getAddTime);
        }
        Page<SqglSqdjtsdxDO> sqglSqdjtsdxPage = selectPage(page, wrapper);
        return new PageResult<>(sqglSqdjtsdxPage.getRecords(), sqglSqdjtsdxPage.getTotal());
    }
    default List<SqglSqdjtsdxDO> selectList(SqglSqdjtsdxListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SqglSqdjtsdxDO>()
            .eqIfPresent(SqglSqdjtsdxDO::getSqdjId, reqVO.getSqdjId())
            .eqIfPresent(SqglSqdjtsdxDO::getPushUserSfzh, reqVO.getPushUserSfzh())
            .likeIfPresent(SqglSqdjtsdxDO::getPushUserName, reqVO.getPushUserName())
            .betweenIfPresent(SqglSqdjtsdxDO::getPushTime, reqVO.getPushTime())
            .likeIfPresent(SqglSqdjtsdxDO::getPushPostName, reqVO.getPushPostName())
            .eqIfPresent(SqglSqdjtsdxDO::getPushPostCode, reqVO.getPushPostCode())
            .eqIfPresent(SqglSqdjtsdxDO::getSqczId, reqVO.getSqczId())
        .orderByDesc(SqglSqdjtsdxDO::getAddTime));    }


    }
