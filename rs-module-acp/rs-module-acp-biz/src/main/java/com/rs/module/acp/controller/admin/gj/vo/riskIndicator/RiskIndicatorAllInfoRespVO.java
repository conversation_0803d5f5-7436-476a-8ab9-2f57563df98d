package com.rs.module.acp.controller.admin.gj.vo.riskIndicator;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险指标 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskIndicatorAllInfoRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("全部-总数")
    private int allCount;

    @ApiModelProperty("风险指标数")
    private int fxIndicatorCount;

    @ApiModelProperty("加分指标数")
    private int jfIndicatorCount;

    @ApiModelProperty("高风险数")
    private int gfxCount;

    @ApiModelProperty("中风险数")
    private int zfxCount;

    @ApiModelProperty("低风险数")
    private int dfxCount;

    @ApiModelProperty("指标报告集合")
    private List<RiskIndicatorBgRespVO> bgList;
}
