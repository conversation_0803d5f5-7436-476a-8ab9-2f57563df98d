package com.rs.module.acp.controller.admin.gj.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-随身物品登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GjPersonalEffectsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("物品名称，字典ZD_SSWPDJ_WPMC")
    private String name;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("经办时间")
    private Date operateTime;

    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("经办民警")
    private String operatePolice;

    @ApiModelProperty("归还时间")
    private Date returnTime;

    @ApiModelProperty("状态，字典ZD_SSWPDJ_WPZT")
    private String status;

}
