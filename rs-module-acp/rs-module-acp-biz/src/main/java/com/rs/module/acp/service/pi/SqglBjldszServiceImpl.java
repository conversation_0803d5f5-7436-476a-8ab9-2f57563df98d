package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspSdk;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.*;
import com.rs.module.acp.entity.pi.SqglGjlxpzDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.pi.SqglBjldszDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.SqglBjldszDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情管理-报警联动设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqglBjldszServiceImpl extends BaseServiceImpl<SqglBjldszDao, SqglBjldszDO> implements SqglBjldszService {

    @Resource
    private SqglBjldszDao sqglBjldszDao;

    @Autowired
    private SqglGjlxpzService sqglGjlxpzService;

    @Value("${system-mark}")
    private String systemMark;

    @Autowired
    private BspSdk bspSdk;

    @Override
    public String createSqglBjldsz(SqglBjldszSaveReqVO createReqVO) {
        // 插入
        SqglBjldszDO sqglBjldsz = BeanUtils.toBean(createReqVO, SqglBjldszDO.class);
        sqglBjldszDao.insert(sqglBjldsz);
        // 返回
        return sqglBjldsz.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSqglBjldsz(SqglBjldszSaveReqVO updateReqVO) {
        // 校验存在
        validateSqglBjldszExists(updateReqVO.getId());

        if (CollectionUtil.isNotEmpty(updateReqVO.getGjlxList())) {
            List<SqglGjlxpzDO> sqglGjlxpzDOList = BeanUtils.toBean(updateReqVO.getGjlxList(), SqglGjlxpzDO.class);
            for(int i=0;i<sqglGjlxpzDOList.size();i++){
                sqglGjlxpzDOList.get(i).setOrderId(i+1);
            }
            sqglGjlxpzService.updateBatchById(sqglGjlxpzDOList);
        }

        // 更新
        SqglBjldszDO updateObj = BeanUtils.toBean(updateReqVO, SqglBjldszDO.class);
        sqglBjldszDao.updateById(updateObj);
    }

    @Override
    public void deleteSqglBjldsz(String id) {
        // 校验存在
        validateSqglBjldszExists(id);
        // 删除
        sqglBjldszDao.deleteById(id);
    }

    private void validateSqglBjldszExists(String id) {
        if (sqglBjldszDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情管理-报警联动设置数据不存在");
        }
    }

    @Override
    public SqglBjldszDO getSqglBjldsz(String id) {
        return sqglBjldszDao.selectById(id);
    }

    @Override
    public PageResult<SqglBjldszDO> getSqglBjldszPage(SqglBjldszPageReqVO pageReqVO) {
        if(ObjectUtil.isEmpty(pageReqVO.getOrgCode())){
            pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        return sqglBjldszDao.selectPage(pageReqVO);
    }

    @Override
    public List<SqglBjldszDO> getSqglBjldszList(SqglBjldszListReqVO listReqVO) {
        if(ObjectUtil.isEmpty(listReqVO.getOrgCode())){
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        return sqglBjldszDao.selectList(listReqVO);
    }

    @Override
    public SqglBjldszRespVO getSqglBjldszById(String id) {
        SqglBjldszDO sqglBjldszDO = sqglBjldszDao.selectById(id);
        if (ObjectUtil.isEmpty(sqglBjldszDO)) {
            throw new ServerException("所查询的配置不存在");
        }
        SqglBjldszRespVO res = BeanUtils.toBean(sqglBjldszDO, SqglBjldszRespVO.class);

        LambdaQueryWrapper<SqglGjlxpzDO> gjlxpzDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        gjlxpzDOLambdaQueryWrapper.eq(SqglGjlxpzDO::getEventSrc,sqglBjldszDO.getEventSrc())
                .eq(SqglGjlxpzDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        List<SqglGjlxpzDO> sqglGjlxpzDOList = sqglGjlxpzService.list(gjlxpzDOLambdaQueryWrapper);

        if(CollectionUtil.isNotEmpty(sqglGjlxpzDOList)){
            res.setGjlxList(BeanUtils.toBean(sqglGjlxpzDOList, SqglGjlxpzRespVO.class));
        }

        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initByOrgCode(String orgCode) {
        List<SqglBjldszDO> sqglBjldszDOList = list(new LambdaQueryWrapper<SqglBjldszDO>().eq(SqglBjldszDO::getOrgCode,orgCode));

        Map<String,SqglBjldszDO> sjkMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(sqglBjldszDOList)){
            sqglBjldszDOList.forEach(x->{
                sjkMap.put(x.getEventSrc(),x);
            });
        }

        List<SqglBjldszDO> saveLdszList = new ArrayList<>();
        List<SqglGjlxpzDO> saveGjlxList = new ArrayList<>();

        Map<Object, Object> sqlyMap = bspSdk.getDic(systemMark,"ZD_JJKS_SQLY");
        Map<Object, Object> gjlxMap = bspSdk.getDic(systemMark,"ZD_JJKS_GJLX");

        for(Map.Entry<Object,Object> temp:sqlyMap.entrySet()){
            String key = String.valueOf(temp.getKey());
            String value = String.valueOf(temp.getValue());
            SqglBjldszDO sqglBjldszDO = new SqglBjldszDO();
            if(sjkMap.containsKey(key)){
                sqglBjldszDO = sjkMap.get(key);
            }else {
                sqglBjldszDO.setEventSrc(key);
            }

            List<SqglGjlxpzDO> sqglGjlxpzDOList = sqglGjlxpzService.list(new LambdaQueryWrapper<SqglGjlxpzDO>()
                    .eq(SqglGjlxpzDO::getOrgCode,orgCode).eq(SqglGjlxpzDO::getEventSrc,key));
            Map<String,SqglGjlxpzDO> sjkGjlxMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(sqglGjlxpzDOList)){
                sqglGjlxpzDOList.forEach(x->{
                    sjkGjlxMap.put(x.getAlarmType(),x);
                });
            }

            for(Map.Entry<Object,Object> tempGjlxMap:gjlxMap.entrySet()){
                String gjlxKey = String.valueOf(tempGjlxMap.getKey());
                if(gjlxKey.startsWith(key)){
                    SqglGjlxpzDO sqglGjlxpzDO = new SqglGjlxpzDO();
                    if(sjkGjlxMap.containsKey(gjlxKey)){
                        sqglGjlxpzDO = sjkGjlxMap.get(gjlxKey);
                    }else {
                        sqglGjlxpzDO.setEventSrc(key);
                        sqglGjlxpzDO.setAlarmType(gjlxKey);
                        sqglGjlxpzDO.setOrgCode(orgCode);
                    }
                    saveGjlxList.add(sqglGjlxpzDO);
                }
            }
            saveLdszList.add(sqglBjldszDO);
        }

        saveOrUpdateBatch(saveLdszList);
        sqglGjlxpzService.saveOrUpdateBatch(saveGjlxList);
        return true;
    }

    @Override
    public List<SqglGjlxpzRespVO> getGjlxByEventSrc(String eventSrc) {
        LambdaQueryWrapper<SqglGjlxpzDO> gjlxpzDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        gjlxpzDOLambdaQueryWrapper.eq(SqglGjlxpzDO::getEventSrc,eventSrc)
                .eq(SqglGjlxpzDO::getOrgCode,SessionUserUtil.getSessionUser().getOrgCode());
        List<SqglGjlxpzDO> sqglGjlxpzDOList = sqglGjlxpzService.list(gjlxpzDOLambdaQueryWrapper);
        if(CollectionUtil.isEmpty(sqglGjlxpzDOList)){
            return new ArrayList<>();
        }
        return BeanUtils.toBean(sqglGjlxpzDOList,SqglGjlxpzRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(String id) {
        SqglBjldszDO sqglBjldszDO = getById(id);

        if(ObjectUtil.isEmpty(sqglBjldszDO)){
            throw new ServerException("所情联动配置不存在");
        }
        if(sqglBjldszDO.getIsEnabled() == 1){
            sqglBjldszDO.setIsEnabled((short) 0);
        }else {
            sqglBjldszDO.setIsEnabled((short) 1);
        }
        return updateById(sqglBjldszDO);
    }
}
