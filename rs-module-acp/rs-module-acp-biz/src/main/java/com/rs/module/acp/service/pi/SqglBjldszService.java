package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.*;
import com.rs.module.acp.entity.pi.SqglBjldszDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情管理-报警联动设置 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglBjldszService extends IBaseService<SqglBjldszDO>{

    /**
     * 创建实战平台-巡视管控-所情管理-报警联动设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSqglBjldsz(@Valid SqglBjldszSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情管理-报警联动设置
     *
     * @param updateReqVO 更新信息
     */
    void updateSqglBjldsz(@Valid SqglBjldszSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情管理-报警联动设置
     *
     * @param id 编号
     */
    void deleteSqglBjldsz(String id);

    /**
     * 获得实战平台-巡视管控-所情管理-报警联动设置
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-报警联动设置
     */
    SqglBjldszDO getSqglBjldsz(String id);

    /**
    * 获得实战平台-巡视管控-所情管理-报警联动设置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情管理-报警联动设置分页
    */
    PageResult<SqglBjldszDO> getSqglBjldszPage(SqglBjldszPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情管理-报警联动设置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情管理-报警联动设置列表
    */
    List<SqglBjldszDO> getSqglBjldszList(SqglBjldszListReqVO listReqVO);


    /**
     * 获得实战平台-巡视管控-所情管理-报警联动设置
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-报警联动设置
     */
    SqglBjldszRespVO getSqglBjldszById(String id);

    /**
     * 根据单位编号，初始化报警联动配置
     * @param orgCode
     * @return
     */
    boolean initByOrgCode(String orgCode);

    /**
     * 根据所情来源编码，获取该联动配置下的告警类型
     * @param eventSrc
     * @return
     */
    List<SqglGjlxpzRespVO> getGjlxByEventSrc(String eventSrc);

    /**
     * 启用或禁用所情联动配置
     * @param id
     * @return
     */
    boolean changeStatus(String id);
}
