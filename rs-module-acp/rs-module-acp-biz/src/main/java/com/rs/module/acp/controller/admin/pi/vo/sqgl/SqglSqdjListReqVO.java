package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqdjListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所情编号")
    private String eventCode;

    @ApiModelProperty("所情等级，字典：")
    private String eventLevel;

    @ApiModelProperty("所情地点")
    private String areaId;

    @ApiModelProperty("所情地点名称")
    private String areaName;

    @ApiModelProperty("发生时间")
    private Date[] happenTime;

    @ApiModelProperty("event_template_id")
    private String eventTemplateId;

    @ApiModelProperty("所情名称")
    private String eventName;

    @ApiModelProperty("所情类型")
    private String eventType;

    @ApiModelProperty("所情开始时间（精确时间-开始）")
    private Date[] eventStartTime;

    @ApiModelProperty("所情结束时间（精确时间-结束）")
    private Date[] eventEndTime;

    @ApiModelProperty("所情详情")
    private String eventDetails;

    @ApiModelProperty("推送对象，存JSON")
    private String pushObject;

    @ApiModelProperty("状态，字典：")
    private String status;

}
