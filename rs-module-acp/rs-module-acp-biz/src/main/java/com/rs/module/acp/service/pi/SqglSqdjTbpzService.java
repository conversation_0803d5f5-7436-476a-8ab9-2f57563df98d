package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglSqdjTbpzDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情管理-所情登记-同步配置 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglSqdjTbpzService extends IBaseService<SqglSqdjTbpzDO>{

    List<JSONObject> getSyncList(String sql);

}
