package com.rs.module.acp.service.pi;

import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjtsdxListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjtsdxPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjtsdxSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglSqdjtsdxDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.SqglSqdjtsdxDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情管理-所情登记推送对象 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqglSqdjtsdxServiceImpl extends BaseServiceImpl<SqglSqdjtsdxDao, SqglSqdjtsdxDO> implements SqglSqdjtsdxService {

    @Resource
    private SqglSqdjtsdxDao sqglSqdjtsdxDao;

    @Override
    public String createSqglSqdjtsdx(SqglSqdjtsdxSaveReqVO createReqVO) {
        // 插入
        SqglSqdjtsdxDO sqglSqdjtsdx = BeanUtils.toBean(createReqVO, SqglSqdjtsdxDO.class);
        sqglSqdjtsdxDao.insert(sqglSqdjtsdx);
        // 返回
        return sqglSqdjtsdx.getId();
    }

    @Override
    public void updateSqglSqdjtsdx(SqglSqdjtsdxSaveReqVO updateReqVO) {
        // 校验存在
//        validateSqglSqdjtsdxExists(updateReqVO.getId());
        // 更新
        SqglSqdjtsdxDO updateObj = BeanUtils.toBean(updateReqVO, SqglSqdjtsdxDO.class);
        sqglSqdjtsdxDao.updateById(updateObj);
    }

    @Override
    public void deleteSqglSqdjtsdx(String id) {
        // 校验存在
        validateSqglSqdjtsdxExists(id);
        // 删除
        sqglSqdjtsdxDao.deleteById(id);
    }

    private void validateSqglSqdjtsdxExists(String id) {
        if (sqglSqdjtsdxDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情管理-所情登记推送对象数据不存在");
        }
    }

    @Override
    public SqglSqdjtsdxDO getSqglSqdjtsdx(String id) {
        return sqglSqdjtsdxDao.selectById(id);
    }

    @Override
    public PageResult<SqglSqdjtsdxDO> getSqglSqdjtsdxPage(SqglSqdjtsdxPageReqVO pageReqVO) {
        return sqglSqdjtsdxDao.selectPage(pageReqVO);
    }

    @Override
    public List<SqglSqdjtsdxDO> getSqglSqdjtsdxList(SqglSqdjtsdxListReqVO listReqVO) {
        return sqglSqdjtsdxDao.selectList(listReqVO);
    }


}
