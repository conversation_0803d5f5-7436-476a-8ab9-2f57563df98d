package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxZsjcsjdReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("驻所检察室监督提请日期")
    private Date zsjcsjdtqrq;

    @ApiModelProperty("驻所检察室监督审核日期")
    private Date zsjcsjdshrq;

    @ApiModelProperty("驻所检察室意见（1、同意；2、不同意。）")
    private String zsjcsyj;

    @ApiModelProperty("驻所检察室监督备注")
    private String zsjcsjdbz;

    @ApiModelProperty("驻所检察室监督材料")
    private String zsjcsjdcl;

}
