package com.rs.module.acp.controller.admin.gj.vo.xzfyssjl;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-行政复议讼诉记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class XzfyssjlRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("被监管人编码")
    private String jgrybm;
    @ApiModelProperty("业务类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XZFYXZSS_TYPE")
    private String businessType;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XZFYXZSS_STAUTS")
    private String status;
    @ApiModelProperty("经办人身份证号")
    private String handlePoliceSfzh;
    @ApiModelProperty("经办人")
    private String handlePoliceXm;
    @ApiModelProperty("经办时间")
    private Date handlePoliceTime;
    @ApiModelProperty("转递时间")
    private Date forwardTime;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
