package com.rs.module.acp.controller.app.face;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFaceForm;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.acp.dao.pm.CnpFaceDao;
import com.rs.module.acp.entity.pm.CnpFaceDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Api(tags = "实战平台-移动端-人脸管理")
@RestController
@RequestMapping("/app/acp/face")
@Validated
public class APPFaceController {

    public static final String PersonnelType_Prefix_Police = "police";
    private final String PersonnelType_Prefix_Prisoner = "prisoner";
    private final String PersonnelType_Separator = "_";

    @Autowired
    private FileStorageService fileStorageService;
    @Autowired
    private CnpFaceDao cnpFaceDao;

    /**
     * 上传文件，成功返回文件 url
     */
    @ApiOperation(value = "实战平台-移动端-人脸管理-上传人脸图片")
    @PostMapping("/upload")
    public CommonResult<FileInfo> face(MultipartFile file, HttpServletRequest request) {
        String originalFilename = IdUtil.fastSimpleUUID() + ".jpg";

        FileInfo fileInfo = fileStorageService.of(file)
                .setHashCalculatorMd5()
                .setSaveFilename(originalFilename) //设置保存的文件名，不需要可以不写，会随机生成
                .setObjectId(request.getParameter("objId"))   //关联对象id，为了方便管理，不需要可以不写
                .setObjectType(request.getParameter("objType")) //关联对象类型，为了方便管理，不需要可以不写
                .upload();  //将文件上传到对应地方
        return CommonResult.success(fileInfo);
    }

    @RequestMapping(value = "/importFace", method = RequestMethod.POST)
    @ApiOperation(value = "实战平台-移动端-人脸管理-导入照片", responseContainer = "List")
    public CommonResult<?> importFace(@RequestBody CnpFaceForm form) {
        if (StringUtils.isBlank(form.getPhoto())) {
            return R.ResponseError(400, "参数错误 photo不能为空");
        }
        Integer personnelType;
        if (PersonnelType_Prefix_Prisoner.equals(form.getPersonnelType())) {
            personnelType = 2;
        } else if (form.getPersonnelType() != null && form.getPersonnelType().startsWith(PersonnelType_Prefix_Police)) {
            personnelType = 1;
        } else {
            return R.ResponseError(400, "参数错误 personnelType");
        }
        CnpFaceDO entity = new CnpFaceDO();
        entity.setPhoto(form.getPhoto());
        entity.setUpdateTime(new Date());
        int updateRows = cnpFaceDao.update(entity, new LambdaQueryWrapper<CnpFaceDO>()
                .eq(CnpFaceDO::getPersonnelType, personnelType)
                .eq(CnpFaceDO::getPersonnelCode, form.getPersonnelId()));
        if (updateRows == 0) {
            entity.setId(IdUtil.fastSimpleUUID());
            entity.setPersonnelType(personnelType);
            entity.setPersonnelCode(form.getPersonnelId());
            cnpFaceDao.insert(entity);
        }

        if (form.getPersonnelType().startsWith(PersonnelType_Prefix_Police)) {
            BspDbUtil.updateUserFacePic(form.getPhoto(), form.getPersonnelId());
        }

        return CommonResult.success();
    }
}
