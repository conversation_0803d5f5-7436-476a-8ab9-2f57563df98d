package com.rs.module.acp.controller.admin.pm;

import cn.hutool.core.collection.CollUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.DeviceDoorListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.DeviceDoorRespVO;
import com.rs.module.acp.entity.pm.DeviceDoorDO;
import com.rs.module.acp.service.pm.DeviceDoorService;
import com.rs.third.api.enums.DoorControlTypeEnum;
import com.rs.third.api.model.DoorControlEventPageReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-门禁点管理")
@RestController
@RequestMapping("/acp/pm/deviceDoor")
@Validated
public class DeviceDoorController {

    @Resource
    private DeviceDoorService deviceDoorService;

    @PostMapping("/sync")
    @ApiOperation(value = "同步门禁点信息")
    public CommonResult<String> syncDeviceDoor() {
        deviceDoorService.syncDeviceDoor();
        return success("同步门禁点信息成功");
    }

    @PostMapping("/bindRoom")
    @ApiOperation(value = "门禁绑定监室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "门禁点ID"),
            @ApiImplicitParam(name = "roomId", value = "监室ID"),
            @ApiImplicitParam(name = "roomName", value = "监室名称"),
            @ApiImplicitParam(name = "bindRoomStatus", value = "绑定状态（0-解绑，1-绑定）")
    })
    public CommonResult bindRoom(@RequestParam("id") String id,
                                 @RequestParam("roomId") String roomId,
                                 @RequestParam("roomName") String roomName,
                                 @RequestParam("bindRoomStatus") String bindRoomStatus) {
        deviceDoorService.bindRoom(id, roomId, roomName, bindRoomStatus);
        return success();
    }

    @PostMapping("/getDeviceDoorStatus")
    @ApiOperation(value = "查询门禁点状态")
    @ApiImplicitParam(name = "doorIndexCode", value = "门禁唯一编码")
    public CommonResult getDeviceDoorStatus(@RequestParam("doorIndexCode") String doorIndexCode) {
        return success(deviceDoorService.getDeviceDoorStatus(doorIndexCode));
    }

    @ApiOperation(value = "门禁点反控")
    @PostMapping("/controlDoor")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "doorIndexCode", value = "门禁唯一编码"),
            @ApiImplicitParam(name = "controlType", value = "控制类型（0-常开，1-门闭，2-门开，3-常闭）")
    })
    public CommonResult controlDoor(String doorIndexCode, String controlType) {
        DoorControlTypeEnum typeEnum = DoorControlTypeEnum.getEnum(Integer.parseInt(controlType));
        if (typeEnum == null) {
            return CommonResult.error("控制类型错误");
        }
        return success(deviceDoorService.controlDoor(doorIndexCode, typeEnum));
    }

    @ApiOperation(value = "监室门禁控制")
    @PostMapping("/controlRoomDoor")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roomId", value = "监室号ID"),
            @ApiImplicitParam(name = "controlType", value = "控制类型（0-常开，1-门闭，2-门开，3-常闭）")
    })
    public CommonResult controlRoomDoor(String roomId, String controlType) {
        DoorControlTypeEnum typeEnum = DoorControlTypeEnum.getEnum(Integer.parseInt(controlType));
        if (typeEnum == null) {
            return CommonResult.error("控制类型错误");
        }
        return success(deviceDoorService.controlRoomDoor(roomId, typeEnum));
    }

    @ApiOperation(value = "获取门禁控制事件列表")
    @PostMapping("/getDoorControlEventList")
    public CommonResult getDoorControlEventList(@Valid @RequestBody DoorControlEventPageReq pageReq) {
        List<String> doorIndexCodes = pageReq.getDoorIndexCodes();
        if (CollUtil.isEmpty(doorIndexCodes)) {
            throw new IllegalArgumentException("门禁点唯一编号不能为空");
        }
        return success(deviceDoorService.getDoorControlEventList(pageReq));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-门禁点")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DeviceDoorRespVO> getDeviceDoor(@RequestParam("id") String id) {
        DeviceDoorDO deviceDoor = deviceDoorService.getDeviceDoor(id);
        return success(BeanUtils.toBean(deviceDoor, DeviceDoorRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-门禁点管理列表")
    public CommonResult<List<DeviceDoorRespVO>> getDeviceDoorList(@Valid @RequestBody DeviceDoorListReqVO listReqVO) {
        List<DeviceDoorDO> list = deviceDoorService.getDeviceDoorList(listReqVO);
        return success(BeanUtils.toBean(list, DeviceDoorRespVO.class));
    }

}
