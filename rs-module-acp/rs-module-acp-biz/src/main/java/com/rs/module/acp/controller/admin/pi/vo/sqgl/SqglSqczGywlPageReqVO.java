package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情处置关联业务分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SqglSqczGywlPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所情处置ID")
    private String sqczId;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("业务主表id")
    private String businessId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
