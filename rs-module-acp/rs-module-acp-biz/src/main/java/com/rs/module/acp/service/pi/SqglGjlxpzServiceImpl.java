package com.rs.module.acp.service.pi;

import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglGjlxpzListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglGjlxpzPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglGjlxpzSaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglGjlxpzDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.SqglGjlxpzDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情管理-告警类型配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqglGjlxpzServiceImpl extends BaseServiceImpl<SqglGjlxpzDao, SqglGjlxpzDO> implements SqglGjlxpzService {

    @Resource
    private SqglGjlxpzDao sqglGjlxpzDao;

    @Override
    public String createSqglGjlxpz(SqglGjlxpzSaveReqVO createReqVO) {
        // 插入
        SqglGjlxpzDO sqglGjlxpz = BeanUtils.toBean(createReqVO, SqglGjlxpzDO.class);
        sqglGjlxpzDao.insert(sqglGjlxpz);
        // 返回
        return sqglGjlxpz.getId();
    }

    @Override
    public void updateSqglGjlxpz(SqglGjlxpzSaveReqVO updateReqVO) {
        // 校验存在
        validateSqglGjlxpzExists(updateReqVO.getId());
        // 更新
        SqglGjlxpzDO updateObj = BeanUtils.toBean(updateReqVO, SqglGjlxpzDO.class);
        sqglGjlxpzDao.updateById(updateObj);
    }

    @Override
    public void deleteSqglGjlxpz(String id) {
        // 校验存在
        validateSqglGjlxpzExists(id);
        // 删除
        sqglGjlxpzDao.deleteById(id);
    }

    private void validateSqglGjlxpzExists(String id) {
        if (sqglGjlxpzDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情管理-告警类型配置数据不存在");
        }
    }

    @Override
    public SqglGjlxpzDO getSqglGjlxpz(String id) {
        return sqglGjlxpzDao.selectById(id);
    }

    @Override
    public PageResult<SqglGjlxpzDO> getSqglGjlxpzPage(SqglGjlxpzPageReqVO pageReqVO) {
        return sqglGjlxpzDao.selectPage(pageReqVO);
    }

    @Override
    public List<SqglGjlxpzDO> getSqglGjlxpzList(SqglGjlxpzListReqVO listReqVO) {
        return sqglGjlxpzDao.selectList(listReqVO);
    }


}
