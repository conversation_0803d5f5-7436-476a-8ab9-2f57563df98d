package com.rs.module.acp.service.pm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.KnowledgeBaseSaveReqVO;
import com.rs.module.acp.dao.pm.KnowledgeBaseDao;
import com.rs.module.acp.entity.pm.KnowledgeBaseDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 实战平台-监管管理-知识库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class KnowledgeBaseServiceImpl extends BaseServiceImpl<KnowledgeBaseDao, KnowledgeBaseDO> implements KnowledgeBaseService {

    @Resource
    private KnowledgeBaseDao knowledgeBaseDao;

    @Override
    public String createKnowledgeBase(KnowledgeBaseSaveReqVO createReqVO) {
        // 插入
        KnowledgeBaseDO knowledgeBase = BeanUtils.toBean(createReqVO, KnowledgeBaseDO.class);
        knowledgeBaseDao.insert(knowledgeBase);
        // 返回
        return knowledgeBase.getId();
    }

    @Override
    public void updateKnowledgeBase(KnowledgeBaseSaveReqVO updateReqVO) {
        // 校验存在
        validateKnowledgeBaseExists(updateReqVO.getId());
        // 更新
        KnowledgeBaseDO updateObj = BeanUtils.toBean(updateReqVO, KnowledgeBaseDO.class);
        knowledgeBaseDao.updateById(updateObj);
    }

    @Override
    public void deleteKnowledgeBase(String id) {
        // 校验存在
        validateKnowledgeBaseExists(id);
        // 删除
        knowledgeBaseDao.deleteById(id);
    }

    private void validateKnowledgeBaseExists(String id) {
        if (knowledgeBaseDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-知识库数据不存在");
        }
    }

    @Override
    public KnowledgeBaseDO getKnowledgeBase(String id) {
        return knowledgeBaseDao.selectById(id);
    }

}
