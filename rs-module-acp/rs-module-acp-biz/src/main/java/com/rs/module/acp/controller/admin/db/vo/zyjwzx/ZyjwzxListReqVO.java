package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("申请人类型（1、罪犯本人；2、亲属或者监护人；3、看守所管教民警或者驻看守所医生；4、驻所检察室）")
    private String sqrlx;

    @ApiModelProperty("申请原因（1．患有属于《暂予监外执行规定》所附《保外就医严重疾病范围》的严重疾病，需要保外就医；2．怀孕或者正在哺乳自己婴儿的妇女；3．生活不能自理。）")
    private String sqyy;

    @ApiModelProperty("是否从严审批（1、是；2、否）")
    private String sfcysp;

    @ApiModelProperty("从严审批原因")
    private String cyspyy;

    @ApiModelProperty("联系人")
    private String lxr;

    @ApiModelProperty("联系方式")
    private String lxfs;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("附件材料")
    private String fjcl;

    @ApiModelProperty("申请时间")
    private Date[] operateTime;

    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("经办民警")
    private String operatePolice;

    @ApiModelProperty("所务会初审会议号")
    private String swhcshyh;

    @ApiModelProperty("召开所务会进行初审研究日期")
    private Date zkswhjxcsyjrq;

    @ApiModelProperty("所务会初审结果（1、通过；2、未通过。）")
    private String swhcsjg;

    @ApiModelProperty("未通过原因")
    private String wtgyy;

    @ApiModelProperty("告知申请人未通过原因时间")
    private Date gzsqrwtgyysj;

    @ApiModelProperty("诊断、鉴定或者检查类型（1、病情诊断；2、女性妊娠检查；3、正在哺乳自己婴儿的妇女证明；4、生活不能自理鉴别；5、短期内有生命危险诊断或者评估；6、久治不愈疾病诊断或者评估；7、严重功能障碍评估；8、精神疾病诊断或者评估）")
    private String zdjdhzjclx;

    @ApiModelProperty("诊断、鉴定或者检查结论（1、通过；2、未通过。）")
    private String zdjdhzjcjl;

    @ApiModelProperty("诊断、鉴定或者检查备注")
    private String zdjdhzjcbz;

    @ApiModelProperty("诊断、鉴定或者检查材料")
    private String zdjdhzjccl;

    @ApiModelProperty("诊断、鉴定或者检查经办民警身份证号")
    private String zdjdhzjcjbmjsfzh;

    @ApiModelProperty("诊断、鉴定或者检查经办民警姓名")
    private String zdjdhzjcjbmjxm;

    @ApiModelProperty("诊断、鉴定或者检查经办时间")
    private Date zdjdhzjcjbsj;

    @ApiModelProperty("保证人提出或者推荐人（1、亲属或者监护人；2、执行地村（居）民委员会；3、所在单位或者社区矫正机构）")
    private String bzrtshctjr;

    @ApiModelProperty("bzrzjlx")
    private String bzrzjlx;

    @ApiModelProperty("保证人证件号码")
    private String bzrzjhm;

    @ApiModelProperty("保证人姓名")
    private String bzrxm;

    @ApiModelProperty("保证人联系电话")
    private String bzrlxdh;

    @ApiModelProperty("保外就医审查结论（1、通过；2、未通过。）")
    private String bwjyscjl;

    @ApiModelProperty("保外就医审查备注")
    private String bwjyscbz;

    @ApiModelProperty("保外就医审查材料")
    private String bwjysccl;

    @ApiModelProperty("保外就医审查经办民警身份证号")
    private String bwjyscjbmjsfzh;

    @ApiModelProperty("保外就医审查经办民警姓名")
    private String bwjyscjbmjxm;

    @ApiModelProperty("保外就医审查经办时间")
    private Date bwjyscjbsj;

    @ApiModelProperty("罪犯居住地地址")
    private String zfjzddz;

    @ApiModelProperty("执行地的社区矫正机构")
    private String zxddsqjzjg;

    @ApiModelProperty("有关社会组织")
    private String ygshzz;

    @ApiModelProperty("核实居住地调查评估意见（1、通过；2、未通过。）")
    private String hsjzddcpgyj;

    @ApiModelProperty("核实居住地备注")
    private String hsjzdbz;

    @ApiModelProperty("核实居住地材料")
    private String hsjzdcl;

    @ApiModelProperty("核实居住地经办民警身份证号")
    private String hsjzdjbmjsfzh;

    @ApiModelProperty("保核实居住地经办民警姓名")
    private String hsjzdjbmjxm;

    @ApiModelProperty("核实居住地经办时间")
    private Date hsjzdjbsj;

    @ApiModelProperty("所务会审议时间")
    private Date swhsysj;

    @ApiModelProperty("swhsyhyh")
    private String swhsyhyh;

    @ApiModelProperty("所务会审议结果（1、通过；2、未通过。）")
    private String swhsyjg;

    @ApiModelProperty("swhsywtgyy")
    private String swhsywtgyy;

    @ApiModelProperty("所务会审议备注")
    private String swhsybz;

    @ApiModelProperty("所务会审议材料")
    private String swhsycl;

    @ApiModelProperty("所务会审议经办民警身份证号")
    private String swhsyjbmjsfzh;

    @ApiModelProperty("所务会审议经办民警姓名")
    private String swhsyjbmjxm;

    @ApiModelProperty("所务会审议经办时间")
    private Date swhsyjbsj;

    @ApiModelProperty("是否所内公示（1、是；2、否）")
    private String sfsngs;

    @ApiModelProperty("所内公示开始日期")
    private Date sngsksrq;

    @ApiModelProperty("所内公示截止日期")
    private Date sngsjsrq;

    @ApiModelProperty("是否收到异议（1、是；2、否）")
    private String sfsdyy;

    @ApiModelProperty("所内公示备注")
    private String sngsbz;

    @ApiModelProperty("所内公示材料")
    private String sngscl;

    @ApiModelProperty("所内公示经办民警身份证号")
    private String sngsjbmjsfzh;

    @ApiModelProperty("所内公示经办民警姓名")
    private String sngsjbmjxm;

    @ApiModelProperty("所内公示经办时间")
    private Date sngsjbsj;

    @ApiModelProperty("是否组织听证（1、是；2、否）")
    private String sfzztz;

    @ApiModelProperty("听证日期")
    private Date tzrq;

    @ApiModelProperty("听证情况")
    private String tzqk;

    @ApiModelProperty("听证审核备注")
    private String tzshbz;

    @ApiModelProperty("听证审核材料")
    private String tzshcl;

    @ApiModelProperty("听证审核经办民警身份证号")
    private String tzshjbmjsfzh;

    @ApiModelProperty("听证审核经办民警姓名")
    private String tzshjbmjxm;

    @ApiModelProperty("听证审核经办时间")
    private Date tzshjbsj;

    @ApiModelProperty("驻所检察室监督提请日期")
    private Date zsjcsjdtqrq;

    @ApiModelProperty("驻所检察室监督审核日期")
    private Date zsjcsjdshrq;

    @ApiModelProperty("驻所检察室意见（1、同意；2、不同意。）")
    private String zsjcsyj;

    @ApiModelProperty("驻所检察室监督备注")
    private String zsjcsjdbz;

    @ApiModelProperty("驻所检察室监督材料")
    private String zsjcsjdcl;

    @ApiModelProperty("驻所检察室监督经办民警身份证号")
    private String zsjcsjdjbmjsfzh;

    @ApiModelProperty("驻所检察室监督经办民警姓名")
    private String zsjcsjdjbmjxm;

    @ApiModelProperty("驻所检察室监督经办时间")
    private Date zsjcsjdjbsj;

    @ApiModelProperty("是否公安网站公示（1、是；2、否）")
    private String sfgawzgs;

    @ApiModelProperty("公安机关在网站公示起始日期")
    private Date gajgzwzgsqsrq;

    @ApiModelProperty("公安机关在网站公示截止日期")
    private Date gajgzwzgsjzrq;

    @ApiModelProperty("公安机关公示是否收到异议（1、是；2、否）")
    private String gajggssfsdyy;

    @ApiModelProperty("回复异议日期")
    private Date hfyyrq;

    @ApiModelProperty("回复异议情况")
    private String hfyyqk;

    @ApiModelProperty("公安机关在网站公示备注")
    private String gajgzwzgsbz;

    @ApiModelProperty("公安机关在网站公示材料")
    private String gajgzwzgscl;

    @ApiModelProperty("公安机关在网站公示经办民警身份证号")
    private String gajgzwzgsjbmjsfzh;

    @ApiModelProperty("公安机关在网站公示经办民警姓名")
    private String gajgzwzgsjbmjxm;

    @ApiModelProperty("公安机关在网站公示经办时间")
    private Date gajgzwzgsjdjbsj;

    @ApiModelProperty("公安机关审批报请日期")
    private Date gajgspbqrq;

    @ApiModelProperty("审批机关名称")
    private String spjgmc;

    @ApiModelProperty("公安机关审批结果（1、同意；2、不同意。）")
    private String gajgspjg;

    @ApiModelProperty("公安机关审批备注")
    private String gajgspbz;

    @ApiModelProperty("公安机关审批材料")
    private String gajgspcl;

    @ApiModelProperty("公安机关审批经办民警身份证号")
    private String gajgspjbmjsfzh;

    @ApiModelProperty("公安机关审批经办民警姓名")
    private String gajgspjbmjxm;

    @ApiModelProperty("公安机关审批经办时间")
    private Date gajgspjdjbsj;

    @ApiModelProperty("批准后是否在公安机关网站公开（1、是；2、否。）")
    private String pzhsfzgajgwzgk;

    @ApiModelProperty("批准后在公安机关网站公开日期")
    private Date pzhzgajgwzgkrq;

    @ApiModelProperty("材料送达看守所日期")
    private Date clsdkssrq;

    @ApiModelProperty("执行地与服刑地是否在同一省份（1、是；2、否。）")
    private String zxdyfxdsfztysf;

    @ApiModelProperty("执行地社区矫正机构名称")
    private String zxdsqjzjgmc;

    @ApiModelProperty("执行地接收罪犯档案看守所代码")
    private String zxdjszfdakssdm;

    @ApiModelProperty("执行地接收罪犯档案看守所名称")
    private String zxdjszfdakssmc;

    @ApiModelProperty("状态")
    private String status;

}
