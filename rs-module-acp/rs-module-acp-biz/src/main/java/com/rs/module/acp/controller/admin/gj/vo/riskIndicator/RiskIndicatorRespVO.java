package com.rs.module.acp.controller.admin.gj.vo.riskIndicator;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险指标 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskIndicatorRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("指标类型编码")
    @Trans(type = TransType.DICTIONARY, key = "ZD_FXZB_ZBLX")
    private String indicatorTypeCode;
    @ApiModelProperty("指标名称")
    private String indicatorName;
    @ApiModelProperty("指标描述")
    private String indicatorDescription;
    @ApiModelProperty("评估风险等级")
    @Trans(type = TransType.DICTIONARY, key = "ZD_FXZB_ZBLX")
    private String riskLevel;
    @ApiModelProperty("正向/异常")
    @Trans(type = TransType.DICTIONARY, key = "ZD_FXZB_ZYLX")
    private String positiveAnomalous;
    @ApiModelProperty("查询脚本")
    private String queryScript;
    @ApiModelProperty("展示模板")
    private String displayTemplate;
    @ApiModelProperty("是否启用")
    private Short isEnabled;
}
