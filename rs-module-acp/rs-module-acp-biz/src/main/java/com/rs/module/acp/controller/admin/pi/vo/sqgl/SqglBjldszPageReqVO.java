package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-报警联动设置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SqglBjldszPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所请来源，字典：")
    private String eventSrc;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("可选联动配置，逗号分隔	字典：")
    private String optionalLinkageSettings;

    @ApiModelProperty("提示音，字典：")
    private String promptSound;

    @ApiModelProperty("所属单位编码")
    private String orgCode;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
