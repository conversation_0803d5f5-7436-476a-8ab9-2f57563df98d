package com.rs.module.acp.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.pm.NotifyAssignRecipientDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-通知交办新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class NotifyAssignRecipientReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("接收用户机构编号")
    private String orgCode;
    @ApiModelProperty("接收用户机构名称")
    private String orgName;
    @ApiModelProperty("接收用户身份证号")
    private String idCard;
    @ApiModelProperty("接收用户姓名")
    private String name;

}
