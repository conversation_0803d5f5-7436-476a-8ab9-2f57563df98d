package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.ZyflzdFeedbackReqVO;
import com.rs.module.acp.controller.admin.gj.vo.ZyflzdSaveReqVO;
import com.rs.module.acp.entity.gj.ZyflzdDO;

import javax.validation.Valid;

/**
 * 实战平台-管教业务-专业法律指导 Service 接口
 *
 * <AUTHOR>
 */
public interface ZyflzdService extends IBaseService<ZyflzdDO>{

    /**
     * 创建专业法律指导
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createZyflzd(@Valid ZyflzdSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-专业法律指导
     *
     * @param updateReqVO 更新信息
     */
    void updateZyflzd(@Valid ZyflzdSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-专业法律指导
     *
     * @param id 编号
     */
    void deleteZyflzd(String id);

    /**
     * 获得实战平台-管教业务-专业法律指导
     *
     * @param id 编号
     * @return 实战平台-管教业务-专业法律指导
     */
    ZyflzdDO getZyflzd(String id);

    /**
     * 专业法律指导反馈
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void feedback(@Valid ZyflzdFeedbackReqVO createReqVO);


}
