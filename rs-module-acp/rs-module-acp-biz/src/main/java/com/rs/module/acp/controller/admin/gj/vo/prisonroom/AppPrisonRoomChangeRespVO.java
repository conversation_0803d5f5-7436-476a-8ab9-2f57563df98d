package com.rs.module.acp.controller.admin.gj.vo.prisonroom;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - app端-实战平台-管教业务-监室调整 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppPrisonRoomChangeRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("旧监室名称")
    private String oldRoomName;
    @ApiModelProperty("新监室名称")
    private String newRoomName;
    @ApiModelProperty("调整原因")
    private String changeReason;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_JSTZZT")
    private String status;
    @ApiModelProperty("流程实例ID")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

}
