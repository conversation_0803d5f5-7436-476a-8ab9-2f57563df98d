package com.rs.module.acp.service.pi;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.pi.SqglSqdjTbpzDO;

import com.rs.module.acp.dao.pi.SqglSqdjTbpzDao;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.List;


/**
 * 实战平台-巡视管控-所情管理-所情登记-同步配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqglSqdjTbpzServiceImpl extends BaseServiceImpl<SqglSqdjTbpzDao, SqglSqdjTbpzDO> implements SqglSqdjTbpzService {

    @Resource
    private SqglSqdjTbpzDao sqglSqdjTbpzDao;

    @Override
    public List<JSONObject> getSyncList(String sql) {
        return sqglSqdjTbpzDao.getSyncList(sql);
    }
}
