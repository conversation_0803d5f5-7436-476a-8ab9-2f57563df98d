package com.rs.module.acp.controller.app.gj.vo;


import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "app端 - bsp审批流程 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ApprovalVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("节点名称")
    private String taskName;

    @ApiModelProperty("审批人")
    private String executeUserName;

    @ApiModelProperty("审批意见")
    private String approvalContent;

    @ApiModelProperty("审批时间")
    private Date endTime;

    @ApiModelProperty("审批状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BPM_APPROVE_STATUS")
    private String status;


}
