package com.rs.module.acp.service.gj;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.gj.vo.*;
import com.rs.module.acp.entity.gj.GjPersonalEffectsDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-管教业务-随身物品登记 Service 接口
 *
 * <AUTHOR>
 */
public interface GjPersonalEffectsService extends IBaseService<GjPersonalEffectsDO>{

    /**
     * 创建实战平台-管教业务-随身物品登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGjPersonalEffects(@Valid GjPersonalEffectsSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-随身物品登记
     *
     * @param updateReqVO 更新信息
     */
    void updateGjPersonalEffects(@Valid GjPersonalEffectsSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-随身物品登记
     *
     * @param id 编号
     */
    void deleteGjPersonalEffects(String id);

    /**
     * 获得实战平台-管教业务-随身物品登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-随身物品登记
     */
    GjPersonalEffectsDO getGjPersonalEffects(String id);

    /**
    * 获得实战平台-管教业务-随身物品登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-随身物品登记分页
    */
    PageResult<GjPersonalEffectsDO> getGjPersonalEffectsPage(GjPersonalEffectsPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-随身物品登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-随身物品登记列表
    */
    List<GjPersonalEffectsDO> getGjPersonalEffectsList(GjPersonalEffectsListReqVO listReqVO);


}
