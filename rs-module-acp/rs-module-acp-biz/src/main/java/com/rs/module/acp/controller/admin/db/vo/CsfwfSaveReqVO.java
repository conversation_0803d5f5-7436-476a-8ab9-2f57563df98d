package com.rs.module.acp.controller.admin.db.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-出所防误放新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CsfwfSaveReqVO extends BaseVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监室ID")
    @NotEmpty(message = "监室ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("业务类型（1：出所、2：入所）")
    @NotEmpty(message = "业务类型（1：出所、2：入所）不能为空")
    private String businessType;

    @ApiModelProperty("业务原因")
    @NotEmpty(message = "业务原因不能为空")
    private String businessReason;

    @ApiModelProperty("对比方式")
    @NotEmpty(message = "对比方式不能为空")
    private String verificationMode;

    @ApiModelProperty("核验结果（01：待核验，02：验证成功，03：验证失败）")
    private String checkResult;

    @ApiModelProperty("申请时间")
    @NotNull(message = "申请时间不能为空")
    private Date operateTime;

    @ApiModelProperty("经办民警身份证号")
    @NotEmpty(message = "经办民警身份证号不能为空")
    private String operatePoliceSfzh;

    @ApiModelProperty("经办民警")
    @NotEmpty(message = "经办民警不能为空")
    private String operatePolice;

    @ApiModelProperty("强制类型（1：强制带出，2：强制带入）")
    private String mandatoryType;

    @ApiModelProperty("强制原因")
    private String mandatoryReason;

    @ApiModelProperty("性别")
    private String xb;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("被监管人员ID")
    @NotEmpty(message = "被监管人员ID不能为空")
    private String prisonerId;
    @ApiModelProperty("关联业务ID")
    @NotEmpty(message = "关联业务ID不能为空")
    private String businessId;

}
