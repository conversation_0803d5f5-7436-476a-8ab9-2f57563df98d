package com.rs.module.acp.service.ds.sjbs;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.ds.vo.sjbs.DailyDataSubmitKssSaveReqVO;
import com.rs.module.acp.dao.ds.sjbs.DailyDataSubmitKssDao;
import com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitKssDO;
import com.rs.util.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 实战平台-数据固化-每日数据报送(看守所) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DailyDataSubmitKssServiceImpl extends BaseServiceImpl<DailyDataSubmitKssDao, DailyDataSubmitKssDO> implements DailyDataSubmitKssService {

    @Resource
    private DailyDataSubmitKssDao dailyDataSubmitKssDao;

    @Override
    public String createDailyDataSubmitKss(DailyDataSubmitKssSaveReqVO createReqVO) {
        // 插入
        DailyDataSubmitKssDO dailyDataSubmitKss = BeanUtils.toBean(createReqVO, DailyDataSubmitKssDO.class);
        dailyDataSubmitKssDao.insert(dailyDataSubmitKss);
        // 返回
        return dailyDataSubmitKss.getId();
    }

    @Override
    public void updateDailyDataSubmitKss(DailyDataSubmitKssSaveReqVO updateReqVO) {
        // 校验存在
        validateDailyDataSubmitKssExists(updateReqVO.getId());
        // 更新
        DailyDataSubmitKssDO updateObj = BeanUtils.toBean(updateReqVO, DailyDataSubmitKssDO.class);
        dailyDataSubmitKssDao.updateById(updateObj);
    }

    @Override
    public void deleteDailyDataSubmitKss(String id) {
        // 校验存在
        validateDailyDataSubmitKssExists(id);
        // 删除
        dailyDataSubmitKssDao.deleteById(id);
    }

    private void validateDailyDataSubmitKssExists(String id) {
        if (dailyDataSubmitKssDao.selectById(id) == null) {
            throw new ServerException("实战平台-数据固化-每日数据报送(看守所)数据不存在");
        }
    }

    @Override
    public DailyDataSubmitKssDO getDailyDataSubmitKss(String id) {
        return dailyDataSubmitKssDao.selectById(id);
    }

    @Override
    public void saveForStatistic(String orgCode, String startDate, String endDate){
        String solidificationDate =DateUtil.format(DateUtil.getYesterday(), DateUtil.DATE_PATTERN_OF_BAR);
        if(StringUtil.isEmpty(startDate)) startDate= solidificationDate;
        List<DailyDataSubmitKssDO> list = dailyDataSubmitKssDao.statisticNum(orgCode,startDate,endDate);
        //根据solidificationDate=当前时间减1天 查询数据
        dailyDataSubmitKssDao.deleteByCondition(orgCode,startDate);
        dailyDataSubmitKssDao.insertBatch(list);
    }

    @Override
    public DailyDataSubmitKssDO getDailyDataSubmitKssBySolidificationDate(String solidificationDate, String orgCode) {
        if(StringUtil.isEmpty(solidificationDate)) solidificationDate= DateUtil.format(DateUtil.getYesterday(), DateUtil.DATE_PATTERN_OF_BAR);
        DailyDataSubmitKssDO entity = dailyDataSubmitKssDao.selectOne(new LambdaQueryWrapperX<DailyDataSubmitKssDO>().
                eq(DailyDataSubmitKssDO::getSolidificationDate, solidificationDate).
                eq(DailyDataSubmitKssDO::getOrgCode, orgCode).eq(DailyDataSubmitKssDO::getIsDel, 0));
        return entity;
    }
}
