package com.rs.module.acp.controller.admin.pi;

import com.rs.module.acp.controller.admin.pi.vo.sqgl.*;
import com.rs.module.acp.entity.pi.SqglSqczDO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglSqdjDO;
import com.rs.module.acp.service.pi.SqglSqdjService;

@Api(tags = "实战平台-巡视管控-所情管理-所情登记")
@RestController
@RequestMapping("/acp/pi/sqglSqdj")
@Validated
public class SqglSqdjController {

    @Resource
    private SqglSqdjService sqglSqdjService;

    @PostMapping("/create")
    @ApiOperation(value = "所情登记")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:create", operateType = LogOperateType.CREATE, title = "创建实战平台-巡视管控-所情管理-所情登记",
            success = "创建实战平台-巡视管控-所情管理-所情登记成功",
            fail = "创建实战平台-巡视管控-所情管理-所情登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createSqglSqdj(@Valid @RequestBody SqglSqdjSaveReqVO createReqVO) {
        return success(sqglSqdjService.createSqglSqdj(createReqVO));
    }

    @PostMapping("/verify")
    @ApiOperation(value = "所情核实")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:verify", operateType = LogOperateType.UPDATE, title = "创建实战平台-巡视管控-所情管理-所情核实",
            success = "创建实战平台-巡视管控-所情管理-所情核实成功",
            fail = "创建实战平台-巡视管控-所情管理-所情核实失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> verify(@Valid @RequestBody SqglSqdjSaveReqVO updateReqVO) {
        return success(sqglSqdjService.verify(updateReqVO));
    }

    @PostMapping("/dispose")
    @ApiOperation(value = "所情处置")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:dispose", operateType = LogOperateType.UPDATE, title = "创建实战平台-巡视管控-所情管理-所情处置",
            success = "创建实战平台-巡视管控-所情管理-所情处置成功",
            fail = "创建实战平台-巡视管控-所情管理-所情处置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> dispose(@Valid @RequestBody SqglSqczSaveReqVO updateReqVO) {
        return success(sqglSqdjService.dispose(updateReqVO));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "领导审批")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:dispose", operateType = LogOperateType.UPDATE, title = "创建实战平台-巡视管控-所情管理-领导审批",
            success = "创建实战平台-巡视管控-所情管理-领导审批成功",
            fail = "创建实战平台-巡视管控-所情管理-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> approve(@Valid @RequestBody SqglSqczApproveSaveReqVO updateReqVO) {
        return success(sqglSqdjService.approve(updateReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获取所情登记详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:get", operateType = LogOperateType.QUERY, title = "获取所情登记详情",
            success = "获取所情登记详情成功",
            fail = "获取所情登记详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<SqglSqdjRespVO> getSqglSqdj(@RequestParam("id") String id) {
        return success(sqglSqdjService.getSqglSqdjById(id));
    }

    @GetMapping("/getCurrent")
    @ApiOperation(value = "获取当前用户需办理的业务内容（所情处置、领导审批时使用）")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:getCurrent", operateType = LogOperateType.QUERY, title = "获取所情登记详情",
            success = "获取所情登记详情成功",
            fail = "获取所情登记详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<SqglSqczRespVO> getCurrent(@RequestParam("id") String id) {
        return success(sqglSqdjService.getCurrent(id));
    }

}
