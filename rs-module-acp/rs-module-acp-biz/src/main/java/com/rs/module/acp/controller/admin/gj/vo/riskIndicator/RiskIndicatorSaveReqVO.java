package com.rs.module.acp.controller.admin.gj.vo.riskIndicator;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险指标新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskIndicatorSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("指标类型编码")
    @NotEmpty(message = "指标类型编码不能为空")
    private String indicatorTypeCode;

    @ApiModelProperty("指标名称")
    @NotEmpty(message = "指标名称不能为空")
    private String indicatorName;

    @ApiModelProperty("指标描述")
    @NotEmpty(message = "指标描述不能为空")
    private String indicatorDescription;

    @ApiModelProperty("评估风险等级")
    private String riskLevel;

    @ApiModelProperty("正向/异常")
    @NotEmpty(message = "正向/异常不能为空")
    private String positiveAnomalous;

    @ApiModelProperty("查询脚本")
    @NotEmpty(message = "查询脚本不能为空")
    private String queryScript;

    @ApiModelProperty("展示模板")
    @NotEmpty(message = "展示模板不能为空")
    private String displayTemplate;

    @ApiModelProperty("是否启用 字典：ZD_TQ_TYPE    0否 1是")
    @NotNull(message = "是否启用不能为空")
    private Short isEnabled;

}
