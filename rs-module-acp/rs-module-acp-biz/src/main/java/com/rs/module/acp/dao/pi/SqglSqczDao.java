package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczPageReqVO;
import com.rs.module.acp.entity.pi.SqglSqczDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情管理-所情处置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SqglSqczDao extends IBaseDao<SqglSqczDO> {


    default PageResult<SqglSqczDO> selectPage(SqglSqczPageReqVO reqVO) {
        Page<SqglSqczDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SqglSqczDO> wrapper = new LambdaQueryWrapperX<SqglSqczDO>()
            .eqIfPresent(SqglSqczDO::getSqdjId, reqVO.getSqdjId())
            .eqIfPresent(SqglSqczDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
            .likeIfPresent(SqglSqczDO::getHandleUserName, reqVO.getHandleUserName())
            .betweenIfPresent(SqglSqczDO::getHandleTime, reqVO.getHandleTime())
            .likeIfPresent(SqglSqczDO::getHandlePostName, reqVO.getHandlePostName())
            .eqIfPresent(SqglSqczDO::getHandlePostCode, reqVO.getHandlePostCode())
            .eqIfPresent(SqglSqczDO::getHanleGenericPlan, reqVO.getHanleGenericPlan())
            .eqIfPresent(SqglSqczDO::getHandleInfo, reqVO.getHandleInfo())
            .eqIfPresent(SqglSqczDO::getHandleType, reqVO.getHandleType())
            .eqIfPresent(SqglSqczDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(SqglSqczDO::getStatus, reqVO.getStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SqglSqczDO::getAddTime);
        }
        Page<SqglSqczDO> sqglSqczPage = selectPage(page, wrapper);
        return new PageResult<>(sqglSqczPage.getRecords(), sqglSqczPage.getTotal());
    }
    default List<SqglSqczDO> selectList(SqglSqczListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SqglSqczDO>()
            .eqIfPresent(SqglSqczDO::getSqdjId, reqVO.getSqdjId())
            .eqIfPresent(SqglSqczDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
            .likeIfPresent(SqglSqczDO::getHandleUserName, reqVO.getHandleUserName())
            .betweenIfPresent(SqglSqczDO::getHandleTime, reqVO.getHandleTime())
            .likeIfPresent(SqglSqczDO::getHandlePostName, reqVO.getHandlePostName())
            .eqIfPresent(SqglSqczDO::getHandlePostCode, reqVO.getHandlePostCode())
            .eqIfPresent(SqglSqczDO::getHanleGenericPlan, reqVO.getHanleGenericPlan())
            .eqIfPresent(SqglSqczDO::getHandleInfo, reqVO.getHandleInfo())
            .eqIfPresent(SqglSqczDO::getHandleType, reqVO.getHandleType())
            .eqIfPresent(SqglSqczDO::getAttUrl, reqVO.getAttUrl())
            .eqIfPresent(SqglSqczDO::getStatus, reqVO.getStatus())
        .orderByDesc(SqglSqczDO::getAddTime));    }

    }
