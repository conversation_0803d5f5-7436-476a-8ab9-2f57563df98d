package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxSwhsyReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所务会审议时间")
    private Date swhsysj;

    @ApiModelProperty("所务会审议会议号")
    private String swhsyhyh;

    @ApiModelProperty("所务会审议结果（1、通过；2、未通过。）")
    private String swhsyjg;

    @ApiModelProperty("所务会审议未通过原因")
    private String swhsywtgyy;

    @ApiModelProperty("所务会审议备注")
    private String swhsybz;

    @ApiModelProperty("所务会审议材料")
    private String swhsycl;

    @ApiModelProperty("告知申请人时间")
    private Date gssqrsj;

}
