package com.rs.module.acp.entity.gj;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-管教业务-风险指标 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_risk_indicator")
@KeySequence("acp_gj_risk_indicator_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_risk_indicator")
public class RiskIndicatorDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 指标类型编码
     */
    private String indicatorTypeCode;
    /**
     * 指标名称
     */
    private String indicatorName;
    /**
     * 指标描述
     */
    private String indicatorDescription;
    /**
     * 评估风险等级
     */
    private String riskLevel;
    /**
     * 正向/异常
     */
    private String positiveAnomalous;
    /**
     * 查询脚本
     */
    private String queryScript;
    /**
     * 展示模板
     */
    private String displayTemplate;
    /**
     * 是否启用
     */
    private Short isEnabled;

}
