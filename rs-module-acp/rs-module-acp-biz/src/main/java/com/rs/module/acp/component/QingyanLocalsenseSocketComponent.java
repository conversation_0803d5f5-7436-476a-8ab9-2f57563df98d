package com.rs.module.acp.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.base.StringRedisService;
import com.bsp.common.util.StringUtil;
import com.rs.module.acp.config.QingyanDeviceProperties;
import com.rs.module.acp.util.QingyanLocalSenseUtil;
import com.tsingoal.LsWebsocketClient;
import com.tsingoal.enumerate.LsFrameEnum;
import com.tsingoal.enumerate.LsWebsocketStateEnum;
import com.tsingoal.pojo.*;
import com.tsingoal.pojo.init.LsWebsocketParam;
import com.tsingoal.service.LsWebsocketDataService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Log4j2
@Scope("singleton") // 确保Spring容器中唯一实例
public class QingyanLocalsenseSocketComponent extends LsWebsocketDataService implements DisposableBean {
    private static final AtomicReference<LsWebsocketClient> CLIENT_INSTANCE = new AtomicReference<>();
    private static final Lock connectionLock = new ReentrantLock();

    private final QingyanDeviceProperties deviceProperties;
    private final LsWebsocketParam connectionParam;

    @Autowired
    private StringRedisService stringRedisService;
    /**
     * 获取实时数据
     * Get real-time data
     * @param object Object 转换的类型
     * @param object Object Type of conversion
     * @param type 实时数据类型 <LsFrameEnum>
     * @param type Real-time data type <LsFrameEnum>
     */
    @Override
    public void onDataInfo(Object object, LsFrameEnum type) {
        switch (type) {
            case POS://位置信息
                List<LsTagPosition> poses = (List<LsTagPosition>) object;
                /*for (LsTagPosition pos : poses) {
                    log.info("recv LsTagPosition: " + pos.toString());
                }*/
                setPosRedis(poses);
                break;
            case ALARM://详细报警信息
                LsExtendAlarm alarm = (LsExtendAlarm) object;
                //log.info("recv LsExtendAlarm " + alarm);
                break;

            case BASE://基站状态信息
                List<LsBaseStatus> baseStatuses  = (List<LsBaseStatus>) object;
                for (LsBaseStatus status : baseStatuses) {
                    //log.info("recv LsBaseStatus: " + status.toString());

                }
                break;
            case CAP://电量信息
                List<LsTagBattery> batteries  = (List<LsTagBattery>) object;
                //setCapRedis(batteries);
                break;
            case AREASTA://区域统计信息
                LsRegionStatistics regionStatistics  = (LsRegionStatistics) object;
                // log.info("recv regionStatistics: " + regionStatistics.toString());
                break;
            case TAGNUM://标签数量统计
                LsTagStatisticsExt personStatistics  = (LsTagStatisticsExt) object;
                //log.info("recv LsTagStatisticsExt: " + personStatistics.toString());
                break;
            case AREAINOUT://区域进出信息
                LsRegionInOut regionInOut  = (LsRegionInOut) object;
                // log.info("recv LsRegionInOut: " + regionInOut.toString());
                break;
            case EXTRADATA:
                LsTagExtendData heartRate  = (LsTagExtendData) object;
                // log.info("recv LsTagHeartRate: " + heartRate.toString());
                break;
            case VITALSIGN://标签扩展信息包含定位标签所拥有的心率、血氧和体温数据。
                LsVitalSignInfo vitalSignInfo  = (LsVitalSignInfo) object;
                //log.info("recv LsVitalSignInfo: " + vitalSignInfo.toString());
                setVitalsignRedis(vitalSignInfo);
                break;
        }
    }
    public void setCapRedis(List<LsTagBattery> batteries){
        batteries.stream().forEach(b -> {
            String key =QingyanLocalSenseUtil.getCapKey(b.getTagId());
            setRedisCommon(key,b.getBatteryPercent()+"",3600,redisKeyRefreshInterval);
        });
    }
    public void setVitalsignRedis(LsVitalSignInfo vitalSignInfos){
        String key = QingyanLocalSenseUtil.getVitalsignKey(vitalSignInfos.getTagId(),vitalSignInfos.getType());
        String value = JSON.toJSONString(vitalSignInfos);
        setRedisCommon(key,value,3600,redisKeyRefreshInterval);
    }
    public void setPosRedis(List<LsTagPosition> poses){
        poses.stream().forEach(b -> {
            String key = QingyanLocalSenseUtil.getPosKey(b.getTagId());
            // 1. 获取上次存储的位置
            LsTagPosition lastPosition = null;
            String json = stringRedisService.getString( key);
            if(StringUtil.isNotEmpty(json)){
                json = json.replaceAll("\\u0000", "");
                lastPosition = JSONObject.parseObject(json, LsTagPosition.class);

            };
            // 2. 判断位置变化是否超过阈值
            if (lastPosition != null && !isPositionChanged(lastPosition, b)) {
                //log.debug("位置变化未达阈值，跳过存储 - TagID: {}", b.getTagId());
                return;
            }
            setRedisCommon(key,JSON.toJSONString(b),3600,redisKeyRefreshInterval);
        });
    }

    private static long redisKeyRefreshInterval=1000*10;
    /**
     * 带频率控制的Redis写入方法
     * @param key Redis key
     * @param value 要写入的值
     * @param expireTime key过期时间(秒)
     */
    public void setRedisCommon(String key, String value, int expireTime,long redisKeyRefreshInterval) {
        try {
            // 1. 检查上次写入时间
            String lastWriteKey = key + ":last_write";
            /*Object objLastWriteTime = redisTemplate.opsForValue().get(lastWriteKey);
            Long lastWriteTime = null;
            if(objLastWriteTime != null) lastWriteTime = (long)objLastWriteTime;*/
            Long lastWriteTime = StringUtil.isEmpty(stringRedisService.getString(lastWriteKey))  ? 0 : Long.parseLong(stringRedisService.getString(lastWriteKey));
            long currentTime = System.currentTimeMillis();

            // 2. 频率控制判断
            if (lastWriteTime != null &&  currentTime - lastWriteTime < redisKeyRefreshInterval) {
                //log.debug("Key {} 刷新频率过高，跳过本次写入", key);
                return;
            }
            value = value.replaceAll("\\u0000", "");
            // 3. 写入数据
            stringRedisService.set(key,value);
            // 4. 记录本次写入时间
            stringRedisService.set(lastWriteKey,currentTime+"");
        } catch (Exception e) {
            log.error("Redis写入失败 - Key: {}", key, e);
        }
    }
    /**
     * 判断位置是否发生显著变化
     * @param oldPos 旧位置
     * @param newPos 新位置
     * @return 是否超过变化阈值
     */
    private boolean isPositionChanged(LsTagPosition oldPos, LsTagPosition newPos) {
        double dx = Math.abs(oldPos.getX() - newPos.getX());
        double dy = Math.abs(oldPos.getY() - newPos.getY());
        double dz = Math.abs(oldPos.getZ() - newPos.getZ());

        // 任一坐标变化超过阈值即认为位置变化
        return dx > deviceProperties.getPositionChangeThresholdX() ||
                dy > deviceProperties.getPositionChangeThresholdY() ||
                dz > deviceProperties.getPositionChangeThresholdZ();
    }
    /**
     * websocket 连接状态
     * Websocket connection status
     * @param state LsWebsocketStateEnum
     */
    @Override
    public void onWebsocketState(LsWebsocketStateEnum state) {
        log.info("websocket state change, state: " + state.name());
    }

    @Autowired
    public QingyanLocalsenseSocketComponent(QingyanDeviceProperties deviceProperties) {
        this.deviceProperties = deviceProperties;
        this.connectionParam = initConnectionParam();
    }

    private LsWebsocketParam initConnectionParam() {
        LsWebsocketParam param = new LsWebsocketParam();
        param.setServerInfo(deviceProperties.getServerIp(), deviceProperties.getWebsocketPort(), "localSensePush-protocol", false);
        param.setUserInfo(deviceProperties.getUsername(), deviceProperties.getPassword(), null);
        return param;
    }
    /**
     * 在所有依赖注入完成后执行
     */
    @PostConstruct
    private void init() {
        if (deviceProperties.isEnableWebSocketConnection()) {
            ensureConnection();
        } else {
            log.info("清研手环 WebSocket连接未启用");
        }
    }
    public void ensureConnection() {
        if (CLIENT_INSTANCE.get() == null) {
            connectionLock.lock();
            try {
                if (CLIENT_INSTANCE.get() == null) {
                    CLIENT_INSTANCE.set(new LsWebsocketClient(this, connectionParam));
                    CLIENT_INSTANCE.get().connectToServer();
                    log.info("清研手环 WebSocket全局连接已建立");
                }
            } finally {
                connectionLock.unlock();
            }
        }
    }

    @Override
    public void destroy() throws Exception {
        connectionLock.lock();
        try {
            if (CLIENT_INSTANCE.get() != null) {
                CLIENT_INSTANCE.get().closeWebSocket();
                CLIENT_INSTANCE.set(null);
                log.info("清研手环 WebSocket全局连接已关闭");
            }
        } finally {
            connectionLock.unlock();
        }
    }
}