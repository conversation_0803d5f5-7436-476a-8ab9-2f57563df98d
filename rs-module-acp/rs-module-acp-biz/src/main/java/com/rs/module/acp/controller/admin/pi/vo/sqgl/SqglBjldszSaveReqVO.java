package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-报警联动设置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglBjldszSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所请来源，字典：")
    @NotEmpty(message = "所请来源，字典：不能为空")
    private String eventSrc;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("视频异常报警简介")
    private String remark;

    @ApiModelProperty("可选联动配置，逗号分隔	字典：")
    private String optionalLinkageSettings;

    @ApiModelProperty("提示音，字典：")
    private String promptSound;

    @ApiModelProperty("告警类型配置列表")
    private List<SqglGjlxpzSaveReqVO> gjlxList;

}
