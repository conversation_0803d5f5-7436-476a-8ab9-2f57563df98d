package com.rs.module.acp.service.db;

import com.bsp.common.cache.DicUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.InProcessStageEnum;
import com.rs.module.acp.cons.InTypeEnums;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.dao.db.HealthCheckDao;
import com.rs.module.acp.entity.db.HealthCheckDO;
import com.rs.module.acp.service.db.components.PrisonerInInfoSyncContext;
import com.rs.module.acp.service.db.components.RegistrationInfoService;
import com.rs.module.acp.service.ds.DSPrisonRoomChangeService;
import com.rs.module.acp.util.GeneralUtil;
import com.rs.util.DateUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.InRecordJlsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.InRecordJlsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-入所登记（拘留所） Service 实现类
 *
 * <AUTHOR>
 */
@Service("inRecordJlsService")
@Validated
public class InRecordJlsServiceImpl extends BaseServiceImpl<InRecordJlsDao, InRecordJlsDO> implements InRecordJlsService, RegistrationInfoService {

    @Resource
    private InRecordJlsDao inRecordJlsDao;
    @Resource
    private DbSocialRelationsService socialRelationsService;

    @Resource
    private HealthCheckDao healthCheckDao;
    @Resource
    private PrisonerInInfoSyncContext prisonerInInfoSyncContext;

    @Resource
    private DSPrisonRoomChangeService dsPrisonRoomChangeService;
    @Override
    public String createInRecordJls(InRecordJlsSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();

        String rybh = GeneralUtil.generateRybh(orgCode);
        //演示数据使用##########
        if(createReqVO.getZjhm().equals("120101198410100125")){
            rybh = "110000121202507010834";
        }
        if(createReqVO.getZjhm().equals("440106198208153768")){
            rybh = "110000121202507010047";
        }
        // 插入

        //如果提交的状态为操作02，则设置当前阶段为01
        if (createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())) {
            createReqVO.setCurrentStep(InProcessStageEnum.PENDING_REGISTRATION.getCode());
        }if (createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())) {
            createReqVO.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
        }
        InRecordJlsDO inRecordJlsDO = BeanUtils.toBean(createReqVO, InRecordJlsDO.class);
        inRecordJlsDO.setId(GeneralUtil.generateUUID());
        inRecordJlsDO.setRybh(rybh);
        inRecordJlsDO.setJgrybm(rybh);
        //如果是快速入所类型，则设置所领导审批为待审批
        if (createReqVO.getRslx().equalsIgnoreCase(InTypeEnums.EMERGENCY.getCode())&&createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())) {
            inRecordJlsDO.setSpzt(LeaderApprovalStatusEnum.PENDING.getCode());
            inRecordJlsDO.setCurrentStep(InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode());
        }
        inRecordJlsDao.insert(inRecordJlsDO);

        // 插入子表
        List<dbSocialRelationsSaveReqVO> socialRelations = createReqVO.getSocialRelations();
        if (socialRelations != null && !socialRelations.isEmpty()) {
            for (dbSocialRelationsSaveReqVO socialRelation : socialRelations) {
                socialRelation.setId(UUID.randomUUID().toString().replaceAll("-", ""));
                socialRelation.setRybh(inRecordJlsDO.getRybh());
                socialRelationsService.createdbSocialRelations(socialRelation);
            }
        }
        // 返回
        return inRecordJlsDO.getJgrybm();
    }

    @Override
    public void updateInRecordJls(InRecordJlsSaveReqVO updateReqVO) {
        // 校验存在
        validateInRecordJlsExists(updateReqVO.getId());
        // 更新
        InRecordJlsDO updateObj = BeanUtils.toBean(updateReqVO, InRecordJlsDO.class);
        inRecordJlsDao.updateById(updateObj);
    }

    @Override
    public void deleteInRecordJls(String id) {
        // 校验存在
        validateInRecordJlsExists(id);
        // 删除
        inRecordJlsDao.deleteById(id);
    }

    private void validateInRecordJlsExists(String id) {
        if (inRecordJlsDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-入所登记（拘留所）数据不存在");
        }
    }

    @Override
    public InRecordJlsDO getInRecordJls(String id) {
        return inRecordJlsDao.selectById(id);
    }

    @Override
    public PageResult<InRecordJlsDO> getInRecordJlsPage(InRecordJlsPageReqVO pageReqVO) {
        return inRecordJlsDao.selectPage(pageReqVO);
    }

    @Override
    public List<InRecordJlsDO> getInRecordJlsList(InRecordJlsListReqVO listReqVO) {
        return inRecordJlsDao.selectList(listReqVO);
    }

    @Override
    public CombineRespVO getCombineInfo(String rybh) {
        InRecordJlsListReqVO reqVO = new InRecordJlsListReqVO();
        reqVO.setRybh(rybh);
        List<InRecordJlsDO> dlist =  inRecordJlsDao.selectList(reqVO);

        InRecordJlsDO db = new InRecordJlsDO();
        if(dlist!=null&& !dlist.isEmpty()){
            db = dlist.get(0);
        } else if (dlist!=null&&dlist.size()==0) {
            return null;
        }
//        DetainRegKssDO db = detainRegKssDao.selectById(rybh);
        HealthCheckListReqVO listReqVO = new HealthCheckListReqVO();
        listReqVO.setRybh(db.getRybh());
        List<HealthCheckDO> list = healthCheckDao.selectList(listReqVO);
        CombineRespVO combineRespVO = BeanUtils.toBean(db, CombineRespVO.class);
        if(list!=null&&list.size()>0){
            HealthCheckDO healthCheckDO = list.get(0);
            combineRespVO.setYsyj(healthCheckDO.getYsyj());
            combineRespVO.setJcr(healthCheckDO.getJcr());
            combineRespVO.setJcsj(healthCheckDO.getJcsj());
            combineRespVO.setBz(healthCheckDO.getBz());
        }

        return combineRespVO;
    }

    /**
     * 更新领导审批状态
     *
     * 此方法首先验证指定的被拘留人员登记记录是否存在，然后更新其领导审批状态
     *
     * @param updateReqVO 包含更新请求数据的实体类
     */
    @Override
    public void updateLeaderApprovalStatus(LeaderApprovalStatusReqVO updateReqVO) {
        // 校验存在
//    validateDetainRegKssExists(updateReqVO.getId());
        InRecordJlsDO inRecordJlsDO = new InRecordJlsDO();
        if(updateReqVO.getRybh()!=null&&updateReqVO.getRybh()!=""){
            inRecordJlsDO = getPrisonerInfo(updateReqVO.getRybh());
        }

        //入所类型
        String rslx = inRecordJlsDO.getRslx();
        // 更新
        InRecordJlsDO updateObj = BeanUtils.toBean(updateReqVO, InRecordJlsDO.class);
        updateObj.setId(inRecordJlsDO.getId());
        //如果是快速入所，所领导审批完成后流转到健康检查
        if(rslx!=null&&rslx.equalsIgnoreCase("02")){
            //原快速入所所领导审批后流转到健康登记
//            updateObj.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
            //现所领导审批后需流转到第一步登记信息，便于补录信息
            updateObj.setCurrentStep(InProcessStageEnum.PENDING_REGISTRATION.getCode());
            updateObj.setStatus(DetainRegStatusEnum.DRAFT.getCode());
        }else {
            updateObj.setCurrentStep(InProcessStageEnum.COMPLETED.getCode());
        }
        inRecordJlsDao.updateById(updateObj);

        //如果领导审批完成了，则从acp_db_in_record_kss写入在所表acp_pm_prisoner_kss_in
        syncDataToPrisonerJlsIn(updateReqVO);
        try {
            dsPrisonRoomChangeService.jlsSave(inRecordJlsDO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void syncDataToPrisonerJlsIn(LeaderApprovalStatusReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();
        String id = updateReqVO.getId();
        //领导同意，则同步数据
        if(updateReqVO.getSpzt()!=null&&updateReqVO.getSpzt().equalsIgnoreCase(LeaderApprovalStatusEnum.APPROVED.getCode())){
            inRecordJlsDao.syncDataToPrisonerJlsIn(id);
        }

        //同步社会关系数据
        prisonerInInfoSyncContext.getHandler("jls").syncSocialRelations(updateReqVO.getRybh(), orgCode,updateReqVO.getId());
    }


    @Override
    public InRecordJlsDO getPrisonerInfo(String rybh) {
        InRecordJlsListReqVO reqVO = new InRecordJlsListReqVO();
        reqVO.setRybh(rybh);
        List<InRecordJlsDO> dlist =  inRecordJlsDao.selectList(reqVO);

        InRecordJlsDO db = new InRecordJlsDO();
        if(dlist!=null&& !dlist.isEmpty()){
            db = dlist.get(0);
        } else if (dlist==null|| dlist.isEmpty()) {
            return null;
        }
        return db;
    }

    @Override
    public String ifExists(InRecordJlsSaveReqVO updateReqVO) {
        String id = "";
        if (inRecordJlsDao.selectById(updateReqVO.getId()) == null) {
            id = createInRecordJls(updateReqVO);
        }
        return id;
    }

    @Override
    public InRecordStatusVO getInRecordStatus(String rybh) {
        InRecordStatusVO db = inRecordJlsDao.getInRecordStatus(rybh);

        return db;
    }


    @Override
    public boolean updateStateInfo(String rybh, String spzt, String currentStep) {
        InRecordJlsDO inRecordJlsDO = getPrisonerInfo(rybh);
        if(inRecordJlsDO!=null){
            // 如果spzt==null 则保持原始值
            if(spzt==null){
                spzt = inRecordJlsDO.getSpzt();
            }
            // 如果currentStep==null 则保持原始值
            if(currentStep==null){
                currentStep = inRecordJlsDO.getCurrentStep();
            }
            inRecordJlsDO.setSpzt(spzt);
            inRecordJlsDO.setCurrentStep(currentStep);
            inRecordJlsDao.updateById(inRecordJlsDO);
        }

        return true;
    }

    @Override
    public boolean updateWristbandInfo(String rybh, String shid, String shbdzt, Date sdbdsj,String status) {
        InRecordJlsDO inRecordJlsDO = getPrisonerInfo(rybh);
        if (inRecordJlsDO != null) {
            // 更新手环信息
            inRecordJlsDO.setShid(shid);
            inRecordJlsDO.setShbdzt(shbdzt);
            inRecordJlsDO.setSdbdsj(sdbdsj);
            inRecordJlsDO.setStatus(status);
            inRecordJlsDao.updateById(inRecordJlsDO);
        }

        return true;
    }

    @Override
    public String getRslxInfo(String rybh) {
        InRecordJlsDO db = getPrisonerInfo(rybh);
        return db.getRslx();
    }

    @Override
    public CollectedPersonDetailVO getCollectedPersonDetail(prisonerInfoReqVO prisonerInfoReqVO) {
        InRecordJlsDO db = getPrisonerInfo(prisonerInfoReqVO.getJgrybm());
        return convertToCollectedPersonDetailVO(db);
    }

    private CollectedPersonDetailVO convertToCollectedPersonDetailVO(InRecordJlsDO entity) {
        CollectedPersonDetailVO vo = new CollectedPersonDetailVO();
        String appCode = HttpUtils.getAppCode();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        vo.setJZRYBH(entity.getJgrybm());
        vo.setRYBH(entity.getJgrybm());
        vo.setAJBH(entity.getAjbh());
        vo.setXM(entity.getXm());
        vo.setXMHYPY("");
        vo.setGMSFHM(entity.getZjhm());
        vo.setCYZJDM("");
        vo.setCYZJ("");
        vo.setZJHM("");
        vo.setXBDM(entity.getXb());
        vo.setXB(DicUtil.translate("ZD_XB", entity.getXb()));
        vo.setCSRQ(DateUtil.format(entity.getCsrq(),DateUtil.DATE_PATTERN));
        vo.setGJDM(entity.getGj());
        vo.setGJ(DicUtil.translate("ZD_GABBZ_GJ", entity.getXb()));
        vo.setMZDM(entity.getMz());
        vo.setMZ(DicUtil.translate("ZD_MZ", entity.getXb()));
        vo.setHJD_XZQHDM(entity.getHjd());
        vo.setHJD_XZQH(DicUtil.translate("ZD_JG", entity.getXb()));
        vo.setHJD_DZMC(entity.getHjdxz());
        vo.setXZD_XZQHDM(entity.getXzz());
        vo.setXZD_XZQH(DicUtil.translate("ZD_JG", entity.getXb()));
        vo.setXZD_DZMC(entity.getXzzxz());
        vo.setCSD_XZQHDM("");
        vo.setCSD_XZQH("");
        vo.setCSD_DZMC("");
        vo.setZZMMDM(entity.getZzmm());
        vo.setZZMM(DicUtil.translate("ZD_ZZMM", entity.getXb()));
        vo.setHYZKDM(entity.getHyzk());
        vo.setHYZK(DicUtil.translate("ZD_HYZK", entity.getXb()));
        vo.setZJXYDM(entity.getZjxy());
        vo.setZJXY(DicUtil.translate("ZD_ZJXY", entity.getXb()));
        vo.setXLDM(entity.getWhcd());
        vo.setXL(DicUtil.translate("ZD_WHCD", entity.getXb()));
        vo.setGRSFDM(entity.getSf());
        vo.setGRSF(DicUtil.translate("ZD_SF", entity.getXb()));
        vo.setTSSFDM(entity.getTssf());
        vo.setTSSF(DicUtil.translate("ZD_TSSF", entity.getXb()));
        vo.setLXDH("");
        vo.setBCJRYLBDM(entity.getGllb());
        vo.setBCJRYLB(DicUtil.translate("ZD_KSS_RYGLLB", entity.getXb()));
        vo.setCJR_XM(sessionUser.getName());
        vo.setCJR_SFHM(sessionUser.getIdCard());
        vo.setCJR_JH(sessionUser.getJobId());
        vo.setCJDW_GAJGJGDM(sessionUser.getOrgCode());
        vo.setCJDW_DWMC(sessionUser.getOrgName());
        vo.setCJSJ(DateUtil.nowNonDelimiterDateTimeStr());

        return vo;
    }
}
