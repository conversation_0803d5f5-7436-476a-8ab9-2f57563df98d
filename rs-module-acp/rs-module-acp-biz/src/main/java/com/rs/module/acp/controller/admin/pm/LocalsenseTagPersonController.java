package com.rs.module.acp.controller.admin.pm;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.pm.dto.RoomDetailDTO;
import com.rs.module.acp.controller.admin.pm.dto.TagTrackDTO;
import com.rs.module.acp.util.QingyanLocalsenseHttpUtil;
import com.rs.module.base.service.pm.AreaService;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import com.rs.module.acp.service.pm.LocalsenseTagPersonService;

@Api(tags = "实战平台-监管管理-定位标签与人员绑定")
@RestController
@RequestMapping("/acp/pm/localsenseTagPerson")
@Validated
public class LocalsenseTagPersonController {

    @Resource
    private LocalsenseTagPersonService localsenseTagPersonService;

    @Resource
    private AreaService areaService;
    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-定位标签与人员绑定")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LocalsenseTagPersonRespVO> getLocalsenseTagPerson(@RequestParam("id") String id) {
        LocalsenseTagPersonDO localsenseTagPerson = localsenseTagPersonService.getLocalsenseTagPerson(id);
        return success(BeanUtils.toBean(localsenseTagPerson, LocalsenseTagPersonRespVO.class));
    }
    @PostMapping("/rsCreate")
    @ApiOperation("入所被监管人员绑定标签(手环)")
    public CommonResult<String> rsCreateLocalsenseTagPerson(@Valid @RequestBody LocalsenseTagPersonRsSaveReqVO reqVO) {
        try{
            return success(localsenseTagPersonService.rsCreateLocalsenseTagPerson(reqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @PostMapping("/znwdCreate")
    @ApiOperation("智能腕带模块绑定被监管人员")
    public CommonResult<String> znwdCreateLocalsenseTagPerson(@Valid @RequestBody LocalsenseTagPersonZnwdSaveReqVO reqVO) {
        try{
            return success(localsenseTagPersonService.znwdCreateLocalsenseTagPerson(reqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @PostMapping("/mjCreate")
    @ApiOperation("民警绑定工牌")
    public CommonResult<String> mjCreateLocalsenseTagPerson(@Valid @RequestBody LocalsenseTagPersonMjSaveReqVO reqVO) {
        try{
            return success(localsenseTagPersonService.mjCreateLocalsenseTagPerson(reqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @GetMapping("/getByPersonId")
    @ApiOperation("根据人员ID查询绑定信息")
    @ApiImplicitParam(name = "bindPersonId", value = "人员ID", required = true)
    public CommonResult<List<LocalsenseTagPersonRespVO>> getByPersonId(@RequestParam String bindPersonIds) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonByBindPersonIds(bindPersonIds);
        return success(BeanUtils.toBean(list, LocalsenseTagPersonRespVO.class));
    }

    @GetMapping("/getByTagId")
    @ApiOperation("根据标签ID查询绑定信息")
    @ApiImplicitParam(name = "tagId", value = "标签ID", required = true)
    public CommonResult<List<LocalsenseTagPersonRespVO>> getByTagId(@RequestParam String tagIds) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonByTagIds(tagIds);
        return success(BeanUtils.toBean(list, LocalsenseTagPersonRespVO.class));
    }

    @PostMapping("/unbind")
    @ApiOperation("解绑标签")
    public CommonResult<String> unbind(@Valid @RequestBody LocalsenseTagPersonUnbindSaveReqVO reqVO) {
        try{
            return success(localsenseTagPersonService.unbind(reqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @GetMapping("/historyByPerson")
    @ApiOperation("查询人员所有历史绑定记录")
    @ApiImplicitParam(name = "bindPersonId", value = "人员ID", required = true)
    public CommonResult<List<LocalsenseTagPersonRespVO>> historyByPerson(@RequestParam String bindPersonId) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonHistoryList(bindPersonId);
        return success(BeanUtils.toBean(list, LocalsenseTagPersonRespVO.class));
    }

    @GetMapping("/historyByTag")
    @ApiOperation("查询标签所有绑定记录")
    @ApiImplicitParam(name = "tagId", value = "标签ID", required = true)
    public CommonResult<List<LocalsenseTagPersonRespVO>> historyByTag(@RequestParam String tagId) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonHistoryListByTagId(tagId);
        return success(BeanUtils.toBean(list, LocalsenseTagPersonRespVO.class));
    }
    //查询手环告警信息
    @GetMapping("/getAlarmByParam")
    @ApiOperation("查询手环告警信息")
    public CommonResult<JSONObject> getAlarmByParam(@RequestParam(required = false) String alarmType,
                                             @RequestParam(required = false) String areaId,
                                             @RequestParam(required = false) String beginTime,
                                             @RequestParam(required = false) String endTime,
                                             @RequestParam(required = false) Integer num,
                                             @RequestParam(required = false) Integer page,
                                             @RequestParam(required = false) String state) {
        try {
            return success(new QingyanLocalsenseHttpUtil().getAlarmByParam(alarmType,areaId,beginTime,endTime,num,page,state));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @GetMapping("/getBatteryByPersonId")
    @ApiOperation("查询标签电量,体征信息")
    public CommonResult<List<JSONObject>> getBatteryByPersonId(@RequestParam String tagIds) {
        try {
            return success(localsenseTagPersonService.getLocalSenseInfo(tagIds));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    @PostMapping("/setHandBandVitalSignPushInterval")
    @ApiOperation("设置手环体征数据推送采样周期")
    public CommonResult<List<JSONObject>> setHandBandVitalSignPushInterval(@Valid @RequestBody LocalsenseHeartRateSaveReqVO saveReqVO) {
        try {
            localsenseTagPersonService.setHandBandVitalSignPushInterval(saveReqVO);
            return success();
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @Resource
    private LocalsenseTagApi localsenseTagApi;
    @GetMapping("/areaInfo")
    @ApiOperation("test-提供监所下的监区和监室信息")
    @ApiImplicitParam(name = "areaCode", value = "code", required = true)
    public CommonResult<List<Tree<String>>> getAreaInfo(@RequestParam("areaCode") String areaCode){
        return localsenseTagApi.getAreaInfo(areaCode);
    }

    /**
     * 2、监所、监区、监室三类手环聚合数据
     * 返回某个监所下手环聚合总数（监所、监区、监室）——通过参数返回在押人员和民警的数据
     */
    @GetMapping("/aggregateData")
    @ApiOperation("test-监所、监区、监室三类手环聚合数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaCode", value = "区域编码", required = true),
            @ApiImplicitParam(name = "personType", value = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    public CommonResult<List<Tree<String>>> getAggregateData(
            @RequestParam("areaCode") String areaCode,
            @RequestParam(value = "personType", required = false) String personType){
        return localsenseTagApi.getAggregateData(areaCode, personType);
    }

    /**
     * 3、提供某个监室下的所有手环定位数据
     * 返回某个监室下的所有手环定位数据，——通过参数返回在押人员和民警的手环或民警卡定位数据
     */
    @GetMapping("/roomPositionData")
    @ApiOperation("test-提供某个监室下的所有手环定位数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaCode", value = "区域编码", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室ID", required = true),
            @ApiImplicitParam(name = "personType", value = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    public CommonResult<List<RoomDetailDTO>> getRoomPositionData(
            @RequestParam("areaCode") String areaCode,
            @RequestParam("roomId") String roomId,
            @RequestParam(value = "personType", required = false) String personType){
        return localsenseTagApi.getRoomPositionData(areaCode,roomId, personType);
    }

    /**
     * 4、提供某个手环对应的详情数据
     * 返回手环电量、心率、体温基础数据及绑定在押人员的基础数据；（民警卡无详情数据）
     */
    @GetMapping("/tagDetail")
    @ApiOperation("test-提供某个手环对应的详情数据")
    @ApiImplicitParam(name = "tagId", value = "手环ID", required = true)
    public CommonResult<RoomDetailDTO> getTagDetail(@RequestParam("tagId") String tagId){
        return localsenseTagApi.getTagDetail(tagId);
    }

    /**
     * 5、提供手环或民警卡的历史轨迹数据
     * 返回手环或民警卡的历史轨迹数据在地图上展示——通过参数返回在押人员和民警的手环或民警卡历史定位数据
     */
    @GetMapping("/historyTrack")
    @ApiOperation("test-提供手环或民警卡的历史轨迹数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tagId", value = "手环或民警卡ID", required = true),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true),
            @ApiImplicitParam(name = "personType", value = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    public CommonResult<List<TagTrackDTO>> getHistoryTrack(
            @RequestParam("tagId") String tagId,
            @RequestParam("startTime") String startTime,
            @RequestParam("endTime") String endTime,
            @RequestParam(value = "personType", required = false) String personType){
        return localsenseTagApi.getHistoryTrack(tagId, startTime, endTime, personType);
    }
}
