package com.rs.module.acp.service.pi;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor.FixedScreenMonitorSaveReqVO;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.acp.util.FixedScreenMonitorUtil;
import com.rs.module.base.vo.RoomVideoVO;
import com.rs.third.api.component.haikang.HaiKangTVWallControlComponent;
import com.rs.third.api.dto.haikang.tvwall.*;
import com.rs.third.api.model.tvwall.TvWallRealPlayReq;
import com.rs.third.api.model.tvwall.TvWallWndsReq;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.pi.FixedScreenMonitorDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.FixedScreenMonitorDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.*;


/**
 * 实战平台-巡视管控-定屏监控 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FixedScreenMonitorServiceImpl extends BaseServiceImpl<FixedScreenMonitorDao, FixedScreenMonitorDO> implements FixedScreenMonitorService {

    @Resource
    private FixedScreenMonitorDao fixedScreenMonitorDao;

    @Resource
    private FixedScreenMonitorPrisonerService fixedScreenMonitorPrisonerService;

    @Resource
    private HaiKangTVWallControlComponent tvWallControlComponent;

    @Resource
    private BaseDeviceCameraService baseDeviceCameraService;
    @Override
    public String createFixedScreenMonitor(FixedScreenMonitorSaveReqVO reqVO) {
        // 插入
        FixedScreenMonitorDO entity = BeanUtils.toBean(reqVO, FixedScreenMonitorDO.class);
        entity.setId(StringUtil.getGuid());
        entity.setStatus(FixedScreenMonitorUtil.STATUS_NOTSTART);
        fixedScreenMonitorDao.insert(entity);

        fixedScreenMonitorPrisonerService.batchSavePrisoner(entity);
        // 返回
        return entity.getId();
    }

    @Override
    public void updateFixedScreenMonitor(FixedScreenMonitorSaveReqVO reqVO) {
        // 校验存在
        validateFixedScreenMonitorExists(reqVO.getId());
        // 更新
        FixedScreenMonitorDO entity = BeanUtils.toBean(reqVO, FixedScreenMonitorDO.class);
        fixedScreenMonitorDao.updateById(entity);
    }

    @Override
    public void deleteFixedScreenMonitor(String id) {
        // 校验存在
        validateFixedScreenMonitorExists(id);
        // 删除
        fixedScreenMonitorDao.deleteById(id);
    }

    private void validateFixedScreenMonitorExists(String id) {
        if (fixedScreenMonitorDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-定屏监控数据不存在");
        }
    }

    @Override
    public FixedScreenMonitorDO getFixedScreenMonitor(String id) {
        return fixedScreenMonitorDao.selectById(id);
    }

    @Override
    public void updateStatusWithTimes(String ids, String status, Date operaTime) {
        List<FixedScreenMonitorDO> list = new ArrayList<>();
        for (String id : ids.split(",")) {
            list.add(fillEntity(id, status, operaTime));
        }
        fixedScreenMonitorDao.updateBatch( list);
    }

    private FixedScreenMonitorDO fillEntity(String id, String status, Date operaTime) {
        FixedScreenMonitorDO entity = fixedScreenMonitorDao.selectById(id);
        entity.setId(id);
        entity.setStatus(status);
        if(operaTime == null) operaTime = new Date();
        if(FixedScreenMonitorUtil.STATUS_ONSCREEN.equals(status)){ //已上墙
            entity.setOnScreenTime(operaTime);
            entity.setOperateTime(operaTime);
            entity.setOperatorXm(SessionUserUtil.getSessionUser().getName());
            entity.setOperatorSfzh(SessionUserUtil.getSessionUser().getIdCard());
        }else{//已下墙
            entity.setOffScreenTime(operaTime);
            entity.setStatus(FixedScreenMonitorUtil.STATUS_OFFSCREEN);
        }
        return entity;
    }
    // 查询指定dlpId status =1所有记录
    @Override
    public List<FixedScreenMonitorDO> getFixedScreenMonitorListByDlpIdStatus(String dlpId, String status) {
        return fixedScreenMonitorDao.selectList(new LambdaQueryWrapper<FixedScreenMonitorDO>()
                .eq(FixedScreenMonitorDO::getDlpId, dlpId)
                .eq(FixedScreenMonitorDO::getIsDel, 0)
                .eq(FixedScreenMonitorDO::getStatus, status));
    }
    //上墙业务逻辑
    /*
    1、查询当前监室对应电视墙信息 dlp_id  需配置目前写死测试
    2、完善视频同步接口 从视频联网平台同步海康gb2816code 字段
    3、根据监室查询视频监控信息 通道ID,gb2816code
    4、从所有监控点资源信息中根据 gb2816code 查询出对应 cameraIndexcode （上墙参数）
    5、获取所有的上墙窗口（跟本地未使用）获取第一个 窗口的 wndUri
    */
    @Override
    public boolean onScreenBus(String roomId,Integer dlpId) throws Exception {
        //Integer dlpId = 3;// 电视墙ID 西一测试
        List<FixedScreenMonitorDO> onScreenList = getFixedScreenMonitorListByDlpIdStatus(dlpId.toString(),FixedScreenMonitorUtil.STATUS_ONSCREEN);

        TvWallWndsReq tvWallWndsReq = new TvWallWndsReq();
        tvWallWndsReq.setDlpId(dlpId);
        HaiKangApiResponse<TvWallWndsDTO> windowList = tvWallControlComponent.getWindowList(tvWallWndsReq);
        String wndUri = FixedScreenMonitorUtil.getAvailableWndUri(windowList,onScreenList);
        List<RoomVideoVO> roomVideoVOList = baseDeviceCameraService.roomVideo(roomId);
        HaiKangApiResponse<CameraPointDTO> allCameras = tvWallControlComponent.getAllCameras("0");
        List<String> cameraIndexcodeList = FixedScreenMonitorUtil.getCameraIndexcode(allCameras,roomVideoVOList);
        TvWallRealPlayReq req = FixedScreenMonitorUtil.fillTvWallRealPlayReq(wndUri,cameraIndexcodeList);
        HaiKangApiResponse<TvWallRealPlayDTO> result = tvWallControlComponent.batchAddToWall(req);
        if(result.isSuccess() && result.getData() != null && CollectionUtil.isNotNull(result.getData().getRealplayList())){
            return true;
        }
        return false;
    }

}
