package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzPageReqVO;
import com.rs.module.acp.entity.pi.SqglMbpzDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情管理-模板配置 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SqglMbpzDao extends IBaseDao<SqglMbpzDO> {


    default PageResult<SqglMbpzDO> selectPage(SqglMbpzPageReqVO reqVO) {
        Page<SqglMbpzDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SqglMbpzDO> wrapper = new LambdaQueryWrapperX<SqglMbpzDO>()
            .eqIfPresent(SqglMbpzDO::getEventSrc, reqVO.getEventSrc())
            .likeIfPresent(SqglMbpzDO::getTemplateName, reqVO.getTemplateName())
            .eqIfPresent(SqglMbpzDO::getEventTypeSettings, reqVO.getEventTypeSettings())
            .eqIfPresent(SqglMbpzDO::getHandlingSituationTemplate, reqVO.getHandlingSituationTemplate())
            .eqIfPresent(SqglMbpzDO::getPushObjectSettings, reqVO.getPushObjectSettings())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SqglMbpzDO::getAddTime);
        }
        Page<SqglMbpzDO> sqglMbpzPage = selectPage(page, wrapper);
        return new PageResult<>(sqglMbpzPage.getRecords(), sqglMbpzPage.getTotal());
    }
    default List<SqglMbpzDO> selectList(SqglMbpzListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SqglMbpzDO>()
            .eqIfPresent(SqglMbpzDO::getEventSrc, reqVO.getEventSrc())
            .likeIfPresent(SqglMbpzDO::getTemplateName, reqVO.getTemplateName())
            .eqIfPresent(SqglMbpzDO::getOrgCode, reqVO.getOrgCode())
        .orderByDesc(SqglMbpzDO::getAddTime));    }


    }
