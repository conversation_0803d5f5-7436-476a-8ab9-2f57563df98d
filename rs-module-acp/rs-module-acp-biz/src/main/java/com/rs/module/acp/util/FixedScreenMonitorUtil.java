package com.rs.module.acp.util;

import com.bsp.common.util.StringUtil;
import com.rs.module.acp.entity.pi.FixedScreenMonitorDO;
import com.rs.module.base.vo.RoomVideoVO;
import com.rs.third.api.dto.haikang.tvwall.CameraPointDTO;
import com.rs.third.api.dto.haikang.tvwall.HaiKangApiResponse;
import com.rs.third.api.dto.haikang.tvwall.TvWallWndsDTO;
import com.rs.third.api.dto.haikang.tvwall.WindowInfoDTO;
import com.rs.third.api.model.tvwall.TvWallRealPlayReq;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class FixedScreenMonitorUtil {
    /**
     * 状态:未上墙
     */
    public final static String STATUS_NOTSTART = "0";
    /**
     * 状态:上墙
     */
    public final static String STATUS_ONSCREEN = "1";
    /**
     * 状态:下墙
     */
    public final static String STATUS_OFFSCREEN = "2";
    public static List<String> getCameraIndexcode(HaiKangApiResponse<CameraPointDTO> cameraPointDTOS, List<RoomVideoVO> roomVideoVOList) throws Exception{
        Set<String> gbCodeSet = roomVideoVOList.stream()
                .map(RoomVideoVO::getGbCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 过滤掉已上墙的窗口
        if (cameraPointDTOS == null || cameraPointDTOS.getData() == null || cameraPointDTOS.getData().getList() == null) {
            throw new RuntimeException("获取所有监控点资源失败");
        }

        // 如果gbCodeSet中存在的则获取cameraPointDTOS.getData().getList()中cameraIndexCode值添加到List<String>返回
        List<String> filteredGbIndexCodes = new ArrayList<>();
        cameraPointDTOS.getData().getList().stream()
                .filter(camera -> StringUtil.isNotEmpty(camera.getCameraIndexCode()) && StringUtil.isNotEmpty(camera.getGhIndexCode()) && gbCodeSet.contains(camera.getGhIndexCode()))
                .map(CameraPointDTO.CameraInfo::getCameraIndexCode)
                .forEach(filteredGbIndexCodes::add);

        return filteredGbIndexCodes;
    }
    public static String getAvailableWndUri(HaiKangApiResponse<TvWallWndsDTO> windowList, List<FixedScreenMonitorDO> onScreenList) throws Exception{

        // 收集已上墙的wndUri
        Set<String> onScreenWndUris = onScreenList.stream()
                .map(FixedScreenMonitorDO::getWndUri)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 过滤掉已上墙的窗口
        if (windowList == null || windowList.getData() == null || windowList.getData().getWndList() == null) {
            throw new RuntimeException("当前电视墙未找到可用的窗口");
        }
        return windowList.getData().getWndList().stream()
                .filter(window -> window.getWndUri() != null)  // 确保wndUri不为空
                .filter(window -> !onScreenWndUris.contains(window.getWndUri()))  // 过滤掉已上墙的
                .findFirst()
                .map(WindowInfoDTO::getWndUri)
                .orElse(null);
    }
    public static TvWallRealPlayReq fillTvWallRealPlayReq(String wndUri ,List<String> cameraIndexcodeList){
        TvWallRealPlayReq req = new TvWallRealPlayReq();
        for (String cameraIndexcode: cameraIndexcodeList) {
            TvWallRealPlayReq.RealPlayItem item = new TvWallRealPlayReq.RealPlayItem();
            item.setCameraIndexcode(cameraIndexcode);
            item.setWndUri(wndUri);
            req.getRealplayList().add(item);
        }
        return req;
    }
}
