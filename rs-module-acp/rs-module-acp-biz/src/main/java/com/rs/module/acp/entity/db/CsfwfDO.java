package com.rs.module.acp.entity.db;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-收押业务-出所防误放 DO
 *
 * <AUTHOR>
 */
@TableName("acp_db_csfwf")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_db_csfwf")
public class CsfwfDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监室ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 业务类型（1：出所、2：入所）
     */
    private String businessType;
    /**
     * 业务原因
     */
    private String businessReason;
    /**
     * 对比方式
     */
    private String verificationMode;
    /**
     * 核验结果（01：待核验，02：验证成功，03：验证失败）
     */
    private String checkResult;
    /**
     * 申请时间
     */
    private Date operateTime;
    /**
     * 经办民警身份证号
     */
    private String operatePoliceSfzh;
    /**
     * 经办民警
     */
    private String operatePolice;
    /**
     * 强制类型（1：强制带出，2：强制带入）
     */
    private String mandatoryType;
    /**
     * 强制原因
     */
    private String mandatoryReason;
    /**
     * 性别
     */
    private String xb;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 被监管人员ID
     */
    private String prisonerId;
    /**
     * 关联业务ID
     */
    private String businessId;

}
