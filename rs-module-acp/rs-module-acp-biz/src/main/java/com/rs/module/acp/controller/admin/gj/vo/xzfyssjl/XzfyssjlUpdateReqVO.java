package com.rs.module.acp.controller.admin.gj.vo.xzfyssjl;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-行政复议讼诉记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class XzfyssjlUpdateReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键不能为空")
    private String id;

    @ApiModelProperty("被监管人编码")
    private String jgrybm;

    @ApiModelProperty("业务类型 字典：ZD_XZFYXZSS")
    private String businessType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("经办人身份证号")
    private String handlePoliceSfzh;

    @ApiModelProperty("经办人")
    private String handlePoliceXm;

    @ApiModelProperty("经办时间")
    private Date handlePoliceTime;

    @ApiModelProperty("转递时间")
    private Date forwardTime;

}
