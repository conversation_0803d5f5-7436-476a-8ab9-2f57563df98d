package com.rs.module.acp.controller.admin.gj.vo.riskIndicator;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险指标 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskIndicatorJhRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("指标类型编码")
    @Trans(type = TransType.DICTIONARY, key = "ZD_FXZB_ZBLX")
    private String indicatorTypeCode;

    @ApiModelProperty("指标集合")
    private List<RiskIndicatorRespVO> indicatorList;
}
