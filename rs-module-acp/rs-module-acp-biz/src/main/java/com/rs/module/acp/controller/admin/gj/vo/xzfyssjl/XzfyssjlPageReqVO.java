package com.rs.module.acp.controller.admin.gj.vo.xzfyssjl;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-行政复议讼诉记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class XzfyssjlPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("被监管人编码")
    private String jgrybm;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("经办人身份证号")
    private String handlePoliceSfzh;

    @ApiModelProperty("经办人")
    private String handlePoliceXm;

    @ApiModelProperty("经办时间")
    private Date[] handlePoliceTime;

    @ApiModelProperty("转递时间")
    private Date[] forwardTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
