package com.rs.module.acp.controller.admin.gj.vo.riskIndicator;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险指标列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskIndicatorListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("指标类型编码")
    private String indicatorTypeCode;

    @ApiModelProperty("指标名称")
    private String indicatorName;

    @ApiModelProperty("指标描述")
    private String indicatorDescription;

    @ApiModelProperty("评估风险等级")
    private String riskLevel;

    @ApiModelProperty("正向/异常")
    private String positiveAnomalous;

    @ApiModelProperty("查询脚本")
    private String queryScript;

    @ApiModelProperty("展示模板")
    private String displayTemplate;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("机构编码")
    private String orgCode;

}
