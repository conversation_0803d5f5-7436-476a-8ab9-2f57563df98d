package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-所情登记推送对象 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_sqdjtsdx")
@KeySequence("acp_pi_sqgl_sqdjtsdx_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_sqdjtsdx")
public class SqglSqdjtsdxDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所情登记ID
     */
    private String sqdjId;
    /**
     * 处置人身份证号
     */
    private String pushUserSfzh;
    /**
     * 推送人名称
     */
    private String pushUserName;
    /**
     * 推送时间
     */
    private Date pushTime;
    /**
     * 推送人所属岗位名称
     */
    private String pushPostName;
    /**
     * 推送岗位编号
     */
    private String pushPostCode;
    /**
     * 所情处置ID
     */
    private String sqczId;

}
