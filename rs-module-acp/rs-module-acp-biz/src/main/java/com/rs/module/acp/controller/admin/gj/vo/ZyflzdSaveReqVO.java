package com.rs.module.acp.controller.admin.gj.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-专业法律指导新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyflzdSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("具体内容")
    @NotEmpty(message = "具体内容不能为空")
    private String jtnr;

    @ApiModelProperty("联系电话")
    private String lxdh;

    @ApiModelProperty("附件")
    private String fj;


}
