package com.rs.module.acp.entity.zh;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 早850监所晨会每日情况 DO
 *
 * <AUTHOR>
 */
@TableName("acp_zh_report_jschmrqk")
@KeySequence("acp_zh_report_jschmrqk_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_zh_report_jschmrqk")
public class ReportJschmrqkDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 汇报日期
     */
    private Date reportDate;
    /**
     * 汇报人
     */
    private String reportUser;
    /**
     * 汇报单位
     */
    private String reportOrg;
    /**
     * 工作落实情况
     */
    private String gzlsqk;
    /**
     * 汇报突出情况
     */
    private String tcqk;
    /**
     * word地址
     */
    private String wordUrl;
    /**
     * pdf地址
     */
    private String pdfUrl;
    /**
     * 生成状态(0否1是)
     */
    private String status;

}
