package com.rs.module.acp.dao.zh;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.zh.ReportJschmrqkDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.rs.module.acp.controller.admin.zh.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 早850监所晨会每日情况 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ReportJschmrqkDao extends IBaseDao<ReportJschmrqkDO> {


    default PageResult<ReportJschmrqkDO> selectPage(ReportJschmrqkPageReqVO reqVO) {
        Page<ReportJschmrqkDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<ReportJschmrqkDO> wrapper = new LambdaQueryWrapperX<ReportJschmrqkDO>()
            .betweenIfPresent(ReportJschmrqkDO::getReportDate, reqVO.getReportDate())
            .eqIfPresent(ReportJschmrqkDO::getReportUser, reqVO.getReportUser())
            .eqIfPresent(ReportJschmrqkDO::getReportOrg, reqVO.getReportOrg())
            .eqIfPresent(ReportJschmrqkDO::getGzlsqk, reqVO.getGzlsqk())
            .eqIfPresent(ReportJschmrqkDO::getTcqk, reqVO.getTcqk())
            .eqIfPresent(ReportJschmrqkDO::getWordUrl, reqVO.getWordUrl())
            .eqIfPresent(ReportJschmrqkDO::getPdfUrl, reqVO.getPdfUrl())
            .eqIfPresent(ReportJschmrqkDO::getStatus, reqVO.getStatus());
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(ReportJschmrqkDO::getAddTime);
        }
        Page<ReportJschmrqkDO> reportJschmrqkPage = selectPage(page, wrapper);
        return new PageResult<>(reportJschmrqkPage.getRecords(), reportJschmrqkPage.getTotal());
    }
    
    default List<ReportJschmrqkDO> selectList(ReportJschmrqkListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ReportJschmrqkDO>()
            .betweenIfPresent(ReportJschmrqkDO::getReportDate, reqVO.getReportDate())
            .eqIfPresent(ReportJschmrqkDO::getReportUser, reqVO.getReportUser())
            .eqIfPresent(ReportJschmrqkDO::getReportOrg, reqVO.getReportOrg())
            .eqIfPresent(ReportJschmrqkDO::getGzlsqk, reqVO.getGzlsqk())
            .eqIfPresent(ReportJschmrqkDO::getTcqk, reqVO.getTcqk())
            .eqIfPresent(ReportJschmrqkDO::getWordUrl, reqVO.getWordUrl())
            .eqIfPresent(ReportJschmrqkDO::getPdfUrl, reqVO.getPdfUrl())
            .eqIfPresent(ReportJschmrqkDO::getStatus, reqVO.getStatus())
        .orderByDesc(ReportJschmrqkDO::getAddTime));
    }

    JSONObject getDataOf24Hours(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);

    JSONObject getDataOfUpToNow(@Param("orgCode") String orgCode);
    
    JSONObject getDataOf24Tx(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    JSONObject getDataOf24Tj(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    JSONObject getDataOf24Lshj(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getSyqk24Rsry(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getSyqk24Csry(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getZyqk24Above65Years(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getZyqk24Jdxj(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getZyqk24Ddgy(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getZyqk24Gzqtry(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getZyqk24Jsyc(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getZyqk24Tsyw(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getZyqk24Tssf(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getWjrygkGjfb(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getWjrygkSxfzlx(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    //------------------------------------服务办案工作情况------------------------------------//
    
    List<Map<String, Object>> getFwbagzqk24Tx(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getFwbagzqk24Tj(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getFwbagzqk24Lsjd(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getFwbagzqk24Jdjs(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getFwbagzqk24Jfzx(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getFwbagzqk24Sghj(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    
    //------------------------------------重点风险人员管控情况------------------------------------//
    
    Map<String, Object> getZdfxry24Sjfx(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getZdfxry24Zyqk(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    
    //------------------------------------其他基础数据------------------------------------//
    
    Integer getJcsj24Xzfy(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    Integer getJcsj24Snzl(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    Integer getJcsj24Dkjg(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getJcsj24Yj(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getJcsj24Ej(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
    
    List<Map<String, Object>> getJcsj24Sj(@Param("orgCode") String orgCode, @Param("reportDate") String reportDate);
}
