package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情登记推送对象新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqdjtsdxSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("推送人身份证号")
    @NotEmpty(message = "推送人身份证号不能为空")
    private String pushUserSfzh;

    @ApiModelProperty("推送人名称")
    @NotEmpty(message = "推送人名称不能为空")
    private String pushUserName;

    @ApiModelProperty("推送人所属岗位名称")
    @NotEmpty(message = "推送人所属岗位名称不能为空")
    private String pushPostName;

    @ApiModelProperty("推送岗位编号")
    @NotEmpty(message = "推送岗位编号不能为空")
    private String pushPostCode;


}
