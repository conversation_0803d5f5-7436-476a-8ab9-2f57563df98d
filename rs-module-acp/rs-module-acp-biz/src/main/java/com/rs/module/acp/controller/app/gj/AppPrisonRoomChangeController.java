package com.rs.module.acp.controller.app.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.AppPrisonRoomChangeRespVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListReqVO;
import com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListVO;
import com.rs.module.acp.entity.gj.PrisonRoomChangeDO;
import com.rs.module.acp.service.gj.prisonroom.PrisonRoomChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管教业务-监室调整-app端")
@RestController
@RequestMapping("/app/acp/gj/prisonRoomChange")
@Validated
public class AppPrisonRoomChangeController {

    @Resource
    private PrisonRoomChangeService prisonRoomChangeService;

    @PostMapping("/create")
    @ApiOperation(value = "管教业务-监室调整-创建")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:create", operateType = LogOperateType.CREATE, title = "管教业务-监室调整-创建",
            success = "管教业务-监室调整-监室调整登记成功", fail = "管教业务-监室调整登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonRoomChange(@Valid @RequestBody PrisonRoomChangeSaveReqVO createReqVO) {
        return success(prisonRoomChangeService.createPrisonRoomChange(createReqVO));
    }

    @PostMapping("/batchCreate")
    @ApiOperation(value = "批量创建-管教业务--监室调整")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:batchCreate", operateType = LogOperateType.CREATE, title = "管教业务-监室调整-创建",
            success = "管教业务-监室调整-监室调整登记成功", fail = "管教业务-监室调整登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVOList}}")
    public CommonResult<List<String>> batchCreatePrisonRoomChange(@Valid @RequestBody List<PrisonRoomChangeSaveReqVO>  createReqVOList) {
        List<String> result = prisonRoomChangeService.batchCreatePrisonRoomChangeList(createReqVOList);
        return success(result);
    }


    @PostMapping("/page")
    @ApiOperation(value = "管教业务-获取监室调整记录")
    public CommonResult<PageResult<PrisonRoomChangeAppListVO>> getPrisonRoomChangeAppList(@Valid @RequestBody PrisonRoomChangeAppListReqVO pageReqVO) {
        PageResult<PrisonRoomChangeAppListVO> pageResult = prisonRoomChangeService.getPrisonRoomChangeAppList(pageReqVO);
        return success(pageResult);
    }



    @PostMapping("/approve")
    @ApiOperation(value = "领导审批-管教业务-监室调整")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:leaderApprove", operateType = LogOperateType.UPDATE, title = "管教业务-监室调整-领导审批",
            success = "管教业务-监室调整-领导审批成功", fail = "管教业务-监室调整-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        prisonRoomChangeService.leaderApprove(approveReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务--监室调整")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:get", operateType = LogOperateType.QUERY, title = "管教业务-管教业务-监室调整获取",
            bizNo = "{{#id}}", success = "管教业务-管教业务-监室调整获取详情成功", fail = "管教业务-管教业务-监室调整获取详情失败", extraInfo = "{{#id}}")
    public CommonResult<AppPrisonRoomChangeRespVO> getPrisonRoomChange(@RequestParam("id") String id) {
        PrisonRoomChangeDO prisonRoomChange = prisonRoomChangeService.getPrisonRoomChange(id);
        AppPrisonRoomChangeRespVO vo = BeanUtils.toBean(prisonRoomChange, AppPrisonRoomChangeRespVO.class);
        return success(vo);
    }



}
