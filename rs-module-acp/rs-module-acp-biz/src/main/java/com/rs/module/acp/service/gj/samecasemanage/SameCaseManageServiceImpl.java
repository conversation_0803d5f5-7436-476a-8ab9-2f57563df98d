package com.rs.module.acp.service.gj.samecasemanage;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.SameCaseManageListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.SameCaseManagePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.SameCaseManageSaveReqVO;
import com.rs.module.acp.service.gj.samecasemanage.bo.SameCaseManageBO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.SameCaseManageDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.SameCaseManageDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-同案人员管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SameCaseManageServiceImpl extends BaseServiceImpl<SameCaseManageDao, SameCaseManageDO> implements SameCaseManageService {

    @Resource
    private SameCaseManageDao sameCaseManageDao;

    @Resource
    private PrisonerService prisonerService;

    @Override
    public String createSameCaseManage(SameCaseManageSaveReqVO createReqVO) {

        if(org.apache.commons.lang3.StringUtils.equals(createReqVO.getJgrybm(), createReqVO.getSameCaseJgrybm())){
            throw new ServerException("不能添加自己为同案人");
        }

        Integer handleTarCount = sameCaseManageDao.selectCount(Wrappers.lambdaQuery(SameCaseManageDO.class)
                .eq(SameCaseManageDO::getJgrybm, createReqVO.getJgrybm())
                .eq(SameCaseManageDO::getSameCaseJgrybm, createReqVO.getSameCaseJgrybm()));
        if(Objects.nonNull(handleTarCount) && handleTarCount > 0){
            throw new ServerException("已经是同案人，不允许重复添加");
        }
        // 判断是否系统同案人
        String tarAjbh = sameCaseManageDao.getSystemTarAjbh(createReqVO.getSameCaseJgrybm());
        if(StringUtils.equals(createReqVO.getAjbh(), tarAjbh)){
            throw new ServerException("已经是同案人，不允许重复添加");
        }

        // 插入
        SameCaseManageDO sameCaseManage = BeanUtils.toBean(createReqVO, SameCaseManageDO.class);
        sameCaseManageDao.insert(sameCaseManage);
        // 返回
        return sameCaseManage.getId();
    }

    @Override
    public void updateSameCaseManage(SameCaseManageSaveReqVO updateReqVO) {
        // 校验存在
        validateSameCaseManageExists(updateReqVO.getId());
        // 更新
        SameCaseManageDO updateObj = BeanUtils.toBean(updateReqVO, SameCaseManageDO.class);
        sameCaseManageDao.updateById(updateObj);
    }

    @Override
    public void deleteSameCaseManage(String id) {
        // 校验存在
        validateSameCaseManageExists(id);
        // 删除
        sameCaseManageDao.deleteById(id);
    }

    private void validateSameCaseManageExists(String id) {
        if (sameCaseManageDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-同案人员管理数据不存在");
        }
    }

    @Override
    public SameCaseManageDO getSameCaseManage(String id) {
        return sameCaseManageDao.selectById(id);
    }

    @Override
    public PageResult<SameCaseManageDO> getSameCaseManagePage(SameCaseManagePageReqVO pageReqVO) {
        return sameCaseManageDao.selectPage(pageReqVO);
    }

    @Override
    public List<SameCaseManageDO> getSameCaseManageList(SameCaseManageListReqVO listReqVO) {
        return sameCaseManageDao.selectList(listReqVO);
    }

    @Override
    public PageResult<SameCaseManageBO> manageListPage(int pageNo, int pageSize, String jgrybm) {
        PrisonerVwRespVO prisonerInVwRespVO = prisonerService.getPrisonerSelectCompomenOne(jgrybm, PrisonerQueryRyztEnum.ZS);
        if (Objects.isNull(prisonerInVwRespVO)) {
            throw new ServerException("该人员不存在");
        }
        Page<SameCaseManageBO> result = sameCaseManageDao.manageListPage(new Page<>(pageNo, pageSize), jgrybm, prisonerInVwRespVO.getAjbh());
        return new PageResult<>(result.getRecords(), result.getTotal());
    }


}
