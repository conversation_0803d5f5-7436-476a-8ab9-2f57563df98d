package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-告警类型配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SqglGjlxpzPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所请来源，字典：")
    private String eventSrc;

    @ApiModelProperty("告警类型，字典：")
    private String alarmType;

    @ApiModelProperty("所情等级，字典：")
    private String eventLevel;

    @ApiModelProperty("处理时效（分钟）")
    private Short processingDuration;

    @ApiModelProperty("排序号")
    private Integer orderId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
