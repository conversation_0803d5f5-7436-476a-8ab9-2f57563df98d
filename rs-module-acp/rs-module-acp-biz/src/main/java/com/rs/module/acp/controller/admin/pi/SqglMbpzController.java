package com.rs.module.acp.controller.admin.pi;

import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzRespVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglMbpzSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglMbpzDO;
import com.rs.module.acp.service.pi.SqglMbpzService;

@Api(tags = "实战平台-巡视管控-所情管理-模板配置")
@RestController
@RequestMapping("/acp/pi/sqglMbpz")
@Validated
public class SqglMbpzController {

    @Resource
    private SqglMbpzService sqglMbpzService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-巡视管控-所情管理-模板配置")
    @LogRecordAnnotation(bizModule = "acp:sqglMbpz:create", operateType = LogOperateType.CREATE, title = "创建实战平台-巡视管控-所情管理-模板配置",
            success = "创建实战平台-巡视管控-所情管理-模板配置成功",
            fail = "创建实战平台-巡视管控-所情管理-模板配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createSqglMbpz(@Valid @RequestBody SqglMbpzSaveReqVO createReqVO) {
        return success(sqglMbpzService.createSqglMbpz(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-巡视管控-所情管理-模板配置")
    @LogRecordAnnotation(bizModule = "acp:sqglMbpz:update", operateType = LogOperateType.CREATE, title = "更新实战平台-巡视管控-所情管理-模板配置",
            success = "更新实战平台-巡视管控-所情管理-模板配置成功",
            fail = "更新实战平台-巡视管控-所情管理-模板配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> updateSqglMbpz(@Valid @RequestBody SqglMbpzSaveReqVO updateReqVO) {
        sqglMbpzService.updateSqglMbpz(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-巡视管控-所情管理-模板配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglMbpz:delete", operateType = LogOperateType.DELETE, title = "删除实战平台-巡视管控-所情管理-模板配置",
            success = "删除实战平台-巡视管控-所情管理-模板配置成功",
            fail = "删除实战平台-巡视管控-所情管理-模板配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteSqglMbpz(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           sqglMbpzService.deleteSqglMbpz(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-巡视管控-所情管理-模板配置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglMbpz:delete", operateType = LogOperateType.QUERY, title = "获得实战平台-巡视管控-所情管理-模板配置",
            success = "获得实战平台-巡视管控-所情管理-模板配置成功",
            fail = "获得实战平台-巡视管控-所情管理-模板配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<SqglMbpzRespVO> getSqglMbpz(@RequestParam("id") String id) {
        return success(sqglMbpzService.getSqglMbpzById(id));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-巡视管控-所情管理-模板配置列表")
    @LogRecordAnnotation(bizModule = "acp:sqglMbpz:list", operateType = LogOperateType.QUERY, title = "获得实战平台-巡视管控-所情管理-模板配置列表",
            success = "获得实战平台-巡视管控-所情管理-模板配置列表成功",
            fail = "获得实战平台-巡视管控-所情管理-模板配置列表失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<SqglMbpzRespVO>> getSqglMbpzList(@Valid @RequestBody SqglMbpzListReqVO listReqVO) {
        return success(sqglMbpzService.getSqglMbpzList(listReqVO));
    }

    @GetMapping("/changeStatus")
    @ApiOperation(value = "启用或禁用所情模板配置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglMbpz:changeStatus", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-启用或禁用所情模板配置",
            success = "实战平台-巡视管控-启用或禁用所情模板配置成功", fail = "实战平台-巡视管控-启用或禁用所情模板配置失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<Boolean> changeStatus(@RequestParam("id")String id) {
        return success(sqglMbpzService.changeStatus(id));
    }
}
