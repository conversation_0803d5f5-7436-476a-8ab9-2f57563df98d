package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxHsjzdReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("罪犯居住地地址")
    private String zfjzddz;

    @ApiModelProperty("执行地的社区矫正机构")
    private String zxddsqjzjg;

    @ApiModelProperty("有关社会组织")
    private String ygshzz;

    @ApiModelProperty("核实居住地调查评估意见（1、通过；2、未通过。）")
    private String hsjzddcpgyj;

    @ApiModelProperty("核实居住地备注")
    private String hsjzdbz;

    @ApiModelProperty("核实居住地材料")
    private String hsjzdcl;


}
