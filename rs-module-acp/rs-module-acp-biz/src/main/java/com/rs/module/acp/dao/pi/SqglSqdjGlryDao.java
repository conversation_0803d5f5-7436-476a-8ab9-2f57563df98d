package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjGlryListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjGlryPageReqVO;
import com.rs.module.acp.entity.pi.SqglSqdjGlryDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情管理-所情登记关联人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SqglSqdjGlryDao extends IBaseDao<SqglSqdjGlryDO> {


    default PageResult<SqglSqdjGlryDO> selectPage(SqglSqdjGlryPageReqVO reqVO) {
        Page<SqglSqdjGlryDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SqglSqdjGlryDO> wrapper = new LambdaQueryWrapperX<SqglSqdjGlryDO>()
            .eqIfPresent(SqglSqdjGlryDO::getSqdjId, reqVO.getSqdjId())
            .eqIfPresent(SqglSqdjGlryDO::getPersonnelType, reqVO.getPersonnelType())
            .eqIfPresent(SqglSqdjGlryDO::getPersonnelId, reqVO.getPersonnelId())
            .likeIfPresent(SqglSqdjGlryDO::getPersonnelName, reqVO.getPersonnelName())
            .eqIfPresent(SqglSqdjGlryDO::getPhotoUrl, reqVO.getPhotoUrl())
            .eqIfPresent(SqglSqdjGlryDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(SqglSqdjGlryDO::getRoomName, reqVO.getRoomName())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SqglSqdjGlryDO::getAddTime);
        }
        Page<SqglSqdjGlryDO> sqglSqdjGlryPage = selectPage(page, wrapper);
        return new PageResult<>(sqglSqdjGlryPage.getRecords(), sqglSqdjGlryPage.getTotal());
    }
    default List<SqglSqdjGlryDO> selectList(SqglSqdjGlryListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SqglSqdjGlryDO>()
            .eqIfPresent(SqglSqdjGlryDO::getSqdjId, reqVO.getSqdjId())
            .eqIfPresent(SqglSqdjGlryDO::getPersonnelType, reqVO.getPersonnelType())
            .eqIfPresent(SqglSqdjGlryDO::getPersonnelId, reqVO.getPersonnelId())
            .likeIfPresent(SqglSqdjGlryDO::getPersonnelName, reqVO.getPersonnelName())
            .eqIfPresent(SqglSqdjGlryDO::getPhotoUrl, reqVO.getPhotoUrl())
            .eqIfPresent(SqglSqdjGlryDO::getRoomId, reqVO.getRoomId())
            .likeIfPresent(SqglSqdjGlryDO::getRoomName, reqVO.getRoomName())
        .orderByDesc(SqglSqdjGlryDO::getAddTime));    }


    }
