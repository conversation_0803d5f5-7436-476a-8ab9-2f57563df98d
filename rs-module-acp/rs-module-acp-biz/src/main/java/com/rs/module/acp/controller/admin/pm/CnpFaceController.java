package com.rs.module.acp.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.CnpFaceDO;
import com.rs.module.acp.service.pm.CnpFaceService;

@Api(tags = "实战平台-监管管理-仓内外屏人脸信息维护")
@RestController
@RequestMapping("/acp/pm/cnpFace")
@Validated
public class CnpFaceController {

    @Resource
    private CnpFaceService cnpFaceService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-仓内外屏人脸信息维护")
    public CommonResult<String> createCnpFace(@Valid @RequestBody CnpFaceSaveReqVO createReqVO) {
        return success(cnpFaceService.createCnpFace(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-仓内外屏人脸信息维护")
    public CommonResult<Boolean> updateCnpFace(@Valid @RequestBody CnpFaceSaveReqVO updateReqVO) {
        cnpFaceService.updateCnpFace(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-仓内外屏人脸信息维护")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCnpFace(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           cnpFaceService.deleteCnpFace(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-仓内外屏人脸信息维护")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CnpFaceRespVO> getCnpFace(@RequestParam("id") String id) {
        CnpFaceDO cnpFace = cnpFaceService.getCnpFace(id);
        return success(BeanUtils.toBean(cnpFace, CnpFaceRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-仓内外屏人脸信息维护分页")
    public CommonResult<PageResult<CnpFaceRespVO>> getCnpFacePage(@Valid @RequestBody CnpFacePageReqVO pageReqVO) {
        PageResult<CnpFaceDO> pageResult = cnpFaceService.getCnpFacePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CnpFaceRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-仓内外屏人脸信息维护列表")
    public CommonResult<List<CnpFaceRespVO>> getCnpFaceList(@Valid @RequestBody CnpFaceListReqVO listReqVO) {
        List<CnpFaceDO> list = cnpFaceService.getCnpFaceList(listReqVO);
        return success(BeanUtils.toBean(list, CnpFaceRespVO.class));
    }
}
