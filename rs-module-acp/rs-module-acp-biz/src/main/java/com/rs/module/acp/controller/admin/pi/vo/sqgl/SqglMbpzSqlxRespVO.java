package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-模板配置所情类型新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglMbpzSqlxRespVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("所情类型")
    private String eventType;

    @ApiModelProperty("排序")
    private Integer orderId;

}
