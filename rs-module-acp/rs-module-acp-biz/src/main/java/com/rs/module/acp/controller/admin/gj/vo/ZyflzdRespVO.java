package com.rs.module.acp.controller.admin.gj.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.vo.FileReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-专业法律指导 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyflzdRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("具体内容")
    private String jtnr;
    @ApiModelProperty("联系电话")
    private String lxdh;
    @ApiModelProperty("附件")
    private String fj;
    @ApiModelProperty("办案单位")
    private String badw;
    @ApiModelProperty("办案民警")
    private String bamj;
    @ApiModelProperty("经办时间")
    private Date jbsj;
    @ApiModelProperty("签收经办民警身份证号")
    private String jbmjsfzh;
    @ApiModelProperty("签收经办民警")
    private String jbmjxm;
    @ApiModelProperty("反馈内容")
    private String fknr;
    @ApiModelProperty("反馈附件")
    private List<FileReqVO> fkfjList;
    @ApiModelProperty("反馈时间")
    private Date fksj;
    @ApiModelProperty("反馈民警身份证号")
    private String fkjbmjsfzh;
    @ApiModelProperty("反馈民警姓名")
    private String fkjbmjxm;
    @ApiModelProperty("状态")
    private String status;
}
