package com.rs.module.acp.controller.admin.gj.vo.riskIndicator;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险指标分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RiskIndicatorPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("指标类型编码")
    private String indicatorTypeCode;

    @ApiModelProperty("指标名称")
    private String indicatorName;

    @ApiModelProperty("指标描述")
    private String indicatorDescription;

    @ApiModelProperty("评估风险等级")
    private String riskLevel;

    @ApiModelProperty("正向/异常")
    private String positiveAnomalous;

    @ApiModelProperty("查询脚本")
    private String queryScript;

    @ApiModelProperty("展示模板")
    private String displayTemplate;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
