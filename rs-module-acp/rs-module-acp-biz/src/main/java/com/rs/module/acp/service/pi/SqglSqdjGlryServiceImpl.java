package com.rs.module.acp.service.pi;

import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjGlryListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjGlryPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjGlrySaveReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglSqdjGlryDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.SqglSqdjGlryDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情管理-所情登记关联人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqglSqdjGlryServiceImpl extends BaseServiceImpl<SqglSqdjGlryDao, SqglSqdjGlryDO> implements SqglSqdjGlryService {

    @Resource
    private SqglSqdjGlryDao sqglSqdjGlryDao;

    @Override
    public String createSqglSqdjGlry(SqglSqdjGlrySaveReqVO createReqVO) {
        // 插入
        SqglSqdjGlryDO sqglSqdjGlry = BeanUtils.toBean(createReqVO, SqglSqdjGlryDO.class);
        sqglSqdjGlryDao.insert(sqglSqdjGlry);
        // 返回
        return sqglSqdjGlry.getId();
    }

    @Override
    public void updateSqglSqdjGlry(SqglSqdjGlrySaveReqVO updateReqVO) {
        // 校验存在
        validateSqglSqdjGlryExists(updateReqVO.getId());
        // 更新
        SqglSqdjGlryDO updateObj = BeanUtils.toBean(updateReqVO, SqglSqdjGlryDO.class);
        sqglSqdjGlryDao.updateById(updateObj);
    }

    @Override
    public void deleteSqglSqdjGlry(String id) {
        // 校验存在
        validateSqglSqdjGlryExists(id);
        // 删除
        sqglSqdjGlryDao.deleteById(id);
    }

    private void validateSqglSqdjGlryExists(String id) {
        if (sqglSqdjGlryDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情管理-所情登记关联人员数据不存在");
        }
    }

    @Override
    public SqglSqdjGlryDO getSqglSqdjGlry(String id) {
        return sqglSqdjGlryDao.selectById(id);
    }

    @Override
    public PageResult<SqglSqdjGlryDO> getSqglSqdjGlryPage(SqglSqdjGlryPageReqVO pageReqVO) {
        return sqglSqdjGlryDao.selectPage(pageReqVO);
    }

    @Override
    public List<SqglSqdjGlryDO> getSqglSqdjGlryList(SqglSqdjGlryListReqVO listReqVO) {
        return sqglSqdjGlryDao.selectList(listReqVO);
    }


}
