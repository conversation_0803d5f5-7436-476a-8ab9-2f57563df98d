package com.rs.module.acp.service.gj;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.cache.RedisEnum;
import com.bsp.common.cache.RedisUtils;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsRespVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.WbInOutSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutRecordsBatchSaveReqVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutStatisticDetailVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutStatisticVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.PrisonerInOutBusinessTypeRespVO;
import com.rs.module.acp.dao.gj.InOutRecordsDao;
import com.rs.module.acp.entity.gj.InOutRecordsDO;
import com.rs.module.acp.service.gj.prisonroom.PrisonRoomChangeService;
import com.rs.module.acp.service.wb.WbBusinessApiService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerInVwRespVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.util.DicUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 实战平台-管教业务-出入登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InOutRecordsServiceImpl extends BaseServiceImpl<InOutRecordsDao, InOutRecordsDO> implements InOutRecordsService {

    @Resource
    private InOutRecordsDao inOutRecordsDao;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    @Lazy
    private PrisonRoomChangeService prisonRoomChangeService;
    @Resource
    @Lazy
    private WbBusinessApiService wbBusinessApiService;
    @Override
    public String createInOutRecords(InOutRecordsSaveReqVO createReqVO) {
        // 插入
        InOutRecordsDO inOutRecords = BeanUtils.toBean(createReqVO, InOutRecordsDO.class);
        inOutRecords.setStatus(status);
        inOutRecordsDao.insert(inOutRecords);
        // 返回
        return inOutRecords.getId();
    }

    @Override
    public void updateInOutRecords(InOutRecordsSaveReqVO updateReqVO) {
        // 校验存在
        //validateInOutRecordsExists(updateReqVO.getId());
        // 更新
        InOutRecordsDO updateObj = BeanUtils.toBean(updateReqVO, InOutRecordsDO.class);
        //inOutRecordsDao.update(updateObj,new LambdaUpdateWrapper<InOutRecordsDO>().eq(InOutRecordsDO::getId, updateReqVO.getId()));
        inOutRecordsDao.updateById(updateObj);
    }

    @Override
    public void deleteInOutRecords(String id) {
        // 校验存在
        validateInOutRecordsExists(id);
        // 删除
        inOutRecordsDao.deleteById(id);
    }

    private void validateInOutRecordsExists(String id) {
        if (inOutRecordsDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-出入登记数据不存在");
        }
    }

    @Override
    public InOutRecordsDO getInOutRecords(String id) {
        return inOutRecordsDao.selectById(id);
    }

    @Override
    public PageResult<InOutRecordsDO> getInOutRecordsPage(InOutRecordsPageReqVO pageReqVO) {
        return inOutRecordsDao.selectPage(pageReqVO);
    }

    @Override
    public List<InOutRecordsDO> getInOutRecordsList(InOutRecordsListReqVO listReqVO) {
        return inOutRecordsDao.selectList(listReqVO);
    }

    /**
     * 带入记录保存
     *
     * @param vo
     * @return
     */
    private String saveInRecords(InOutRecordsSaveReqVO vo) {
        vo.setInoutType(TYPE_IN);
        return createInOutRecords(vo);
    }

    /**
     * 带出记录保存
     *
     * @param vo
     * @return
     */
    private String saveOutRecords(InOutRecordsSaveReqVO vo) {
        vo.setInoutType(TYPE_OUT);
        return createInOutRecords(vo);
    }

    @Override
    public String initRecordsCommon(String jgrybm, String roomId, String businessType, String businessId, String inoutType, String businessSubType) {
        InOutRecordsDO inOutRecords = new InOutRecordsDO();
        inOutRecords.setInoutType(inoutType);
        inOutRecords.setJgrybm(jgrybm);
        inOutRecords.setRoomId(roomId);
        inOutRecords.setBusinessType(businessType);
        inOutRecords.setBusinessId(businessId);
        inOutRecords.setStatus("0");
        inOutRecords.setBusinessSubType(businessSubType);
        inOutRecords.setDataSources("0");
        inOutRecordsDao.insert(inOutRecords);
        return inOutRecords.getId();
    }

    @Override
    public String initRecordsCommon(String jgrybm, String roomId, String businessType, String businessId, String inoutType) {
        InOutRecordsDO inOutRecords = new InOutRecordsDO();
        inOutRecords.setInoutType(inoutType);
        inOutRecords.setJgrybm(jgrybm);
        inOutRecords.setRoomId(roomId);
        inOutRecords.setBusinessType(businessType);
        inOutRecords.setBusinessId(businessId);
        inOutRecords.setStatus("0");
        inOutRecords.setBusinessSubType("");
        inOutRecordsDao.insert(inOutRecords);
        return inOutRecords.getId();
    }

    /**
     * 入监
     */
    private static final String TYPE_IN = "02";
    /**
     * 出监
     */
    private static final String TYPE_OUT = "01";

    /**
     * 业务完成需要 初始化带入记录
     * 供其他模块调用
     *
     * @param jgrybm
     * @param roomId
     * @param businessType
     * @param businessId
     * @return
     */
    @Override
    public String initInRecords(String jgrybm, String roomId, String businessType, String businessId, String businessSubType) {
        return initRecordsCommon(jgrybm, roomId, businessType, businessId, TYPE_IN, businessSubType);
    }

    @Override
    public String initInRecords(String jgrybm, String roomId, String businessType, String businessId) {
        return initRecordsCommon(jgrybm, roomId, businessType, businessId, TYPE_IN, "");
    }

    /**
     * 业务完成需要 初始化带出记录
     * 供其他模块调用
     *
     * @param jgrybm
     * @param roomId
     * @param businessType
     * @param businessId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String initOutRecords(String jgrybm, String roomId, String businessType, String businessId, String businessSubType) {
        return initRecordsCommon(jgrybm, roomId, businessType, businessId, TYPE_OUT, businessSubType);
    }

    @Override
    public String initOutRecords(String jgrybm, String roomId, String businessType, String businessId) {
        return initRecordsCommon(jgrybm, roomId, businessType, businessId, TYPE_OUT, "");
    }

    /**
     * 根据带出记录初始化带入记录
     * 供其他模块调用
     *
     * @param outRecordsDO
     * @return
     */
    @Override
    public String initInRecords(InOutRecordsSaveReqVO outRecordsDO) {
        if (null == outRecordsDO || StringUtil.isEmpty(outRecordsDO.getId()))
            return null;

        if (StringUtil.isEmpty(outRecordsDO.getJgrybm()) || StringUtil.isEmpty(outRecordsDO.getJgryxm()) || StringUtil.isEmpty(outRecordsDO.getRoomId()) || StringUtil.isEmpty(outRecordsDO.getBusinessId())) {
            outRecordsDO = BeanUtils.toBean(getInOutRecords(outRecordsDO.getId()), InOutRecordsSaveReqVO.class);
        }
        validateInOutRecordsExists(outRecordsDO.getId());

        InOutRecordsDO inRecords = new InOutRecordsDO();
        inRecords.setInoutType(TYPE_IN);//设置出入类型为02
        inRecords.setId("");
        inRecords.setOutBusinessId(outRecordsDO.getId());
        inRecords.setDataSources("2");
        inRecords.setStatus("0");
        inRecords.setJgrybm(outRecordsDO.getJgrybm());
        inRecords.setJgryxm(outRecordsDO.getJgryxm());
        inRecords.setRoomId(outRecordsDO.getRoomId());
        inRecords.setBusinessId(outRecordsDO.getBusinessId());
        inRecords.setBusinessType(outRecordsDO.getBusinessType());
        inOutRecordsDao.insert(inRecords);
        return inRecords.getId();
    }

    //带出记录保存
    @Override
    public String saveOutRecordsAndInitInRecords(InOutRecordsSaveReqVO outRecordsDO) {
        outRecordsDO.setStatus(status);
        updateInOutRecords(outRecordsDO);
        return initInRecords(outRecordsDO);
    }

    /**
     * pc 根据出监记录保存入监记录 通用检查结果
     *
     * @param vo       出监记录
     * @param inRoomId 入监房间id
     * @param inTime   入监时间
     * @return
     */
    @Override
    public String acpSaveInOutRecords(InOutRecordsSaveReqVO vo, String inRoomId, Date inTime) {
        vo.setInoutType(TYPE_OUT);
        vo.setDataSources("1");//数据来源实战平台
        if (StringUtil.isEmpty(vo.getInoutReason()))
            vo.setInoutReason(DicUtils.translate("ZD_GJXZDCSY", vo.getBusinessType()));
        List<InOutRecordsDO> list = getListInOutRecordsByJgrybm(vo.getJgrybm(), vo.getBusinessId(), TYPE_OUT, "0");
        if (CollectionUtil.isNotNull(list)) {
            vo.setId(list.get(0).getId());
        }
        if (StringUtil.isNotEmpty(vo.getId())) {
            vo.setStatus(status);//更新审批通过后写入的待带出数据为已带出
            updateInOutRecords(vo);
        } else {
            saveOutRecords(vo);//保存出监记录
        }
        //根据出监记录构建入监记录并保存
        InOutRecordsSaveReqVO inRecords = new InOutRecordsSaveReqVO();
        BeanUtils.copyProperties(vo, inRecords);
        inRecords.setRoomId(inRoomId);
        inRecords.setInoutTime(inTime);
        inRecords.setId("");
        return saveInRecords(inRecords);
    }

    /**
     * 查询单个人的出入记录
     *
     * @param jgrybm     必传被监管人员编号
     * @param businessId 业务id
     * @param inoutType  出入类型
     * @return
     */
    @Override
    public List<InOutRecordsDO> getListInOutRecordsByJgrybm(String jgrybm, String businessId, String inoutType) {
        return getListInOutRecordsByJgrybm(jgrybm, businessId, inoutType, "3");
    }

    public List<InOutRecordsDO> getListInOutRecordsByJgrybm(String jgrybm, String businessId, String inoutType, String status) {
        LambdaQueryWrapper<InOutRecordsDO> wrapper = new LambdaQueryWrapper<InOutRecordsDO>()
                .eq(InOutRecordsDO::getJgrybm, jgrybm)
                .eq(InOutRecordsDO::getStatus, status)
                .eq(InOutRecordsDO::getIsDel, 0).orderByDesc(InOutRecordsDO::getInoutTime);
        if (StringUtil.isNotEmpty(inoutType)) {
            wrapper.eq(InOutRecordsDO::getInoutType, inoutType);
        }
        if (StringUtil.isNotEmpty(businessId)) {
            wrapper.eq(InOutRecordsDO::getBusinessId, businessId);
        }
        wrapper.last("limit 20");
        List<InOutRecordsDO> list = inOutRecordsDao.selectList(wrapper);
        return list;
    }

    //查询人员当天等于addtime 年月日的带出记录事项
    @Override
    public List<InOutRecordsDO> getListInOutRecordsByJgrybm(String jgrybm) {
        LocalDateTime now = LocalDateTime.ofInstant(Instant.ofEpochMilli(new Date().getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = now.with(LocalTime.MIN);
        LocalDateTime endOfDay = now.with(LocalTime.MAX);

        LambdaQueryWrapper<InOutRecordsDO> wrapper = new LambdaQueryWrapper<InOutRecordsDO>()
                .eq(InOutRecordsDO::getJgrybm, jgrybm)
                .eq(InOutRecordsDO::getStatus, "0")
                .eq(InOutRecordsDO::getIsDel, 0)
                .eq(InOutRecordsDO::getInoutType, TYPE_OUT)
                .ge(InOutRecordsDO::getAddTime, startOfDay)
                .le(InOutRecordsDO::getAddTime, endOfDay)
                .orderByDesc(InOutRecordsDO::getInoutTime);
        List<InOutRecordsDO> list = inOutRecordsDao.selectList(wrapper);
        if (CollectionUtil.isNull(list)) {
            InOutRecordsDO other = new InOutRecordsDO();
            other.setBusinessType("09");
            list.add(other);
        }

        return list;
    }

    @Override
    public List<InOutRecordsDO> getListInOutRecordsByRoomId(String roomId, String inoutType) {
        LocalDateTime now = LocalDateTime.ofInstant(Instant.ofEpochMilli(new Date().getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = now.with(LocalTime.MIN);
        LocalDateTime endOfDay = now.with(LocalTime.MAX);
        LambdaQueryWrapper<InOutRecordsDO> wrapper = new LambdaQueryWrapper<InOutRecordsDO>()
                .eq(InOutRecordsDO::getRoomId, roomId)
                .eq(InOutRecordsDO::getStatus, "0")
                .eq(InOutRecordsDO::getIsDel, 0)
                .eq(InOutRecordsDO::getInoutType, inoutType)
                .ge(InOutRecordsDO::getAddTime, startOfDay)
                .le(InOutRecordsDO::getAddTime, endOfDay)
                .orderByDesc(InOutRecordsDO::getInoutTime);
        List<InOutRecordsDO> list = inOutRecordsDao.selectList(wrapper);
        if (CollectionUtil.isNull(list)) {
            InOutRecordsDO other = new InOutRecordsDO();
            other.setBusinessType("09");
            list.add(other);
        }

        return list;
    }

    @Override
    public List<PrisonerInOutBusinessTypeRespVO> getPaddingRoomInOutRecords(String orgCode, String roomId, String inoutType) {
        // 获取人员列表，并做空值保护
        List<PrisonerInDO> personList = prisonerService.getPrisonerInList(orgCode, roomId);
        if (personList == null) {
            personList = Collections.emptyList();
        }

        // 获取进出记录，并做空值保护
        LambdaQueryWrapper<InOutRecordsDO> wrapper = new LambdaQueryWrapper<InOutRecordsDO>()
                .eq(InOutRecordsDO::getRoomId, roomId)
                .eq(InOutRecordsDO::getStatus, "0")
                .eq(InOutRecordsDO::getIsDel, 0).orderByDesc(InOutRecordsDO::getInoutTime);
        List<InOutRecordsDO> listRecord = inOutRecordsDao.selectList(wrapper);
        if (listRecord == null) {
            listRecord = Collections.emptyList();
        }

        // 构建 jgrybm -> List<InOutRecordsDO> 的映射，提升查找效率
        Map<String, List<InOutRecordsDO>> recordMap = new HashMap<>();
        for (InOutRecordsDO record : listRecord) {
            List<InOutRecordsDO> list = recordMap.computeIfAbsent(record.getJgrybm(), k -> new ArrayList<>());
            list.add(record);
            list.sort((r1, r2) -> r2.getAddTime().compareTo(r1.getAddTime())); // 倒序排序
        }

        // 构建返回结果
        List<PrisonerInOutBusinessTypeRespVO> inResult = new ArrayList<>();
        List<PrisonerInOutBusinessTypeRespVO> outRsult = new ArrayList<>();
        //带出列表数据处理 如果人员有带入记录
        for (PrisonerInDO person : personList) {
            PrisonerInOutBusinessTypeRespVO vo = BeanUtils.toBean(person, PrisonerInOutBusinessTypeRespVO.class);

            List<InOutRecordsRespVO> respVOList = new ArrayList<>();
            List<InOutRecordsDO> records = recordMap.getOrDefault(person.getJgrybm(), Collections.emptyList());
            StringBuffer stringBuffer = new StringBuffer();
            for (InOutRecordsDO record : records) {
                // 如果存在业务时间范围 则判定业务时间范围
                if (!inoutType.equals(record.getInoutType())) continue;

                InOutRecordsRespVO respVO = BeanUtils.toBean(record, InOutRecordsRespVO.class);
                respVO.setBusinessTypeName(DicUtils.translate("ZD_GJXZDCSY", record.getBusinessType()));
                respVOList.add(respVO);
                //多个逗号拼接业务类型名称并用逗号分隔

                if (stringBuffer.indexOf(respVO.getBusinessTypeName()) == -1) {
                    if (stringBuffer.length() > 0) {
                        stringBuffer.append(",");
                    }
                    stringBuffer.append(respVO.getBusinessTypeName());
                }

            }
            vo.setBusinessTypeNames(stringBuffer.toString());
            vo.setInOutRecordsRespVOList(respVOList);
            if (vo.getInOutRecordsRespVOList() == null || vo.getInOutRecordsRespVOList().isEmpty()) {
                InOutRecordsRespVO other = new InOutRecordsRespVO();
                other.setBusinessType("09");
                other.setJgrybm(vo.getJgrybm());
                other.setJgryxm(vo.getXm());
                other.setRoomId(roomId);
                vo.getInOutRecordsRespVOList().add(other);
            }
            //排序后判断第一条记录是出监还是入监
            if (records == null || records.isEmpty() || TYPE_OUT.equals(records.get(0).getInoutType())) {
                outRsult.add(vo);
            } else if (TYPE_IN.equals(records.get(0).getInoutType())) {
                inResult.add(vo);
            }
        }
        if (TYPE_OUT.equals(inoutType)) {
            return outRsult;
        }
        return inResult;
    }

    @Override
    public InOutStatisticVO statisticNum(String orgCode, String roomId) {
/*        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.with(LocalTime.MIN);
        LocalDateTime endOfDay = now.with(LocalTime.MAX);*/
        List<InOutStatisticDetailVO> resultMaps = inOutRecordsDao.selectBusinessTypeCount(roomId, null, null);
        // 统计总数
        int outTotalNum = 0;
        Map<String, Set<String>> businessTypeToJgrybmSet = new HashMap<>();
        List<PrisonerInDO> personList = prisonerService.getPrisonerInList(orgCode, roomId);
        Map<String, PrisonerInDO> ryxxMap = new HashMap<>();
        if (personList != null) {
            for (PrisonerInDO person : personList) {
                ryxxMap.put(person.getJgrybm(), person);
            }
        }
        for (InOutStatisticDetailVO statisticDetailVO : resultMaps) {
            String businessType = statisticDetailVO.getBusinessType();
            String jgrybm = statisticDetailVO.getJgrybm();

            if (businessType == null || jgrybm == null || ryxxMap.get(jgrybm) == null) {
                continue; // 可选：跳过空值处理
            }

            businessTypeToJgrybmSet
                    .computeIfAbsent(businessType, k -> new HashSet<>())
                    .add(jgrybm);
        }
        // 构造返回值
        InOutStatisticVO result = new InOutStatisticVO();
        List<JSONObject> list = new ArrayList<>();
        /*String dicKey = RedisUtils.buildKey(RedisEnum.COM_DIC, new String[]{"ZD_GJXZDCSY"});
        Map<String, String> map = RedisClient.hgetAll(dicKey);*/
        Map<String, String> map = DicUtils.getMap("ZD_GJXZDCSY");
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String businessType = entry.getKey();
            String businessTypeName = entry.getValue();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("businessType", businessType);
            jsonObject.put("businessTypeName", businessTypeName);
            Set<String> jgrybmSet = businessTypeToJgrybmSet.getOrDefault(businessType, Collections.emptySet());
            int count = jgrybmSet.size(); // 实际人数统计
            outTotalNum += count;
            jsonObject.put("count", count);
            list.add(jsonObject);
        }

        result.setPersonNum(CollectionUtil.isNull(personList) ? 0 : personList.size());
        result.setOutTotalNum(outTotalNum);
        result.setListBusinessTypeNum(list);
        return result;
    }

    @Override
    public String saveOutRecordsAndInitInRecords(InOutRecordsBatchSaveReqVO batchSaveReqVO) throws Exception{
        if (StringUtil.isEmpty(batchSaveReqVO.getIds())) {
            InOutRecordsSaveReqVO outRecordsDO = BeanUtils.toBean(batchSaveReqVO, InOutRecordsSaveReqVO.class);
            outRecordsDO.setId(StringUtil.getGuid());
            outRecordsDO.setStatus(status);
            outRecordsDO.setBusinessType("09");
            if (StringUtil.isEmpty(outRecordsDO.getDataSources())) outRecordsDO.setDataSources("2");//外屏
            saveOutRecords(outRecordsDO);
            outSyncBusniess(outRecordsDO);

            initInRecords(outRecordsDO);
            return outRecordsDO.getId();
        }
        for (String s : batchSaveReqVO.getIds().split(",")) {
            InOutRecordsDO outRecordsDO = getInOutRecords(s);
            outRecordsDO.setStatus(status);
            if (StringUtil.isEmpty(outRecordsDO.getDataSources())) outRecordsDO.setDataSources("2");//外屏
            InOutRecordsSaveReqVO outRecordsSaveReqVO = BeanUtils.toBean(outRecordsDO, InOutRecordsSaveReqVO.class);
            updateInOutRecords(outRecordsSaveReqVO);
            outSyncBusniess(outRecordsSaveReqVO);
            initInRecords(outRecordsSaveReqVO);
        }
        return batchSaveReqVO.getIds();
    }
    private void outSyncBusniess(InOutRecordsSaveReqVO outRecordsSaveReqVO) throws  Exception{
        if("08".equals(outRecordsSaveReqVO.getBusinessType())){
            prisonRoomChangeService.appOutPerson(outRecordsSaveReqVO);
        }
        if("01".equals(outRecordsSaveReqVO.getBusinessType()) || "02".equals(outRecordsSaveReqVO.getBusinessType())
                || "03".equals(outRecordsSaveReqVO.getBusinessType()) || "04".equals(outRecordsSaveReqVO.getBusinessType()) || "07".equals(outRecordsSaveReqVO.getBusinessType()) ){
            WbInOutSaveReqVO wbInOutSaveReqVO = BeanUtils.toBean(outRecordsSaveReqVO, WbInOutSaveReqVO.class);
            wbInOutSaveReqVO.setId(outRecordsSaveReqVO.getBusinessId());
            wbBusinessApiService.inOutBusinessDrive(wbInOutSaveReqVO, outRecordsSaveReqVO.getBusinessType(), outRecordsSaveReqVO.getInoutType());
        }
    }
    private void inSyncBusniess(InOutRecordsSaveReqVO inRecordsSaveReqVO) throws  Exception{
        if("08".equals(inRecordsSaveReqVO.getBusinessType())){
            prisonRoomChangeService.appInPerson(inRecordsSaveReqVO);
        }
        if("01".equals(inRecordsSaveReqVO.getBusinessType()) || "02".equals(inRecordsSaveReqVO.getBusinessType())
                || "03".equals(inRecordsSaveReqVO.getBusinessType()) || "04".equals(inRecordsSaveReqVO.getBusinessType()) || "07".equals(inRecordsSaveReqVO.getBusinessType()) ){
            WbInOutSaveReqVO wbInOutSaveReqVO = BeanUtils.toBean(inRecordsSaveReqVO, WbInOutSaveReqVO.class);
            wbInOutSaveReqVO.setId(inRecordsSaveReqVO.getBusinessId());
            wbBusinessApiService.inOutBusinessDrive(wbInOutSaveReqVO, inRecordsSaveReqVO.getBusinessType(), inRecordsSaveReqVO.getInoutType());
        }
    }
    String status = "3";

    @Override
    public String saveInRecords(InOutRecordsBatchSaveReqVO batchSaveReqVO) throws Exception{
        if (StringUtil.isEmpty(batchSaveReqVO.getIds())) {
            InOutRecordsSaveReqVO inRecordsDO = BeanUtils.toBean(batchSaveReqVO, InOutRecordsSaveReqVO.class);
            inRecordsDO.setId(StringUtil.getGuid());
            inRecordsDO.setStatus(status);
            if (StringUtil.isEmpty(inRecordsDO.getDataSources())) inRecordsDO.setDataSources("2");//外屏
            saveInRecords(inRecordsDO);

            inSyncBusniess(inRecordsDO);
            return inRecordsDO.getId();
        }
        for (String s : batchSaveReqVO.getIds().split(",")) {
            InOutRecordsSaveReqVO inRecordsDO = BeanUtils.toBean(batchSaveReqVO, InOutRecordsSaveReqVO.class);
            inRecordsDO.setId(s);
            inRecordsDO.setStatus(status);
            if (StringUtil.isEmpty(inRecordsDO.getDataSources())) inRecordsDO.setDataSources("2");//外屏
            updateInOutRecords(inRecordsDO);
            inSyncBusniess(inRecordsDO);
        }
        return batchSaveReqVO.getIds();
    }

    @Override
    public List<PrisonerInVwRespVO> getOutRyJgrybm(String orgCode, String roomId) {
        List<InOutStatisticDetailVO> resultMaps = inOutRecordsDao.selectBusinessTypeCount(roomId, null, null);
        List<PrisonerInDO> personList = prisonerService.getPrisonerInList(orgCode, roomId);
        Map<String, PrisonerInDO> ryxxMap = new HashMap<>();
        if (personList != null) {
            for (PrisonerInDO person : personList) {
                ryxxMap.put(person.getJgrybm(), person);
            }
        }
        Map<String, Set<String>> businessTypeToJgrybmSet = new HashMap<>();
        for (InOutStatisticDetailVO statisticDetailVO : resultMaps) {
            String businessType = statisticDetailVO.getBusinessType();
            String jgrybm = statisticDetailVO.getJgrybm();

            if (businessType == null || jgrybm == null || ryxxMap.get(jgrybm) == null) {
                continue; // 可选：跳过空值处理
            }
            businessTypeToJgrybmSet
                    .computeIfAbsent(businessType, k -> new HashSet<>())
                    .add(jgrybm);
        }
        //String dicKey = RedisUtils.buildKey(RedisEnum.COM_DIC, new String[]{"ZD_GJXZDCSY"});
        //Map<String, String> map = RedisClient.hgetAll(dicKey);
        Map<String, String> map = DicUtils.getMap("ZD_GJXZDCSY");
        Set<String> jgrybmSets = new HashSet<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            Set<String> jgrybmSet = businessTypeToJgrybmSet.getOrDefault(entry.getKey(), Collections.emptySet());
            jgrybmSets.addAll(jgrybmSet);
        }
        List<PrisonerInVwRespVO> list = personList.stream().filter(person -> !jgrybmSets.contains(person.getJgrybm()))
                .map(person -> {
                    PrisonerInVwRespVO respVO = BeanUtils.toBean(person, PrisonerInVwRespVO.class);
                    if (respVO.getCsrq() != null) {
                        //计算年龄
                        try {
                            respVO.setAge(new Date().getYear() - respVO.getCsrq().getYear());
                        } catch (Exception e) {
                            log.error("计算年龄失败", e);
                        }
                    }
                    return respVO;
                })
                .collect(Collectors.toList());
        return list;
    }
}
