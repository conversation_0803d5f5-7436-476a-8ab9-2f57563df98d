package com.rs.module.acp.service.pi;

import javax.validation.*;

import com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor.FixedScreenMonitorSaveReqVO;
import com.rs.module.acp.entity.pi.FixedScreenMonitorDO;
import com.bsp.common.orm.mybatis.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * 实战平台-巡视管控-定屏监控 Service 接口
 *
 * <AUTHOR>
 */
public interface FixedScreenMonitorService extends IBaseService<FixedScreenMonitorDO>{

    /**
     * 创建实战平台-巡视管控-定屏监控
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFixedScreenMonitor(@Valid FixedScreenMonitorSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-定屏监控
     *
     * @param updateReqVO 更新信息
     */
    void updateFixedScreenMonitor(@Valid FixedScreenMonitorSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-定屏监控
     *
     * @param id 编号
     */
    void deleteFixedScreenMonitor(String id);

    /**
     * 获得实战平台-巡视管控-定屏监控
     *
     * @param id 编号
     * @return 实战平台-巡视管控-定屏监控
     */
    FixedScreenMonitorDO getFixedScreenMonitor(String id);

    void updateStatusWithTimes(String id, String status, Date operaTime);

    // 查询指定dlpId status =1所有记录
    List<FixedScreenMonitorDO> getFixedScreenMonitorListByDlpIdStatus(String dlpId, String status);

    //上墙业务逻辑
    /*
    1、查询当前监室对应电视墙信息 dlp_id  需配置目前写死测试
    2、完善视频同步接口 从视频联网平台同步海康gb2816code 字段
    3、根据监室查询视频监控信息 通道ID,gb2816code
    4、从所有监控点资源信息中根据 gb2816code 查询出对应 cameraIndexcode （上墙参数）
    5、获取所有的上墙窗口（跟本地未使用）获取第一个 窗口的 wndUri
    */
    boolean onScreenBus(String roomId,Integer dlpId) throws Exception;
}
