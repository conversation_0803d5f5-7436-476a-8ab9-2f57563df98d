package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-管教业务-法务待办协同 DO
 *
 * <AUTHOR>
 */
@TableName("acp_gj_fwdbxt")
@KeySequence("acp_gj_fwdbxt_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_fwdbxt")
public class FwdbxtDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监管人员身份证号
     */
    private String jgrysfzh;
    /**
     * 监室号
     */
    private String jsh;
    /**
     * 案由代码
     */
    private String aydm;
    /**
     * 案由名称
     */
    private String aymc;
    /**
     * 案件环节
     */
    private String ajhj;
    /**
     * 办案单位
     */
    private String badw;
    /**
     * 办案民警
     */
    private String bamj;
    /**
     * 原始文书
     */
    private String ysws;
    /**
     * 文书名称
     */
    private String wsmc;
    /**
     * 文书数量
     */
    private Integer wssl;
    /**
     * 推送时间
     */
    private Date tssj;
    /**
     * 是否拒签
     */
    private String sfjq;
    /**
     * 签收时间
     */
    private Date qssj;
    /**
     * 拒签日期
     */
    private Date jqrq;
    /**
     * 拒签理由
     */
    private String jqly;
    /**
     * 签收经办民警身份证号
     */
    private String qsjbmjsfzh;
    /**
     * 签收经办民警
     */
    private String qsjbmjxm;
    /**
     * 回传时间
     */
    private Date hcsj;
    /**
     * 回传文书
     */
    private String hcws;
    /**
     * 回传信息
     */
    private String hcxx;
    /**
     * 回传经办民警身份证号
     */
    private String hcjbmjsfzh;
    /**
     * 回传经办民警姓名
     */
    private String hcjbmjxm;
    /**
     * 状态
     */
    private String status;

}
