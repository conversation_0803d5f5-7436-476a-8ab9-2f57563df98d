package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情登记关联人员新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqdjGlrySaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所情登记ID")
    @NotEmpty(message = "所情登记ID不能为空")
    private String sqdjId;

    @ApiModelProperty("人员ID(在押人员传入jgrybm，工作人员传入身份证号，外来人员传入人员ID，报警人员传入身份证号)")
    @NotEmpty(message = "人员ID不能为空")
    private String personnelId;

    @ApiModelProperty("人员姓名")
    @NotEmpty(message = "人员姓名不能为空")
    private String personnelName;

    @ApiModelProperty("照片URL")
    private String photoUrl;

    @ApiModelProperty("监室号（在押人员传入）")
    private String roomId;

    @ApiModelProperty("监室名称（在押人员传入）")
    private String roomName;

}
