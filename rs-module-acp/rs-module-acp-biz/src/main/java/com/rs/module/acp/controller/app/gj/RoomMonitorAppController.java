package com.rs.module.acp.controller.app.gj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.HttpUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.app.gj.vo.RoomMonitorAppListVO;
import com.rs.module.acp.controller.app.gj.vo.RoomMonitorAppVO;
import com.rs.module.acp.service.sldxxfb.SldxxfbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓内外屏-管教业务-监室监控")
@RestController
@RequestMapping("/app/acp/roomMonitor")
@Validated
public class RoomMonitorAppController {
    @Value("${conf.server.ip:127.0.0.1}")
    private String pamServerIp;
    @Resource
    private SldxxfbService sldxxfbService;
    @GetMapping("/index")
    @ApiOperation(value = "仓内外屏-管教业务-监室监控")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "roomId", value = "监室ID", required = true, dataType = "String", paramType = "query"),
    })
    public CommonResult<RoomMonitorAppListVO> index(@RequestParam String orgCode, @RequestParam String roomId,HttpServletRequest request) {
        JSONObject result = sldxxfbService.getRoomMonitorIndex( roomId,orgCode);
        RoomMonitorAppListVO roomMonitorAppListVO = new RoomMonitorAppListVO();
        roomMonitorAppListVO.setPersonTypeList(new ArrayList<>());
        roomMonitorAppListVO.getPersonTypeList().add(new RoomMonitorAppListVO.RoomMonitorAppVOInfo("一级风险人员",
                result.getInteger("rl1") ==null ? 0 : result.getInteger("rl1"),getRoomMonitorAppVOList(result, "rl1_json")));

        roomMonitorAppListVO.getPersonTypeList().add(new RoomMonitorAppListVO.RoomMonitorAppVOInfo("二级风险人员",
                result.getInteger("rl2")==null ? 0 : result.getInteger("rl2"),getRoomMonitorAppVOList(result,"rl2_json")));

        roomMonitorAppListVO.getPersonTypeList().add(new RoomMonitorAppListVO.RoomMonitorAppVOInfo("三级风险人员",
                result.getInteger("rl3") ==null ? 0 : result.getInteger("rl3"),getRoomMonitorAppVOList(result,"rl3_json")));

        roomMonitorAppListVO.getPersonTypeList().add(new RoomMonitorAppListVO.RoomMonitorAppVOInfo("加戴戒具人员",
                result.getInteger("rssj_count") ==null ? 0 : result.getInteger("rssj_count"),getRoomMonitorAppVOList(result,"rssj_json")));

        roomMonitorAppListVO.getPersonTypeList().add(new RoomMonitorAppListVO.RoomMonitorAppVOInfo("新入所人员",
                result.getInteger("equipment_users") == null ? 0 : result.getInteger("equipment_users"),getRoomMonitorAppVOList(result,"equipment_users_json")));

        /*roomMonitorAppListVO.setFxdjOneCount(result.getInteger("rl1"));
        roomMonitorAppListVO.setFxdjOneList(getRoomMonitorAppVOList(result.getJSONArray("rl1_json")));
        roomMonitorAppListVO.setFxdjOneCount(result.getInteger("rl2"));
        roomMonitorAppListVO.setFxdjOneList(getRoomMonitorAppVOList(result.getJSONArray("rl2_json")));
        roomMonitorAppListVO.setFxdjOneCount(result.getInteger("rl3"));
        roomMonitorAppListVO.setFxdjOneList(getRoomMonitorAppVOList(result.getJSONArray("rl3_json")));

        roomMonitorAppListVO.setFxdjOneCount(result.getInteger("rssj_count"));
        roomMonitorAppListVO.setFxdjOneList(getRoomMonitorAppVOList(result.getJSONArray("rssj_json")));
        roomMonitorAppListVO.setFxdjOneCount(result.getInteger("equipment_users"));
        roomMonitorAppListVO.setFxdjOneList(getRoomMonitorAppVOList(result.getJSONArray("equipment_users_json")));*/
        //查询监室值班当前班次，及值班人员

        JSONObject dutyResult = getSignRecord(request,orgCode, roomId);
        JSONObject lifetEvent = getLifeEventByRoomId(request,orgCode, roomId);
        roomMonitorAppListVO.setDutyResult(dutyResult);
        roomMonitorAppListVO.setLifetEvent(lifetEvent);
        return success(roomMonitorAppListVO);
    }
    private JSONArray getSafeJsonArray(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }

        Object value = jsonObject.get(key);
        if(value == null) return null;
        if (value instanceof PGobject) {
            // 处理PGobject类型
            try {
                PGobject pgObject = (PGobject) value;
                String pgValue = pgObject.getValue();
                if (pgValue != null) {
                    // 处理PGobject可能包含的额外引号
                    if (pgValue.startsWith("\"") && pgValue.endsWith("\"") && pgValue.length() > 1) {
                        pgValue = pgValue.substring(1, pgValue.length() - 1);
                        // 处理转义引号
                        pgValue = pgValue.replace("\\\"", "\"");
                    }

                    Object parsed = JSON.parse(pgValue);
                    if (parsed instanceof JSONArray) {
                        return (JSONArray) parsed;
                    }
                }
            } catch (Exception e) {
                return null;
            }
        }
        // 其他情况返回null
        return null;
    }
    private List<RoomMonitorAppVO> getRoomMonitorAppVOList(JSONObject result, String key) {
        JSONArray jsonObjectList = getSafeJsonArray(result, key);
        if (jsonObjectList == null || jsonObjectList.isEmpty()) {
            return Collections.emptyList();
        }
        return jsonObjectList.stream()
                .filter(Objects::nonNull)
                .map(jsonObject -> {
                    JSONObject json = (JSONObject) jsonObject;
                    RoomMonitorAppVO roomMonitorAppVO = new RoomMonitorAppVO();
                    roomMonitorAppVO.setJgrybm(json.containsKey("jgrybm") ? json.getString("jgrybm") : "");
                    roomMonitorAppVO.setJgryxm(json.containsKey("xm") ? json.getString("xm") : "");
                    return roomMonitorAppVO;
                })
                .collect(Collectors.toList());
    }

    private JSONObject getSignRecord(HttpServletRequest request, String orgCode, String roomId){
        Map<String, Object> dutyParam = new HashMap<>();
        dutyParam.put("orgCode", orgCode);
        dutyParam.put("roomId", roomId);
        dutyParam.put("dutyDate", DateUtil.getCurrDate(2));
        dutyParam.put("access_token", request.getParameter("access_token"));
        String serviceUrl = String.format("http://%s:9600/pam/duty/getSignRecord", pamServerIp);
        String resultStr = HttpUtil.get(serviceUrl,dutyParam);
        JSONObject obj = JSON.parseObject(resultStr);
        JSONObject dutyVO = obj.getJSONObject("data");
        // 1. 获取当前时间

        JSONObject result = new JSONObject();
        // 2. 遍历所有班次，找到当前时间对应的班次
        JSONArray shiftArray = dutyVO.getJSONArray("shiftList");
        for (int i = 0; i < shiftArray.size(); i++) {
            JSONObject shift = shiftArray.getJSONObject(i);
            String startTime = shift.getString("startTime");
            String endTime = shift.getString("endTime");

            // 比较当前时间是否在班次时间段内
            if (isTimeBetween(startTime, endTime)) {
                result.put("shiftName",shift.getString("shiftName"));
                result.put("startTime",shift.getString("startTime"));
                result.put("endTime",shift.getString("endTime"));
                StringBuffer prisonerStr = new StringBuffer();
                // 3. 查找该班次对应的值班人员
                JSONArray dutyList = dutyVO.getJSONArray("dutyList");
                for (int j = 0; j < dutyList.size(); j++) {
                    JSONObject duty = dutyList.getJSONObject(j);
                    JSONArray dutyInfoArray = duty.getJSONArray("dutyInfo");
                    for (int k = 0; k < dutyInfoArray.size(); k++) {
                        JSONObject dutyInfo = dutyInfoArray.getJSONObject(k);
                        if (shift.getString("id").equals(dutyInfo.getString("shiftId")) && StringUtil.isNotEmpty(dutyInfo.getString("prisonerName"))) {
                            if(prisonerStr.length() > 0) {
                                prisonerStr.append("|");
                            }
                            prisonerStr.append(dutyInfo.getString("prisonerName"));
                        }
                    }
                }
                result.put("prisonerName",prisonerStr.toString());
            }
        }

        return result;
    }
    private JSONObject getLifeEventByRoomId(HttpServletRequest request, String orgCode, String roomId){
        Map<String, Object> dutyParam = new HashMap<>();
        dutyParam.put("orgCode", orgCode);
        dutyParam.put("roomId", roomId);
        dutyParam.put("access_token", request.getParameter("access_token"));
        String serviceUrl = String.format("http://%s:9600/pam/daily/life/getLifeEventByRoomId", pamServerIp);
        String resultStr = HttpUtil.get(serviceUrl,dutyParam);
        JSONObject obj = JSON.parseObject(resultStr);
        JSONArray lifeEvent = obj.getJSONArray("data");
        JSONObject result = new JSONObject();
        if(lifeEvent != null && lifeEvent.size() > 0) {
            lifeEvent.forEach(lifeEventObj -> {
                JSONObject lifeEventJson = (JSONObject) lifeEventObj;
                //startTime,endTime
                if (isTimeBetween(lifeEventJson.getString("startTime"), lifeEventJson.getString("endTime"))) {
                    result.put("startTime", lifeEventJson.getString("startTime"));
                    result.put("endTime", lifeEventJson.getString("endTime"));
                    result.put("eventName", lifeEventJson.getString("eventName"));
                }
            });
        }

        return result;
    }
    /**
     * 判断当前时间是否在指定时间段内
     * @param startTime 开始时间(HH:mm)
     * @param endTime 结束时间(HH:mm)
     * @return 是否在时间段内
     */
    private boolean isTimeBetween(String startTime, String endTime) {
        Date now = new Date();
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");

        try {
            String currentTime = timeFormat.format(now);
            Date current = timeFormat.parse(currentTime);
            Date start = timeFormat.parse(startTime);
            Date end = timeFormat.parse(endTime);

            if (start.before(end)) {
                return current.after(start) && current.before(end);
            } else {
                // 处理跨天情况
                return current.after(start) || current.before(end);
            }
        } catch (ParseException e) {
            return false;
        }
    }
}
