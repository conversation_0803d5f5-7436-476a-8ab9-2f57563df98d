package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.ZyflzdFeedbackReqVO;
import com.rs.module.acp.controller.admin.gj.vo.ZyflzdSaveReqVO;
import com.rs.module.acp.dao.gj.ZyflzdDao;
import com.rs.module.acp.entity.gj.ZyflzdDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 实战平台-管教业务-专业法律指导 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZyflzdServiceImpl extends BaseServiceImpl<ZyflzdDao, ZyflzdDO> implements ZyflzdService {

    @Resource
    private ZyflzdDao zyflzdDao;

    @Override
    public String createZyflzd(ZyflzdSaveReqVO createReqVO) {
        // 插入
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        ZyflzdDO zyflzd = BeanUtils.toBean(createReqVO, ZyflzdDO.class);
        zyflzd.setBadw(sessionUser.getOrgName());
        zyflzd.setBamj(sessionUser.getName());
        zyflzd.setStatus("01");
        zyflzd.setType("01");
        zyflzdDao.insert(zyflzd);
        // 返回
        return zyflzd.getId();
    }

    @Override
    public void updateZyflzd(ZyflzdSaveReqVO updateReqVO) {
        // 校验存在
        validateZyflzdExists(updateReqVO.getId());
        // 更新
        ZyflzdDO updateObj = BeanUtils.toBean(updateReqVO, ZyflzdDO.class);
        zyflzdDao.updateById(updateObj);
    }

    @Override
    public void deleteZyflzd(String id) {
        // 校验存在
        validateZyflzdExists(id);
        // 删除
        zyflzdDao.deleteById(id);
    }

    private void validateZyflzdExists(String id) {
        if (zyflzdDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-专业法律指导数据不存在");
        }
    }

    @Override
    public ZyflzdDO getZyflzd(String id) {
        return zyflzdDao.selectById(id);
    }

    @Override
    public void feedback(ZyflzdFeedbackReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        ZyflzdDO zyflzd = BeanUtils.toBean(createReqVO, ZyflzdDO.class);
        zyflzd.setFksj(new Date());
        zyflzd.setFkjbmjsfzh(sessionUser.getIdCard());
        zyflzd.setFkjbmjxm(sessionUser.getName());
        zyflzd.setStatus("02");
        zyflzdDao.updateById(zyflzd);
    }


}
