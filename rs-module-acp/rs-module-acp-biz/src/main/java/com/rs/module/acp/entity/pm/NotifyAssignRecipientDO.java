package com.rs.module.acp.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 实战平台-监管管理-通知交办与收件人关联 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 实战平台-监管管理-通知交办与收件人关联新增/修改 Request VO")
@TableName("acp_pm_notify_assign_recipient")
@KeySequence("acp_pm_notify_assign_recipient_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_notify_assign")
public class NotifyAssignRecipientDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 通知交办ID
     */
    private String notifyAssignId;
    /**
     * 收件用户身份证号
     */
    private String idCard;
    /**
     * 收件用户姓名
     */
    private String name;
    /**
     * 信息处理
     */
    private String handleContent;
    /**
     * 是否已读
     */
    private Short isRead;
    /**
     * 已读时间
     */
    private Date readTime;
    /**
     * 是否已处理
     */
    private Short isProc;
    /**
     * 处理时间
     */
    private Date procTime;


}
