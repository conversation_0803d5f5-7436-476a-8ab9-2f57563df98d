package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情处置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqczRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所情登记ID")
    private String sqdjId;
    @ApiModelProperty("处置人身份证号")
    private String handleUserSfzh;
    @ApiModelProperty("处置人名称")
    private String handleUserName;
    @ApiModelProperty("处置时间")
    private Date handleTime;
    @ApiModelProperty("处置人所属岗位名称")
    private String handlePostName;
    @ApiModelProperty("处置岗位编号")
    private String handlePostCode;
    @ApiModelProperty("处置预案")
    private String hanleGenericPlan;
    @ApiModelProperty("处置情况")
    private String handleInfo;
    @ApiModelProperty("处置类型（1：巡控、2：中间环节、3：所领导审批）")
    private String handleType;
    @ApiModelProperty("附件地址")
    private String attUrl;
    @ApiModelProperty("处置状态 0未办结 1已办结 2不通过 3通过")
    private String status;

    @ApiModelProperty("所领导审批情况")
    private List<SqglSqczApproveChildRespVO> approveInfoList;

    @ApiModelProperty("管教岗关联业务")
    private List<SqglSqczGywlRespVO> glywList;

    @ApiModelProperty("当前管教岗可办理的关联业务")
    private List<JSONObject> allowList;
}
