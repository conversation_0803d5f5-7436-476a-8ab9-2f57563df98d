package com.rs.module.acp.service.pi;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglGjlxpzListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglGjlxpzPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglGjlxpzSaveReqVO;
import com.rs.module.acp.entity.pi.SqglGjlxpzDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-巡视管控-所情管理-告警类型配置 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglGjlxpzService extends IBaseService<SqglGjlxpzDO>{

    /**
     * 创建实战平台-巡视管控-所情管理-告警类型配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSqglGjlxpz(@Valid SqglGjlxpzSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情管理-告警类型配置
     *
     * @param updateReqVO 更新信息
     */
    void updateSqglGjlxpz(@Valid SqglGjlxpzSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情管理-告警类型配置
     *
     * @param id 编号
     */
    void deleteSqglGjlxpz(String id);

    /**
     * 获得实战平台-巡视管控-所情管理-告警类型配置
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-告警类型配置
     */
    SqglGjlxpzDO getSqglGjlxpz(String id);

    /**
    * 获得实战平台-巡视管控-所情管理-告警类型配置分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情管理-告警类型配置分页
    */
    PageResult<SqglGjlxpzDO> getSqglGjlxpzPage(SqglGjlxpzPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情管理-告警类型配置列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情管理-告警类型配置列表
    */
    List<SqglGjlxpzDO> getSqglGjlxpzList(SqglGjlxpzListReqVO listReqVO);


}
