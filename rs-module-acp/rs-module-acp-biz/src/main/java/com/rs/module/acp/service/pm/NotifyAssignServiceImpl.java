package com.rs.module.acp.service.pm;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.cons.MsgType;
import com.bsp.sdk.mongodb.MongodbClient;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.sdk.msg.util.MsgUtils;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.mongodb.BasicDBObject;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.NotifyAssignSaveReqVO;
import com.rs.module.acp.dao.pm.NotifyAssignDao;
import com.rs.module.acp.dao.pm.NotifyAssignRecipientDao;
import com.rs.module.acp.entity.pm.NotifyAssignDO;
import com.rs.module.acp.entity.pm.NotifyAssignRecipientDO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 实战平台-监管管理-通知交办 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NotifyAssignServiceImpl extends BaseServiceImpl<NotifyAssignDao, NotifyAssignDO> implements NotifyAssignService {

    @Resource
    private NotifyAssignDao notifyAssignDao;
    @Resource
    private NotifyAssignRecipientDao notifyAssignRecipientDao;
    @Resource
    private UserApi userApi;
    @Resource
    private MongodbClient mongodbClient;
    @Value("${bsp.mongodb.databaseName}")
    private String databaseName;

    @Override
    @Transactional
    public String createNotifyAssign(NotifyAssignSaveReqVO createReqVO) {
        // 插入
        NotifyAssignDO notifyAssign = BeanUtils.toBean(createReqVO, NotifyAssignDO.class);
        notifyAssign.setHandleObjectId(createReqVO.getUserList().stream().map(user ->
                user.getIdCard()).collect(Collectors.joining(",")));
        notifyAssign.setHandleObjectName(createReqVO.getUserList().stream().map(user ->
                user.getName()).collect(Collectors.joining(",")));
        notifyAssign.setId(StringUtil.getGuid32());
        notifyAssignDao.insert(notifyAssign);
        createReqVO.setId(notifyAssign.getId());
        // 插入子表
        List<NotifyAssignRecipientDO> recipientList = BeanUtils.toBean(createReqVO.getUserList(), NotifyAssignRecipientDO.class);
        createRecipientList(notifyAssign.getId(), recipientList);
        // 发送消息
        sendMsg(createReqVO);

        // 返回
        return notifyAssign.getId();
    }

    @Override
    @Transactional
    public void updateNotifyAssign(NotifyAssignSaveReqVO updateReqVO) {
        // 校验存在
        validateNotifyAssignExists(updateReqVO.getId());
        // 更新
        NotifyAssignDO updateObj = BeanUtils.toBean(updateReqVO, NotifyAssignDO.class);
        notifyAssignDao.updateById(updateObj);

        List<NotifyAssignRecipientDO> oldRecipientList = getRecipientByNotifyId(updateObj.getId());
        Set<String> newUser = updateReqVO.getUserList().stream().map(user -> user.getIdCard()).collect(Collectors.toSet());
        Set<String> oldUser = oldRecipientList.stream().map(user -> user.getIdCard()).collect(Collectors.toSet());
        if (newUser.size() != oldUser.size() || !newUser.equals(oldUser)) {
            // 更新子表
            List<NotifyAssignRecipientDO> recipientList = BeanUtils.toBean(updateReqVO.getUserList(), NotifyAssignRecipientDO.class);
            updateRecipientList(updateReqVO.getId(), recipientList);
            // 删除旧的mongo消息
            SendMessageUtil.deleteTodoMsg(updateObj.getId());
            SendMessageUtil.deleteAlertMsg(updateObj.getId());
            // 发送消息
            sendMsg(updateReqVO);
        }
    }

    /**
     * 发送消息
     * @param reqVO
     */
    public void sendMsg(NotifyAssignSaveReqVO reqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        // 获取消息接收用户
        List<ReceiveUser> receiveUserList = new ArrayList<>();
        if (reqVO.getIsFullPolice() == 1) {
            // 根据机构代码查询用户信息
            List<UserRespDTO> userByOrgCode = userApi.getUserByOrgCode(sessionUser.getOrgCode());
            receiveUserList = BeanUtils.toBean(userByOrgCode, ReceiveUser.class);
        } else {
            receiveUserList = BeanUtils.toBean(reqVO.getUserList(), ReceiveUser.class);
        }

        String id = reqVO.getId();
        String url = String.format("/#/discipline/tzjb?bussinId=%s&type=%s", id, reqVO.getPublishType());
        if (MsgType.TODO.getCode().equals(reqVO.getPublishType())) {
            // 发送待办消息
            SendMessageUtil.sendTodoMsg(reqVO.getTitle(), reqVO.getContent(), url, HttpUtils.getAppCode(),
                    sessionUser.getIdCard(), sessionUser.getName(), sessionUser.getOrgCode(), sessionUser.getOrgName(),
                    id, "", "pc", id, receiveUserList);
        } else {
            // 发送通知消息
            SendMessageUtil.sendAlertMsg(reqVO.getTitle(), reqVO.getContent(), url, HttpUtils.getAppCode(),
                    sessionUser.getIdCard(), sessionUser.getName(), sessionUser.getOrgCode(), sessionUser.getOrgName(),
                    id, "", "pc", id, receiveUserList);
        }
    }

    @Override
    public void deleteNotifyAssign(String id) {
        // 删除
        notifyAssignDao.deleteById(id);
        // 删除子表
        notifyAssignRecipientDao.deleteByNotifyAssignId(id);
        // 删除消息
        SendMessageUtil.deleteTodoMsg(id);
        SendMessageUtil.deleteAlertMsg(id);
    }

    private void validateNotifyAssignExists(String id) {
        if (notifyAssignDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-通知交办数据不存在");
        }
    }

    @Override
    public NotifyAssignDO getNotifyAssign(String id) {
        NotifyAssignDO notifyAssignDO = notifyAssignDao.selectById(id);

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 修改mongo消息状态-已读
        ReadMsg(notifyAssignDO.getPublishType(), notifyAssignDO.getId(), sessionUser.getIdCard());
        // 修改实际数据状态-已读
        NotifyAssignRecipientDO recipientDO = getRecipientByNotifyIdAndUser(id, sessionUser.getIdCard());
        if (ObjectUtil.isNotEmpty(recipientDO)) {
            recipientDO.setIsRead((short)1);
            recipientDO.setReadTime(new Date());
            if (MsgType.ALERT.getCode().equals(notifyAssignDO.getPublishType())) {
                recipientDO.setIsProc((short)1);
                recipientDO.setProcTime(recipientDO.getReadTime());
            }
            notifyAssignRecipientDao.updateById(recipientDO);
        }
        return notifyAssignDO;
    }

    @Override
    public void dispose(String id, String content) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        NotifyAssignRecipientDO recipientDO = getRecipientByNotifyIdAndUser(id, sessionUser.getIdCard());
        recipientDO.setHandleContent(content);
        recipientDO.setIsProc((short)1);
        recipientDO.setProcTime(new Date());
        notifyAssignRecipientDao.updateById(recipientDO);
        // 将消息标记为已处理
        ProcMsg(MsgType.TODO.getCode(), recipientDO.getNotifyAssignId(), recipientDO.getIdCard());
    }

    /**
     * 将消息标记为已读
     * @param msgType
     * @param actInstId
     * @param rUser
     */
    private void ReadMsg(String msgType, String actInstId, String rUser) {
        String collectionName = MsgUtils.getCollectionName(msgType);
        BasicDBObject filter = new BasicDBObject();
        filter.put("actInstId", actInstId);
        filter.put("isRead", "0");
        filter.put("rUser", rUser);
        BasicDBObject updateObj = (new BasicDBObject("isRead", "1"))
                .append("rTime", DateUtil.doFormatDate("yyyy-MM-dd HH:mm:ss:SSS", new Date()));

        mongodbClient.update(filter, updateObj, collectionName, databaseName);

    }

    /**
     * 将消息标记为已处理
     * @param msgType
     * @param actInstId
     * @param rUser
     */
    private void ProcMsg(String msgType, String actInstId, String rUser) {
        String collectionName = MsgUtils.getCollectionName(msgType);
        BasicDBObject filter = new BasicDBObject();
        filter.put("actInstId", actInstId);
        filter.put("isProc", "0");
        filter.put("rUser", rUser);
        BasicDBObject updateObj = (new BasicDBObject("isProc", "1"))
                .append("rTime", DateUtil.doFormatDate("yyyy-MM-dd HH:mm:ss:SSS", new Date()));

        mongodbClient.update(filter, updateObj, collectionName, databaseName);

    }


    // ==================== 子表（实战平台-监管管理-通知交办与收件人关联） ====================

    @Override
    public List<NotifyAssignRecipientDO> getRecipientByNotifyId(String notifyAssignId) {
        return notifyAssignRecipientDao.selectListByNotifyAssignId(notifyAssignId);
    }

    private void createRecipientList(String notifyAssignId, List<NotifyAssignRecipientDO> list) {
        list.forEach(o -> {
            o.setId(null);
            o.setNotifyAssignId(notifyAssignId);
            notifyAssignRecipientDao.insert(o);
        });
    }

    private void updateRecipientList(String notifyAssignId, List<NotifyAssignRecipientDO> list) {
        deleteRecipientByNotifyId(notifyAssignId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createRecipientList(notifyAssignId, list);
    }

    private void deleteRecipientByNotifyId(String notifyAssignId) {
        notifyAssignRecipientDao.deleteByNotifyAssignId(notifyAssignId);
    }

    private NotifyAssignRecipientDO getRecipientByNotifyIdAndUser(String notifyAssignId, String idCard) {
        return notifyAssignRecipientDao.selectOne(new LambdaQueryWrapper<NotifyAssignRecipientDO>()
                .eq(NotifyAssignRecipientDO::getNotifyAssignId, notifyAssignId)
                .eq(NotifyAssignRecipientDO::getIdCard, idCard));
    }



}
