package com.rs.module.acp.service.pm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.rs.framework.common.exception.ServerException;
import com.rs.module.acp.controller.admin.pm.vo.DeviceDoorListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.DoorEventVO;
import com.rs.module.acp.dao.pm.DeviceDoorDao;
import com.rs.module.acp.entity.pm.DeviceDoorDO;
import com.rs.third.api.component.haikang.HaiKangDoorControlComponent;
import com.rs.third.api.dto.haikang.DoorControlEventDTO;
import com.rs.third.api.dto.haikang.DoorControlPointDTO;
import com.rs.third.api.dto.haikang.DoorStatusDTO;
import com.rs.third.api.enums.AccessControlEventType;
import com.rs.third.api.enums.DoorControlTypeEnum;
import com.rs.third.api.model.DoorControlEventPageReq;
import com.rs.third.api.model.DoorControlPointPageReq;
import com.rs.third.api.model.PageApiResult;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 实战平台-监管管理-门禁点管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceDoorServiceImpl extends BaseServiceImpl<DeviceDoorDao, DeviceDoorDO> implements DeviceDoorService {

    @Resource
    private DeviceDoorDao deviceDoorDao;
    @Resource
    private HaiKangDoorControlComponent doorControlComponent;

    private static final Cache<String, Object> CACHE = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(60, TimeUnit.SECONDS)
            .build();


    @Override
    public DeviceDoorDO getDeviceDoor(String id) {
        return deviceDoorDao.selectById(id);
    }


    @Override
    public List<DeviceDoorDO> getDeviceDoorList(DeviceDoorListReqVO listReqVO) {
        return null;
    }

    public Map<String, DeviceDoorDO> getDeviceDoorMap(String orgCode) {
        LambdaQueryWrapper<DeviceDoorDO> query = Wrappers.lambdaQuery(DeviceDoorDO.class);
        query.select(DeviceDoorDO::getId, DeviceDoorDO::getIndexCode)
                .eq(DeviceDoorDO::getOrgCode, orgCode);
        List<DeviceDoorDO> deviceDoorDOS = deviceDoorDao.selectList(query);
        if (CollUtil.isNotEmpty(deviceDoorDOS)) {
            Map<String, DeviceDoorDO> doorDOMap = deviceDoorDOS.stream()
                    .collect(Collectors.toMap(DeviceDoorDO::getIndexCode, Function.identity()));
            return doorDOMap;
        }
        return new HashMap<>();
    }

    @Override
    public void syncDeviceDoor() {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        final int PAGE_SIZE = 200;
        List<DeviceDoorDO> deviceDoorList = new ArrayList<>();
        // 分页控制参数
        DoorControlPointPageReq pageReq = new DoorControlPointPageReq();
        pageReq.setPageSize(PAGE_SIZE);
        pageReq.setOrderBy("name");
        pageReq.setOrderType("desc");
        String region = doorControlComponent.getProperties().getRegion();
        List<String> list = new ArrayList<>();
        log.info("开始同步门禁数据，区域：{}, 组织代码：{}, 用户组织代码：{}",
                region, doorControlComponent.getProperties().getOrgCode(), sessionUser == null ? null : sessionUser.getOrgCode());
        if (sessionUser != null && StrUtil.equals(sessionUser.getOrgCode(), doorControlComponent.getProperties().getOrgCode())) {
            list.add(region);
        } else {
            list.add("");
        }
        pageReq.setRegionIndexCodes(list);
        pageReq.setIsSubRegion(true);
        int currentPage = 1;
        int totalPages = 1;
        boolean isFirstPage = true;
        while (currentPage <= totalPages) {
            try {
                pageReq.setPageNo(currentPage);
                JSONObject jsonObject = doorControlComponent.queryDoorControlPoints(pageReq);
                if (jsonObject == null || !"0".equals(jsonObject.getString("code"))) {
                    log.error("第 {} 页数据获取失败: {}", currentPage, jsonObject == null ? "" : jsonObject.get("msg"));
                    throw new ServerException("门禁数据获取失败:" + (jsonObject == null ? "" : jsonObject.get("msg")));
                }
                JSONObject data = jsonObject.getJSONObject("data");
                // 首頁获取总记录数
                if (isFirstPage) {
                    int totalCount = data.getInteger("total");
                    totalPages = (totalCount + PAGE_SIZE - 1) / PAGE_SIZE;
                    isFirstPage = false;
                    if (totalCount == 0) {
                        break;
                    }
                }
                // 数据转换逻辑
                JSONArray jsonArray = data.getJSONArray("list");
                List<DoorControlPointDTO> dtos = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    // 将 JSONArray 中的每个元素转换为 DoorControlPointDTO 对象
                    DoorControlPointDTO dto = jsonArray.getJSONObject(i).toJavaObject(DoorControlPointDTO.class);
                    dtos.add(dto);
                }
                dtos.stream().map(dto -> {
                            DeviceDoorDO deviceDoor = new DeviceDoorDO();
                            BeanUtils.copyProperties(dto, deviceDoor);
                            deviceDoor.setOriginRegionName(dto.getRegionName());
                            deviceDoor.setOriginRegionPathName(dto.getRegionPathName());
                            if (dto.getDoorSerial() != null) {
                                deviceDoor.setDoorSerial(dto.getDoorSerial().toString());
                            }
                            deviceDoor.setId(dto.getIndexCode());
                            return deviceDoor;
                        })
                        .forEach(deviceDoorList::add);
                log.info("已加载第 {}/{} 页数据", currentPage, totalPages);
                currentPage++;
            } catch (Exception e) {
                log.error("第 {} 页数据处理异常", currentPage, e);
                break;
            }
        }
        Map<String, DeviceDoorDO> deviceDoorMap = getDeviceDoorMap(sessionUser.getOrgCode());
        // 分批次保存（每500条提交一次）
        if (CollUtil.isNotEmpty(deviceDoorList)) {
            int batchSize = 500;
            List<List<DeviceDoorDO>> partitions = Lists.partition(deviceDoorList, batchSize);
            for (List<DeviceDoorDO> partition : partitions) {
                List<DeviceDoorDO> saveList = new ArrayList<>();
                List<DeviceDoorDO> updateList = new ArrayList<>();
                for (DeviceDoorDO deviceDoor : partition) {
                    DeviceDoorDO deviceDoorDO = deviceDoorMap.get(deviceDoor.getIndexCode());
                    if (deviceDoorDO == null) {
                        saveList.add(deviceDoor);
                    } else {
                        deviceDoor.setId(deviceDoorDO.getId());
                        updateList.add(deviceDoor);
                    }
                }
                if (CollUtil.isNotEmpty(saveList)) {
                    deviceDoorDao.insertBatch(saveList);
                }
                if (CollUtil.isNotEmpty(updateList)) {
                    deviceDoorDao.updateBatch(updateList);
                }
            }
            log.info("门禁数据同步完成，总计 {} 条", deviceDoorList.size());
        }
        ThreadUtil.sleep(1000);
        // 异步更新门禁状态
        ThreadUtil.execute(() -> {
            List<String> indexCodes = deviceDoorList.stream().distinct().map(DeviceDoorDO::getIndexCode).collect(Collectors.toList());
            List<List<String>> partitions = Lists.partition(indexCodes, 200);
            List<DeviceDoorDO> updateList = new ArrayList<>(0);
            for (List<String> partition : partitions) {
                List<DoorStatusDTO> doorStatusDTOS = doorControlComponent.doorStatusQuery(partition);
                if (CollUtil.isNotEmpty(doorStatusDTOS)) {
                    for (DoorStatusDTO doorStatusDTO : doorStatusDTOS) {
                        DeviceDoorDO deviceDoorDO = new DeviceDoorDO();
                        deviceDoorDO.setId(doorStatusDTO.getDoorIndexCode());
                        deviceDoorDO.setIndexCode(doorStatusDTO.getDoorIndexCode());
                        if (doorStatusDTO.getDoorState() != null) {
                            deviceDoorDO.setDoorStatus(doorStatusDTO.getDoorState().toString());
                        }
                        updateList.add(deviceDoorDO);
                    }
                }
            }
            if (CollUtil.isNotEmpty(updateList)) {
                saveOrUpdateBatch(updateList);
                log.info("门禁状态同步完成，总计 {} 条", updateList.size());
            }
        });
    }

    @Override
    public void bindRoom(String id, String roomId, String roomName, String bindRoomStatus) {
        // 查询门禁点是否存在
        DeviceDoorDO deviceDoorDO = deviceDoorDao.selectById(id);
        if (deviceDoorDO == null) {
            throw new IllegalArgumentException("门禁点不存在");
        }
        DeviceDoorDO update = new DeviceDoorDO();
        update.setId(id);
        if ("1".equals(bindRoomStatus)) {
            // 查询该监室号是否已被绑定
            DeviceDoorDO boundDeviceDoor = deviceDoorDao.selectOne(DeviceDoorDO::getRoomId, roomId);
            if (boundDeviceDoor != null && !StrUtil.equals(deviceDoorDO.getId(), boundDeviceDoor.getId())) {
                throw new ServerException("该监室号已被其他门禁点绑定");
            }
            // 更新门禁点信息
            update.setRoomId(roomId);
            update.setRoomName(roomName);
            update.setBindRoomStatus("1"); // 绑定状态设置为已绑定
        } else {
            update.setRoomId("");
            update.setRoomName("");
            update.setBindRoomStatus("0");
        }
        deviceDoorDao.updateById(update);
    }

    @Override
    public JSONObject getDeviceDoorStatus(String doorIndexCode) {
        JSONObject result = new JSONObject();
        Object ifPresent = CACHE.getIfPresent(doorIndexCode);
        if (ifPresent == null) {
            System.out.println("执行查询");
            List<DoorStatusDTO> doorStatusDTOS = doorControlComponent.doorStatusQuery(Lists.newArrayList(doorIndexCode));
            if (CollUtil.isNotEmpty(doorStatusDTOS)) {
                DoorStatusDTO doorStatusDTO = doorStatusDTOS.get(0);
                result.put("doorState", doorStatusDTO.getDoorState());
                CACHE.put(doorIndexCode, doorStatusDTO.getDoorState());
            }
        } else {
            result.put("doorState", ifPresent);
        }
        result.put("doorIndexCode", doorIndexCode);
        return result;
    }

    @Override
    public JSONObject controlDoor(String doorIndexCode, DoorControlTypeEnum controlType) {
        DeviceDoorDO deviceDoorDO = deviceDoorDao.selectOne(DeviceDoorDO::getIndexCode, doorIndexCode);
        if (deviceDoorDO == null) {
            throw new IllegalArgumentException("门禁点不存在");
        }
        return doorControlComponent.controlDoor(deviceDoorDO.getIndexCode(), controlType);
    }

    @Override
    public JSONObject controlRoomDoor(String roomId, DoorControlTypeEnum controlType) {
        DeviceDoorDO deviceDoorDO = deviceDoorDao.selectOne(DeviceDoorDO::getRoomId, roomId);
        if (deviceDoorDO == null || !"1".equals(deviceDoorDO.getBindRoomStatus())) {
            throw new IllegalArgumentException("该监室未绑定门禁点");
        }
        JSONObject jsonObject = doorControlComponent.controlDoor(deviceDoorDO.getIndexCode(), controlType);
        // 更新门禁点状态
        ThreadUtil.execute(() -> {
            updateDoorStatus(Lists.newArrayList(deviceDoorDO.getIndexCode()));
//             TODO 添加操作记录
        });
        return jsonObject;
    }

    @Override
    public PageApiResult<DoorControlEventDTO> getDoorControlEventList(DoorControlEventPageReq pageReq) {
        PageApiResult<DoorControlEventDTO> doorControlEventDTOPageApiResult = doorControlComponent.queryDoorEvents(pageReq);
        return doorControlEventDTOPageApiResult;
    }

    @Override
    public DoorControlEventDTO getLastDoorControlEvent(String doorIndexCode, AccessControlEventType eventType) {
        DoorControlEventPageReq doorControlEventPageReq = new DoorControlEventPageReq();
        doorControlEventPageReq.setDoorIndexCodes(new ArrayList<String>() {{
            add(doorIndexCode);
        }});
        doorControlEventPageReq.setOrder("desc");
        doorControlEventPageReq.setSort("eventTime");
        doorControlEventPageReq.setEventTypes(new ArrayList<Integer>() {{
            add(eventType.getCode());
        }});
        doorControlEventPageReq.setPageNo(1);
        doorControlEventPageReq.setPageSize(1);
        PageApiResult<DoorControlEventDTO> queryDoorEvents = doorControlComponent.queryDoorEvents(doorControlEventPageReq);
        if (queryDoorEvents.getList() != null && queryDoorEvents.getList().size() > 0) {
            return queryDoorEvents.getList().get(0);
        }
        return null;
    }

    @Override
    public List<DoorEventVO> getLastDoorCloseControlEvent(String roomId, int num) {
        DeviceDoorDO deviceDoorDO = deviceDoorDao.selectOne(DeviceDoorDO::getRoomId, roomId);
        if (deviceDoorDO == null) {
            throw new ServerException("该监室未绑定门禁点");
        }
        DoorControlEventPageReq doorControlEventPageReq = new DoorControlEventPageReq();
        doorControlEventPageReq.setDoorIndexCodes(new ArrayList<String>() {{
            add(deviceDoorDO.getIndexCode());
        }});
        doorControlEventPageReq.setOrder("desc");
        doorControlEventPageReq.setSort("eventTime");
        doorControlEventPageReq.setEventTypes(new ArrayList<Integer>() {{
            add(AccessControlEventType.NORMAL_CLOSE.getCode());
        }});
        doorControlEventPageReq.setPageNo(1);
        doorControlEventPageReq.setPageSize(num);
        PageApiResult<DoorControlEventDTO> queryDoorEvents = doorControlComponent.queryDoorEvents(doorControlEventPageReq);
        List<DoorControlEventDTO> list = queryDoorEvents.getList();
        if (list != null && list.size() > 0) {
            List<DoorEventVO> doorEventVOS = getDoorEventVOS(list, deviceDoorDO);
            return doorEventVOS;
        }
        return new ArrayList<>();
    }

    @NotNull
    private static List<DoorEventVO> getDoorEventVOS(List<DoorControlEventDTO> list, DeviceDoorDO deviceDoorDO) {
        List<DoorEventVO> doorEventVOS = new ArrayList<>();
        for (DoorControlEventDTO doorControlEventDTO : list) {
            if (AccessControlEventType.NORMAL_CLOSE.getCode() == doorControlEventDTO.getEventType()) {
                DoorEventVO doorEventVO = new DoorEventVO();
                doorEventVO.setRoomId(deviceDoorDO.getRoomId());
                doorEventVO.setEventTime(doorControlEventDTO.getEventTime());
                doorEventVOS.add(doorEventVO);
            }
        }
        return doorEventVOS;
    }

    private void updateDoorStatus(List<String> doorIndexCodes) {
        if (CollUtil.isEmpty(doorIndexCodes)) {
            return;
        }
        List<DeviceDoorDO> list = new ArrayList<>(doorIndexCodes.size());
        List<DeviceDoorDO> deviceDoorDOS = deviceDoorDao.selectList(DeviceDoorDO::getIndexCode, doorIndexCodes);
        List<DoorStatusDTO> doorStatusDTOS = doorControlComponent.doorStatusQuery(doorIndexCodes);
        Map<String, Integer> doorStatusMap = new HashMap<>();
        if (CollUtil.isEmpty(doorStatusDTOS)) {
            for (DoorStatusDTO doorStatusDTO : doorStatusDTOS) {
                doorStatusMap.put(doorStatusDTO.getDoorIndexCode(), doorStatusDTO.getDoorState());
            }
        }
        for (DeviceDoorDO deviceDoorDO : deviceDoorDOS) {
            Integer status = doorStatusMap.get(deviceDoorDO.getIndexCode());
            if (status != null) {
                DeviceDoorDO update = new DeviceDoorDO();
                update.setId(deviceDoorDO.getId());
                update.setDoorStatus(status.toString());
                list.add(update);
            }
        }
        if (CollUtil.isNotEmpty(list)) {
            deviceDoorDao.updateBatch(list);
        }
    }
}
