package com.rs.module.acp.controller.app.gj;

import cn.hutool.core.lang.Assert;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlRespVO;
import com.rs.module.acp.entity.gj.XzfyssjlDO;
import com.rs.module.acp.service.gj.xzfyssjl.XzfyssjlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-行政复议讼诉记录")
@RestController
@RequestMapping("/app/acp/gj/xzfyssjl")
@Validated
public class AppXzfyssjlController {

    @Resource
    private XzfyssjlService xzfyssjlService;

    @GetMapping("/page")
    @ApiOperation(value = "App-行政复议讼诉记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
            @ApiImplicitParam(name = "type", value = "寄信周期类型 1 全部，2 今天，3 昨天，4 近一周")
    })
    public CommonResult<PageResult<XzfyssjlRespVO>> getAppXzfyssjlPage(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                       @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                                       @RequestParam(value = "jgrybm") String jgrybm,
                                                                       @RequestParam(value = "type", defaultValue = "1") String type) {
        Assert.notEmpty(jgrybm, "jgrybm不能为空");
        PageResult<XzfyssjlDO> pageResult = xzfyssjlService.getAppXzfyssjlPage(pageNo, pageSize, jgrybm, type);
        return success(BeanUtils.toBean(pageResult, XzfyssjlRespVO.class));
    }

}
