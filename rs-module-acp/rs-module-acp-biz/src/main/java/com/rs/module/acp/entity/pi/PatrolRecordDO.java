package com.rs.module.acp.entity.pi;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-巡视登记 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_patrol_record")
@KeySequence("acp_pi_patrol_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_patrol_record")
public class PatrolRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;

    private String roomName;
    /**
     * 巡控室ID
     */
    private String patrolRoomId;
    /**
     * 巡控室名称
     */
    private String patrolRoomName;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 登记类型 01 自动,02 手动
     */
    private String recordType;
    /**
     * 巡控人员身份证号
     */
    private String patrolStaffSfzh;
    /**
     * 巡控人员
     */
    private String patrolStaff;
    /**
     * 登记内容
     */
    private String recordContent;
    /**
     * 是否岗位协同：1-是/0-否
     */
    private Short isPostCoordination;
    /**
     * 岗位协同人员，多选项 ZD_XSGKXTGW
     */
    private String coordinationPosts;
    /**
     * 岗位协同人员名称
     */
    private String coordinationPostsName;
    /**
     * 待办来源 ZD_XSGKDBLY
     */
    private String todoSource;
    /**
     * 待办事由
     */
    private String todoReason;
    /**
     * 待办推送人身份证号
     */
    private String todoPersonSfzh;
    /**
     * 待办推送人
     */
    private String todoPerson;
    /**
     * 待办推送岗位
     */
    private String todoPost;
    /**
     * 待办推送时间
     */
    private Date todoPushTime;
    /**
     * 状态 01 未处理 03 已处理 ZD_XSGKYWLX
     */
    private String status;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 登记经办时间
     */
    private Date operatorTime;
    /**
     * 岗位推送对象证件号码
     */
    private String pushTargetIdCard;
    /**
     * 岗位推送对象
     */
    private String pushTarget;
    /**
     * 岗位推送内容
     */
    private String pushContent;
    /**
     * 是否异常 0 正常,1 异常
     */
    private String isAbnormal;
}
