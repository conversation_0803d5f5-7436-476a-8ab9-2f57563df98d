package com.rs.module.acp.controller.admin.gj;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.CwxxgzRespVO;
import com.rs.module.acp.controller.admin.gj.vo.CwxxgzSaveReqVO;
import com.rs.module.acp.entity.gj.CwxxgzDO;
import com.rs.module.acp.service.gj.CwxxgzService;
import com.rs.module.base.vo.FileReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管教业务-错误信息更正")
@RestController
@RequestMapping("/acp/gj/cwxxgz")
@Validated
public class CwxxgzController {

    @Resource
    private CwxxgzService cwxxgzService;

    @PostMapping("/create")
    @ApiOperation(value = "创建错误信息更正")
    public CommonResult<String> createCwxxgz(@Valid @RequestBody CwxxgzSaveReqVO createReqVO) {
        return success(cwxxgzService.createCwxxgz(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新错误信息更正")
    public CommonResult<Boolean> updateCwxxgz(@Valid @RequestBody CwxxgzSaveReqVO updateReqVO) {
        cwxxgzService.updateCwxxgz(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除错误信息更正")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCwxxgz(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           cwxxgzService.deleteCwxxgz(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得错误信息更正")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CwxxgzRespVO> getCwxxgz(@RequestParam("id") String id) {
        CwxxgzDO cwxxgz = cwxxgzService.getCwxxgz(id);
        CwxxgzRespVO respVO = BeanUtils.toBean(cwxxgz, CwxxgzRespVO.class);
        respVO.setAttUrlList(JSONObject.parseArray(cwxxgz.getAttUrl(), FileReqVO.class));
        return success(respVO);
    }

    @GetMapping("/push")
    @ApiOperation(value = "重推")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<Boolean> push(@RequestParam("id") String id) {
        cwxxgzService.update(new LambdaUpdateWrapper<CwxxgzDO>()
                .eq(CwxxgzDO::getId, id)
                .set(CwxxgzDO::getStatus, "02"));
        return success(true);
    }

    @GetMapping("/correct")
    @ApiOperation(value = "更正")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<Boolean> correct(@RequestParam("id") String id) {
        cwxxgzService.update(new LambdaUpdateWrapper<CwxxgzDO>()
                .eq(CwxxgzDO::getId, id)
                .set(CwxxgzDO::getStatus, "04"));
        return success(true);
    }

}
