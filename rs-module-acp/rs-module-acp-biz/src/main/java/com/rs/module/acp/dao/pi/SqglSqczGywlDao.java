package com.rs.module.acp.dao.pi;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczGywlListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqczGywlPageReqVO;
import com.rs.module.acp.entity.pi.SqglSqczGywlDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-巡视管控-所情管理-所情处置关联业务 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SqglSqczGywlDao extends IBaseDao<SqglSqczGywlDO> {


    default PageResult<SqglSqczGywlDO> selectPage(SqglSqczGywlPageReqVO reqVO) {
        Page<SqglSqczGywlDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SqglSqczGywlDO> wrapper = new LambdaQueryWrapperX<SqglSqczGywlDO>()
            .eqIfPresent(SqglSqczGywlDO::getSqczId, reqVO.getSqczId())
            .eqIfPresent(SqglSqczGywlDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(SqglSqczGywlDO::getBusinessId, reqVO.getBusinessId())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(SqglSqczGywlDO::getAddTime);
        }
        Page<SqglSqczGywlDO> sqglSqczGywlPage = selectPage(page, wrapper);
        return new PageResult<>(sqglSqczGywlPage.getRecords(), sqglSqczGywlPage.getTotal());
    }
    default List<SqglSqczGywlDO> selectList(SqglSqczGywlListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SqglSqczGywlDO>()
            .eqIfPresent(SqglSqczGywlDO::getSqczId, reqVO.getSqczId())
            .eqIfPresent(SqglSqczGywlDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(SqglSqczGywlDO::getBusinessId, reqVO.getBusinessId())
        .orderByDesc(SqglSqczGywlDO::getAddTime));    }


    }
