package com.rs.module.acp.entity.gj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-管教业务-法务待办协同关联文书信息 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 实战平台-管教业务-法务待办协同关联文书信息新增/修改 Request VO")
@TableName("acp_gj_fwdbxt_wsxx")
@KeySequence("acp_gj_fwdbxt_wsxx_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_gj_fwdbxt")
public class FwdbxtWsxxDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 法务待办协同ID
     */
    @ApiModelProperty("法务待办协同ID")
    private String fwdbxtId;
    /**
     * 文书编号
     */
    @ApiModelProperty("文书编号")
    private String wsbh;
    /**
     * 文书文号
     */
    @ApiModelProperty("文书文号")
    private String wswh;
    /**
     * 文书名称
     */
    @ApiModelProperty("文书名称")
    private String wsmc;
    /**
     * 存放路径
     */
    @ApiModelProperty("存放路径")
    private String cflj;
    /**
     * 文件大小
     */
    @ApiModelProperty("文件大小")
    private Integer wjdx;
    /**
     * 显示顺序
     */
    @ApiModelProperty("显示顺序")
    private Integer xssx;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date cjsj;

}
