package com.rs.module.acp.service.pi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.*;
import com.rs.module.acp.entity.pi.SqglBjldszDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.SqglMbpzDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.SqglMbpzDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-巡视管控-所情管理-模板配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqglMbpzServiceImpl extends BaseServiceImpl<SqglMbpzDao, SqglMbpzDO> implements SqglMbpzService {

    @Resource
    private SqglMbpzDao sqglMbpzDao;

    @Autowired
    private BspApi bspApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSqglMbpz(SqglMbpzSaveReqVO createReqVO) {

        if(CollectionUtil.isEmpty(createReqVO.getEventTypeSettingList())){
            throw new ServerException("所情类型不能为空");
        }
        if(CollectionUtil.isEmpty(createReqVO.getPushObjectSettingList())){
            throw new ServerException("推送对象不能为空");
        }
            // 插入
        SqglMbpzDO sqglMbpz = BeanUtils.toBean(createReqVO, SqglMbpzDO.class);

        sqglMbpz.setEventTypeSettings(JSON.toJSONString(createReqVO.getEventTypeSettingList()));
        sqglMbpz.setPushObjectSettings(JSON.toJSONString(createReqVO.getPushObjectSettingList()));
        if(CollectionUtil.isNotEmpty(createReqVO.getHandlingSituationList())){
            sqglMbpz.setHandlingSituationTemplate(JSON.toJSONString(createReqVO.getHandlingSituationList()));
        }

        //生成编号
        sqglMbpz.setTemplateCode(bspApi.executeByRuleCode("acp_sqgl_sqmbpz",null));

        sqglMbpzDao.insert(sqglMbpz);
        // 返回
        return sqglMbpz.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSqglMbpz(SqglMbpzSaveReqVO updateReqVO) {
        if(CollectionUtil.isEmpty(updateReqVO.getEventTypeSettingList())){
            throw new ServerException("所情类型不能为空");
        }
        if(CollectionUtil.isEmpty(updateReqVO.getPushObjectSettingList())){
            throw new ServerException("推送对象不能为空");
        }
        // 校验存在
        validateSqglMbpzExists(updateReqVO.getId());
        // 更新
        SqglMbpzDO updateObj = BeanUtils.toBean(updateReqVO, SqglMbpzDO.class);

        updateObj.setEventTypeSettings(JSON.toJSONString(updateReqVO.getEventTypeSettingList()));
        updateObj.setPushObjectSettings(JSON.toJSONString(updateReqVO.getPushObjectSettingList()));
        if(CollectionUtil.isNotEmpty(updateReqVO.getHandlingSituationList())){
            updateObj.setHandlingSituationTemplate(JSON.toJSONString(updateReqVO.getHandlingSituationList()));
        }

        sqglMbpzDao.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSqglMbpz(String id) {
        // 校验存在
        validateSqglMbpzExists(id);
        // 删除
        sqglMbpzDao.deleteById(id);
    }

    private void validateSqglMbpzExists(String id) {
        if (sqglMbpzDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情管理-模板配置数据不存在");
        }
    }

    @Override
    public SqglMbpzDO getSqglMbpz(String id) {
        return sqglMbpzDao.selectById(id);
    }

    @Override
    public PageResult<SqglMbpzDO> getSqglMbpzPage(SqglMbpzPageReqVO pageReqVO) {
        return sqglMbpzDao.selectPage(pageReqVO);
    }

    @Override
    public List<SqglMbpzRespVO> getSqglMbpzList(SqglMbpzListReqVO listReqVO) {
        if(ObjectUtil.isEmpty(listReqVO.getOrgCode())){
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<SqglMbpzDO> sqglMbpzDOList = sqglMbpzDao.selectList(listReqVO);
        if(CollectionUtil.isEmpty(sqglMbpzDOList)){
            return new ArrayList<>();
        }
        List<SqglMbpzRespVO> resList = new ArrayList<>();
        for(SqglMbpzDO sqglMbpzDO:sqglMbpzDOList){
            SqglMbpzRespVO temp = BeanUtils.toBean(sqglMbpzDO,SqglMbpzRespVO.class);
            if(ObjectUtil.isNotEmpty(sqglMbpzDO.getEventTypeSettings())){
                temp.setEventTypeSettingList(JSONArray.parseArray(sqglMbpzDO.getEventTypeSettings(), SqglMbpzSqlxRespVO.class));
            }
            if(ObjectUtil.isNotEmpty(sqglMbpzDO.getHandlingSituationTemplate())){
                temp.setHandlingSituationList(JSONArray.parseArray(sqglMbpzDO.getHandlingSituationTemplate(),SqglMbpzSqczRespVO.class));
            }
            if(ObjectUtil.isNotEmpty(sqglMbpzDO.getPushObjectSettings())){
                temp.setPushObjectSettingList(JSONArray.parseArray(sqglMbpzDO.getPushObjectSettings(),SqglMbpzTsdxRespVO.class));
            }
            resList.add(temp);
        }
        return resList;
    }

    @Override
    public SqglMbpzRespVO getSqglMbpzById(String id) {
        SqglMbpzDO sqglMbpzDO = sqglMbpzDao.selectById(id);
        SqglMbpzRespVO res = BeanUtils.toBean(sqglMbpzDO,SqglMbpzRespVO.class);
        if(ObjectUtil.isNotEmpty(sqglMbpzDO.getEventTypeSettings())){
            res.setEventTypeSettingList(JSONArray.parseArray(sqglMbpzDO.getEventTypeSettings(), SqglMbpzSqlxRespVO.class));
        }

        if(ObjectUtil.isNotEmpty(sqglMbpzDO.getHandlingSituationTemplate())){
            res.setHandlingSituationList(JSONArray.parseArray(sqglMbpzDO.getHandlingSituationTemplate(),SqglMbpzSqczRespVO.class));
        }
        if(ObjectUtil.isNotEmpty(sqglMbpzDO.getPushObjectSettings())){
            res.setPushObjectSettingList(JSONArray.parseArray(sqglMbpzDO.getPushObjectSettings(),SqglMbpzTsdxRespVO.class));
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(String id) {
        SqglMbpzDO sqglMbpzDO = getById(id);

        if(ObjectUtil.isEmpty(sqglMbpzDO)){
            throw new ServerException("所情模板不存在");
        }
        if(sqglMbpzDO.getIsEnabled() == 1){
            sqglMbpzDO.setIsEnabled((short) 0);
        }else {
            sqglMbpzDO.setIsEnabled((short) 1);
        }
        return updateById(sqglMbpzDO);
    }
}
