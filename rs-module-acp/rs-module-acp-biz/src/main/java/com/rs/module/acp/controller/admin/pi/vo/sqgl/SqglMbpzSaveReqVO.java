package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-模板配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglMbpzSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所请来源，字典：")
    @NotEmpty(message = "所请来源，字典：不能为空")
    private String eventSrc;

    @ApiModelProperty("模板名称")
    @NotEmpty(message = "模板名称不能为空")
    private String templateName;

    @ApiModelProperty("告警类型")
    private String alarmType;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("所情类型配置列表")
    private List<SqglMbpzSqlxSaveReqVO> eventTypeSettingList;

    @ApiModelProperty("处置情况模板列表")
    private List<SqglMbpzSqczSaveReqVO> handlingSituationList;

    @ApiModelProperty("推送对象配置列表")
    private List<SqglMbpzTsdxSaveReqVO> pushObjectSettingList;

}
