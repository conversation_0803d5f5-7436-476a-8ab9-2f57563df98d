package com.rs.module.acp.controller.app.pm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 待防误放人员信息
 * <AUTHOR>
 * @Date 2025/8/14 14:15
 */
@Data
@ApiModel(description = "待防误放人员信息")
public class AppCsFwfPrisonerVO implements TransPojo {

    @ApiModelProperty("人员ID")
    private String id;
    @ApiModelProperty("人员名称")
    private String xm;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String xb;
    @ApiModelProperty("年龄")
    private int age;
    @ApiModelProperty("出生日期")
    private String csrq;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("民族")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MZ")
    private String mz;
    @ApiModelProperty("籍贯")
    @Trans(type = TransType.DICTIONARY, key = "ZD_HJ")
    private String jg;
    @ApiModelProperty("业务类型")
    private String businessTypeName;
    @ApiModelProperty("业务原因")
    private String businessReasonName;
    @ApiModelProperty(value = "照片地址")
    private String frontPhoto;
}
