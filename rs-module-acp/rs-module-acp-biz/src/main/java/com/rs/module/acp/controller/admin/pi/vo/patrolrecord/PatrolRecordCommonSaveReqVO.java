package com.rs.module.acp.controller.admin.pi.vo.patrolrecord;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-巡视登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PatrolRecordCommonSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("巡控室ID")
    @NotEmpty(message = "巡控室ID不能为空")
    private String patrolRoomId;

    @ApiModelProperty("巡控室名称")
    @NotEmpty(message = "巡控室名称不能为空")
    private String patrolRoomName;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("待办来源 ZD_XSGKDBLY")
    @NotEmpty(message = "待办来源")
    private String todoSource;

    @ApiModelProperty("待办事由")
    @NotEmpty(message = "待办事由")
    private String todoReason;

    @ApiModelProperty("待办推送人身份证号")
    @NotEmpty(message = "待办推送人身份证号")
    private String todoPersonSfzh;

    @ApiModelProperty("待办推送人")
    @NotEmpty(message = "待办推送人")
    private String todoPerson;

    @ApiModelProperty("待办推送岗位")
    private String todoPost;

    @ApiModelProperty("待办推送时间")
    @NotEmpty(message = "待办推送时间")
    private Date todoPushTime;

    @ApiModelProperty("是否异常 0 正常,1 异常")
    private String isAbnormal;

}
