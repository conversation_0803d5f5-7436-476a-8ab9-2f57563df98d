package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-模板配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SqglMbpzPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所请来源，字典：")
    private String eventSrc;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("所情类型配置,存JSON")
    private String eventTypeSettings;

    @ApiModelProperty("处置情况模板,存JSON")
    private String handlingSituationTemplate;

    @ApiModelProperty("推送对象配置，存JSON")
    private String pushObjectSettings;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
