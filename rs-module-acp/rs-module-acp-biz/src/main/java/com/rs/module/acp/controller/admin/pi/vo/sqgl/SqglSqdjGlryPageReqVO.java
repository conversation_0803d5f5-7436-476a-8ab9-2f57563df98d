package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情登记关联人员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SqglSqdjGlryPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所情登记ID")
    private String sqdjId;

    @ApiModelProperty("人员类型（1：在押人员，2：工作人员，3:外来人员，4：报警人员)")
    private String personnelType;

    @ApiModelProperty("人员ID")
    private String personnelId;

    @ApiModelProperty("人员姓名")
    private String personnelName;

    @ApiModelProperty("照片URL")
    private String photoUrl;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
