package com.rs.module.acp.service.db;

import com.bsp.common.cache.DicUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.InProcessStageEnum;
import com.rs.module.acp.cons.InTypeEnums;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.dao.db.HealthCheckDao;
import com.rs.module.acp.entity.db.HealthCheckDO;
import com.rs.module.acp.service.db.components.PrisonerInInfoSyncContext;
import com.rs.module.acp.service.db.components.RegistrationInfoService;
import com.rs.module.acp.service.ds.DSPrisonRoomChangeService;
import com.rs.module.acp.util.GeneralUtil;
import com.rs.util.DateUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.InRecordJdsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.InRecordJdsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-入所登记（戒毒所） Service 实现类
 *
 * <AUTHOR>
 */
@Service("inRecordJdsService")
@Validated
public class InRecordJdsServiceImpl extends BaseServiceImpl<InRecordJdsDao, InRecordJdsDO> implements InRecordJdsService, RegistrationInfoService {

    @Resource
    private InRecordJdsDao inRecordJdsDao;
    @Resource
    private DbSocialRelationsService socialRelationsService;

    @Resource
    private HealthCheckDao healthCheckDao;
    @Resource
    private PrisonerInInfoSyncContext prisonerInInfoSyncContext;
    @Resource
    private DSPrisonRoomChangeService dsPrisonRoomChangeService;
    @Override
    public String createInRecordJds(InRecordJdsSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();

        String rybh = GeneralUtil.generateRybh(orgCode);
        // 插入

        //如果提交的状态为操作02，则设置当前阶段为01
        if (createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())) {
            createReqVO.setCurrentStep(InProcessStageEnum.PENDING_REGISTRATION.getCode());
        }if (createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())) {
            createReqVO.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
        }
        InRecordJdsDO inRecordJdsDO = BeanUtils.toBean(createReqVO, InRecordJdsDO.class);
        inRecordJdsDO.setId(GeneralUtil.generateUUID());
        inRecordJdsDO.setRybh(rybh);
        inRecordJdsDO.setJgrybm(rybh);
        inRecordJdsDO.setDah(GeneralUtil.generateId(1));
        //如果是快速入所类型，则设置所领导审批为待审批
        if (createReqVO.getRslx().equalsIgnoreCase(InTypeEnums.EMERGENCY.getCode())&&createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())) {
            inRecordJdsDO.setSpzt(LeaderApprovalStatusEnum.PENDING.getCode());
            inRecordJdsDO.setCurrentStep(InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode());
        }
        inRecordJdsDao.insert(inRecordJdsDO);

        // 插入子表
        List<dbSocialRelationsSaveReqVO> socialRelations = createReqVO.getSocialRelations();
        if (socialRelations != null && !socialRelations.isEmpty()) {
            for (dbSocialRelationsSaveReqVO socialRelation : socialRelations) {
                socialRelation.setId(UUID.randomUUID().toString().replaceAll("-", ""));
                socialRelation.setRybh(inRecordJdsDO.getRybh());
                socialRelationsService.createdbSocialRelations(socialRelation);
            }
        }
        // 返回
        return inRecordJdsDO.getJgrybm();
    }

    @Override
    public void updateInRecordJds(InRecordJdsSaveReqVO updateReqVO) {
        // 校验存在
        validateInRecordJdsExists(updateReqVO.getId());
        // 更新
        InRecordJdsDO updateObj = BeanUtils.toBean(updateReqVO, InRecordJdsDO.class);
        inRecordJdsDao.updateById(updateObj);
    }

    @Override
    public void deleteInRecordJds(String id) {
        // 校验存在
        validateInRecordJdsExists(id);
        // 删除
        inRecordJdsDao.deleteById(id);
    }

    private void validateInRecordJdsExists(String id) {
        if (inRecordJdsDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-入所登记（戒毒所）数据不存在");
        }
    }

    @Override
    public InRecordJdsDO getInRecordJds(String id) {
        return inRecordJdsDao.selectById(id);
    }

    @Override
    public PageResult<InRecordJdsDO> getInRecordJdsPage(InRecordJdsPageReqVO pageReqVO) {
        return inRecordJdsDao.selectPage(pageReqVO);
    }

    @Override
    public List<InRecordJdsDO> getInRecordJdsList(InRecordJdsListReqVO listReqVO) {
        return inRecordJdsDao.selectList(listReqVO);
    }

    @Override
    public CombineRespVO getCombineInfo(String rybh) {
        InRecordJdsListReqVO reqVO = new InRecordJdsListReqVO();
        reqVO.setRybh(rybh);
        List<InRecordJdsDO> dlist =  inRecordJdsDao.selectList(reqVO);

        InRecordJdsDO db = new InRecordJdsDO();
        if(dlist!=null&& !dlist.isEmpty()){
            db = dlist.get(0);
        } else if (dlist!=null&&dlist.size()==0) {
            return null;
        }
//        DetainRegKssDO db = detainRegKssDao.selectById(rybh);
        HealthCheckListReqVO listReqVO = new HealthCheckListReqVO();
        listReqVO.setRybh(db.getRybh());
        List<HealthCheckDO> list = healthCheckDao.selectList(listReqVO);
        CombineRespVO combineRespVO = BeanUtils.toBean(db, CombineRespVO.class);
        if(list!=null&&list.size()>0){
            HealthCheckDO healthCheckDO = list.get(0);
            combineRespVO.setYsyj(healthCheckDO.getYsyj());
            combineRespVO.setJcr(healthCheckDO.getJcr());
            combineRespVO.setJcsj(healthCheckDO.getJcsj());
            combineRespVO.setBz(healthCheckDO.getBz());
        }

        return combineRespVO;
    }

    @Override
    public void updateLeaderApprovalStatus(LeaderApprovalStatusReqVO updateReqVO) {
        // 校验存在
//    validateDetainRegKssExists(updateReqVO.getId());
        InRecordJdsDO inRecordJdsDO = new InRecordJdsDO();
        if(updateReqVO.getRybh()!=null&&updateReqVO.getRybh()!=""){
            inRecordJdsDO = getPrisonerInfo(updateReqVO.getRybh());
        }

        //入所类型
        String rslx = inRecordJdsDO.getRslx();
        // 更新
        InRecordJdsDO updateObj = BeanUtils.toBean(updateReqVO, InRecordJdsDO.class);
        updateObj.setId(inRecordJdsDO.getId());
        //如果是快速入所，所领导审批完成后流转到健康检查
        if(rslx!=null&&rslx.equalsIgnoreCase("02")){
            //原快速入所所领导审批后流转到健康登记
//            updateObj.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
            //现所领导审批后需流转到第一步登记信息，便于补录信息
            updateObj.setCurrentStep(InProcessStageEnum.PENDING_REGISTRATION.getCode());
            updateObj.setStatus(DetainRegStatusEnum.DRAFT.getCode());
        }else {
            updateObj.setCurrentStep(InProcessStageEnum.COMPLETED.getCode());
        }
        inRecordJdsDao.updateById(updateObj);

        //如果领导审批完成了，则从acp_db_in_record_kss写入在所表acp_pm_prisoner_kss_in
        syncDataToPrisonerJdsIn(updateReqVO);
        try {
            dsPrisonRoomChangeService.jdsSave(inRecordJdsDO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void syncDataToPrisonerJdsIn(LeaderApprovalStatusReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();
        String id = updateReqVO.getId();
        //领导同意，则同步数据
        if(updateReqVO.getSpzt()!=null&&updateReqVO.getSpzt().equalsIgnoreCase(LeaderApprovalStatusEnum.APPROVED.getCode())){
            inRecordJdsDao.syncDataToPrisonerJdsIn(id);
        }

        //同步社会关系数据
        prisonerInInfoSyncContext.getHandler("jds").syncSocialRelations(updateReqVO.getRybh(), orgCode,updateReqVO.getId());
    }

    @Override
    public InRecordJdsDO getPrisonerInfo(String rybh) {
        InRecordJdsListReqVO reqVO = new InRecordJdsListReqVO();
        reqVO.setRybh(rybh);
        List<InRecordJdsDO> dlist =  inRecordJdsDao.selectList(reqVO);

        InRecordJdsDO db = new InRecordJdsDO();
        if(dlist!=null&& !dlist.isEmpty()){
            db = dlist.get(0);
        } else if (dlist==null|| dlist.isEmpty()) {
            return null;
        }
        return db;
    }

    @Override
    public String ifExists(InRecordJdsSaveReqVO updateReqVO) {
        String id = "";
        if (inRecordJdsDao.selectById(updateReqVO.getId()) == null) {
            id = createInRecordJds(updateReqVO);
        }
        return id;
    }

    @Override
    public InRecordStatusVO getInRecordStatus(String rybh) {
        InRecordStatusVO db = inRecordJdsDao.getInRecordStatus(rybh);

        return db;
    }

    @Override
    public boolean updateStateInfo(String rybh, String spzt, String currentStep) {
        InRecordJdsDO inRecordJdsDO = getPrisonerInfo(rybh);
        if(inRecordJdsDO!=null){
            // 如果spzt==null 则保持原始值
            if(spzt==null){
                spzt = inRecordJdsDO.getSpzt();
            }
            // 如果currentStep==null 则保持原始值
            if(currentStep==null){
                currentStep = inRecordJdsDO.getCurrentStep();
            }
            inRecordJdsDO.setSpzt(spzt);
            inRecordJdsDO.setCurrentStep(currentStep);
            inRecordJdsDao.updateById(inRecordJdsDO);
        }

        return true;
    }

    @Override
    public boolean updateWristbandInfo(String rybh, String shid, String shbdzt, Date sdbdsj,String status) {
        InRecordJdsDO inRecordJdsDO = getPrisonerInfo(rybh);
        if (inRecordJdsDO != null) {
            // 更新手环信息
            inRecordJdsDO.setShid(shid);
            inRecordJdsDO.setShbdzt(shbdzt);
            inRecordJdsDO.setSdbdsj(sdbdsj);
            inRecordJdsDO.setStatus(status);
            inRecordJdsDao.updateById(inRecordJdsDO);
        }

        return true;
    }

    @Override
    public String getRslxInfo(String rybh) {
        InRecordJdsDO db = getPrisonerInfo(rybh);
        return db.getRslx();
    }

    @Override
    public CollectedPersonDetailVO getCollectedPersonDetail(prisonerInfoReqVO prisonerInfoReqVO) {
        InRecordJdsDO db = getPrisonerInfo(prisonerInfoReqVO.getJgrybm());
        return convertToCollectedPersonDetailVO(db);
    }

    private CollectedPersonDetailVO convertToCollectedPersonDetailVO(InRecordJdsDO entity) {
        CollectedPersonDetailVO vo = new CollectedPersonDetailVO();
        String appCode = HttpUtils.getAppCode();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        vo.setJZRYBH(entity.getJgrybm());
        vo.setRYBH(entity.getJgrybm());
        vo.setAJBH(entity.getAjbh());
        vo.setXM(entity.getXm());
        vo.setXMHYPY("");
        vo.setGMSFHM(entity.getZjhm());
        vo.setCYZJDM("");
        vo.setCYZJ("");
        vo.setZJHM("");
        vo.setXBDM(entity.getXb());
        vo.setXB(DicUtil.translate("ZD_XB", entity.getXb()));
        vo.setCSRQ(DateUtil.format(entity.getCsrq(),DateUtil.DATE_PATTERN));
        vo.setGJDM(entity.getGj());
        vo.setGJ(DicUtil.translate("ZD_GABBZ_GJ", entity.getXb()));
        vo.setMZDM(entity.getMz());
        vo.setMZ(DicUtil.translate("ZD_MZ", entity.getXb()));
        vo.setHJD_XZQHDM(entity.getHjd());
        vo.setHJD_XZQH(DicUtil.translate("ZD_JG", entity.getXb()));
        vo.setHJD_DZMC(entity.getHjdxz());
        vo.setXZD_XZQHDM(entity.getXzz());
        vo.setXZD_XZQH(DicUtil.translate("ZD_JG", entity.getXb()));
        vo.setXZD_DZMC(entity.getXzzxz());
        vo.setCSD_XZQHDM("");
        vo.setCSD_XZQH("");
        vo.setCSD_DZMC("");
        vo.setZZMMDM(entity.getZzmm());
        vo.setZZMM(DicUtil.translate("ZD_ZZMM", entity.getXb()));
        vo.setHYZKDM(entity.getHyzk());
        vo.setHYZK(DicUtil.translate("ZD_HYZK", entity.getXb()));
        vo.setZJXYDM(entity.getZjxy());
        vo.setZJXY(DicUtil.translate("ZD_ZJXY", entity.getXb()));
        vo.setXLDM(entity.getWhcd());
        vo.setXL(DicUtil.translate("ZD_WHCD", entity.getXb()));
        vo.setGRSFDM(entity.getSf());
        vo.setGRSF(DicUtil.translate("ZD_SF", entity.getXb()));
        vo.setTSSFDM(entity.getTssf());
        vo.setTSSF(DicUtil.translate("ZD_TSSF", entity.getXb()));
        vo.setLXDH("");
        vo.setBCJRYLBDM(entity.getGllb());
        vo.setBCJRYLB(DicUtil.translate("ZD_KSS_RYGLLB", entity.getXb()));
        vo.setCJR_XM(sessionUser.getName());
        vo.setCJR_SFHM(sessionUser.getIdCard());
        vo.setCJR_JH(sessionUser.getJobId());
        vo.setCJDW_GAJGJGDM(sessionUser.getOrgCode());
        vo.setCJDW_DWMC(sessionUser.getOrgName());
        vo.setCJSJ(DateUtil.nowNonDelimiterDateTimeStr());

        return vo;
    }
}
