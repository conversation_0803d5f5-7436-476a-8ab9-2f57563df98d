package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-报警联动设置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglBjldszRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所请来源，字典：ZD_JJKS_SQLY")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQLY")
    private String eventSrc;
    @ApiModelProperty("是否启用(0:否，1：是)")
    private Short isEnabled;
    @ApiModelProperty("视频异常报警简介")
    private String remark;
    @ApiModelProperty("可选联动配置，逗号分隔，字典：ZD_JJKS_LDPZ")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_LDPZ")
    private String optionalLinkageSettings;
    @ApiModelProperty("提示音，字典：ZD_JJKS_SQYJTSY")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQYJTSY")
    private String promptSound;

    @ApiModelProperty("告警类型配置列表")
    private List<SqglGjlxpzRespVO> gjlxList;
}
