package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情处置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqczApproveChildRespVO extends BaseVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("处置ID，这个ID由后台传给前端，前端保存是原样返回")
    private String sqczId;

    @ApiModelProperty("岗位名称")
    private String postName;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果 0 不同意,1 同意")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

}
