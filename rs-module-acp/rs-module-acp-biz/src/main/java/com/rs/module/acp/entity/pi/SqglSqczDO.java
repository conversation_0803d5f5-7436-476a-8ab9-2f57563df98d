package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-所情处置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_sqcz")
@KeySequence("acp_pi_sqgl_sqcz_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_sqcz")
public class SqglSqczDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 所情登记ID
     */
    private String sqdjId;
    /**
     * 处置人身份证号
     */
    private String handleUserSfzh;
    /**
     * 处置人名称
     */
    private String handleUserName;
    /**
     * 处置时间
     */
    private Date handleTime;
    /**
     * 处置人所属岗位名称
     */
    private String handlePostName;
    /**
     * 处置岗位编号
     */
    private String handlePostCode;
    /**
     * 处置预案
     */
    private String hanleGenericPlan;
    /**
     * 处置情况
     */
    private String handleInfo;
    /**
     * 处置类型（1：巡控、2：中间环节、3：所领导审批）
     */
    private String handleType;
    /**
     * 附件地址
     */
    private String attUrl;
    /**
     * 处置状态 0未办结 1已办结 2不通过 3通过
     */
    private String status;

    /**
     * 审批情况，存储审批结果
     */
    private String approveInfo;
}
