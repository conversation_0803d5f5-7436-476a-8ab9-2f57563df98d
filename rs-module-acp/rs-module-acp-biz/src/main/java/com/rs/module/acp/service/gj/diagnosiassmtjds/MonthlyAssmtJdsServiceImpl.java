package com.rs.module.acp.service.gj.diagnosiassmtjds;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.fhs.core.trans.util.ReflectUtils;
import com.gosun.zhjg.common.util.StringUtil;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.*;
import com.rs.module.acp.enums.gj.MonthlyAssmtJdsStatus;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;
import io.swagger.models.auth.In;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.util.*;

import com.rs.module.acp.entity.gj.MonthlyAssmtJdsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.MonthlyAssmtJdsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-月度考核(戒毒所) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MonthlyAssmtJdsServiceImpl extends BaseServiceImpl<MonthlyAssmtJdsDao, MonthlyAssmtJdsDO> implements MonthlyAssmtJdsService {

    @Resource
    private MonthlyAssmtJdsDao monthlyAssmtJdsDao;

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public String createMonthlyAssmtJds(MonthlyAssmtJdsSaveReqVO createReqVO) {
        // 插入
        MonthlyAssmtJdsDO monthlyAssmtJds = BeanUtils.toBean(createReqVO, MonthlyAssmtJdsDO.class);
        monthlyAssmtJdsDao.insert(monthlyAssmtJds);
        // 返回
        return monthlyAssmtJds.getId();
    }

    @Override
    public void updateMonthlyAssmtJds(MonthlyAssmtJdsSaveReqVO updateReqVO) {
        // 校验存在
        validateMonthlyAssmtJdsExists(updateReqVO.getId());
        // 更新
        MonthlyAssmtJdsDO updateObj = BeanUtils.toBean(updateReqVO, MonthlyAssmtJdsDO.class);
        monthlyAssmtJdsDao.updateById(updateObj);
    }

    @Override
    public void deleteMonthlyAssmtJds(String id) {
        // 校验存在
        validateMonthlyAssmtJdsExists(id);
        // 删除
        monthlyAssmtJdsDao.deleteById(id);
    }

    private void validateMonthlyAssmtJdsExists(String id) {
        if (monthlyAssmtJdsDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-月度考核(戒毒所)数据不存在");
        }
    }

    @Override
    public MonthlyAssmtJdsDO getMonthlyAssmtJds(String id) {
        return monthlyAssmtJdsDao.selectById(id);
    }

    @Override
    public PageResult<MonthlyAssmtJdsDO> getMonthlyAssmtJdsPage(MonthlyAssmtJdsPageReqVO pageReqVO) {
        return monthlyAssmtJdsDao.selectPage(pageReqVO);
    }

    @Override
    public List<MonthlyAssmtJdsDO> getMonthlyAssmtJdsList(MonthlyAssmtJdsListReqVO listReqVO) {
        return monthlyAssmtJdsDao.selectList(listReqVO);
    }

    @Override
    public List<MonthlyAssmtJdsDO> getMonthlyAssmtJdsListByInJds() {
        return monthlyAssmtJdsDao.getMonthlyAssmtJdsListByInJds();
    }

    @Override
    public List<AssmtCommonBO> getModel() {
        List<AssmtCommonBO> nrList = new ArrayList<>();
        nrList.add(new AssmtCommonBO("assmtContentValue1", "遵章守纪", null));
        nrList.add(new AssmtCommonBO("assmtContentValue2", "戒毒康复", null));
        nrList.add(new AssmtCommonBO("assmtContentValue3", "教育学习", null));
        nrList.add(new AssmtCommonBO("assmtContentValue4", "康复劳动", null));
        nrList.add(new AssmtCommonBO("assmtContentValue5", "坦白检举", null));
        nrList.add(new AssmtCommonBO("assmtContentTotalScore", "当月得分", null));
        return nrList;
    }

    @Override
    public void commit(MonthlyAssmtJdsCommitReqVO createReqVO) {
        MonthlyAssmtJdsDO monthlyAssmtJdsDO = monthlyAssmtJdsDao.selectById(createReqVO.getId());
        if (Objects.isNull(monthlyAssmtJdsDO)) {
            throw new ServerException("实战平台-管教业务-月度考核(戒毒所)数据不存在");
        }
        List<AssmtCommonBO> khjg = createReqVO.getKhjg();
        int sum = 0;
        for (AssmtCommonBO assmtCommonBO : khjg) {
            ReflectUtil.setFieldValue(monthlyAssmtJdsDO, assmtCommonBO.getKeyCode(), assmtCommonBO.getKeyValue());
            try {
                sum = sum + Integer.parseInt(assmtCommonBO.getKeyValue());
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(monthlyAssmtJdsDO.getAssmtContentTotalScore())) {
            monthlyAssmtJdsDO.setAssmtContentTotalScore(sum + "");
        }
        monthlyAssmtJdsDO.setStatus(MonthlyAssmtJdsStatus.DJYQM.getCode());
        monthlyAssmtJdsDO.setBonusOrPenaltyPointsSituati(createReqVO.getBonusOrPenaltyPointsSituati());
        monthlyAssmtJdsDO.setAccording(createReqVO.getAccording());
        monthlyAssmtJdsDO.setAsstmContentJson(JSON.toJSONString(khjg));
        monthlyAssmtJdsDO.setAssmtTime(SDF.format(new Date()));
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        monthlyAssmtJdsDO.setAssmtUser(sessionUser.getName());
        monthlyAssmtJdsDO.setAssmtUserSfzh(sessionUser.getIdCard());

        monthlyAssmtJdsDao.updateById(monthlyAssmtJdsDO);

    }

    @Override
    public List<MonthlyAssmtJdsDO> getRecordByJgrybm(String jgrybm) {
        LambdaQueryWrapper<MonthlyAssmtJdsDO> wrapper = Wrappers.lambdaQuery(MonthlyAssmtJdsDO.class);
        wrapper.eq(MonthlyAssmtJdsDO::getJgrybm, jgrybm).ne(MonthlyAssmtJdsDO::getStatus, MonthlyAssmtJdsStatus.DDJ.getCode());
        wrapper.orderByAsc(MonthlyAssmtJdsDO::getMonthPeriod);
        return monthlyAssmtJdsDao.selectList(wrapper);
    }

    @Override
    public Map<String, String> getAvgRecordByJgrybm(String jgrybm) {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isEmpty(jgrybm)) {
            return map;
        }
        int assmtContentValue1Total = 0, assmtContentValue2Total = 0, assmtContentValue3Total = 0,
                assmtContentValue4Total = 0, assmtContentValue5Total = 0;
        List<MonthlyAssmtJdsDO> recordByJgrybm = getRecordByJgrybm(jgrybm);
        if (CollectionUtil.isNotEmpty(recordByJgrybm)) {
            int length = recordByJgrybm.size();
            for (MonthlyAssmtJdsDO monthlyAssmtJdsDO : recordByJgrybm) {
                assmtContentValue1Total += Integer.parseInt(monthlyAssmtJdsDO.getAssmtContentValue1());
                assmtContentValue2Total += Integer.parseInt(monthlyAssmtJdsDO.getAssmtContentValue2());
                assmtContentValue3Total += Integer.parseInt(monthlyAssmtJdsDO.getAssmtContentValue3());
                assmtContentValue4Total += Integer.parseInt(monthlyAssmtJdsDO.getAssmtContentValue4());
                assmtContentValue5Total += Integer.parseInt(monthlyAssmtJdsDO.getAssmtContentValue5());
            }
            int assmtContentValue1Avg = assmtContentValue1Total / length;
            int assmtContentValue2Avg = assmtContentValue2Total / length;
            int assmtContentValue3Avg = assmtContentValue3Total / length;
            int assmtContentValue4Avg = assmtContentValue4Total / length;
            int assmtContentValue5Avg = assmtContentValue5Total / length;
            map.put("assmtContentValue1", "" + assmtContentValue1Avg);
            map.put("assmtContentValue2", "" + assmtContentValue2Avg);
            map.put("assmtContentValue3", "" + assmtContentValue3Avg);
            map.put("assmtContentValue4", "" + assmtContentValue4Avg);
            map.put("assmtContentValue5", "" + assmtContentValue5Avg);
            map.put("assmtContentTotalScore", "" +
                    (assmtContentValue1Avg + assmtContentValue2Avg + assmtContentValue3Avg + assmtContentValue4Avg + assmtContentValue5Avg));
        } else {
            map.put("assmtContentValue1", "0");
            map.put("assmtContentValue2", "0");
            map.put("assmtContentValue3", "0");
            map.put("assmtContentValue4", "0");
            map.put("assmtContentValue5", "0");
            map.put("assmtContentTotalScore", "0");
        }
        return map;
    }

    @Override
    public void confirm(MonthlyAssmtJdsConfirmReqVO confirmReqVO) {
        // 校验存在
        MonthlyAssmtJdsDO monthlyAssmtJdsDO = monthlyAssmtJdsDao.selectById(confirmReqVO.getId());
        if (Objects.isNull(monthlyAssmtJdsDO)) {
            throw new ServerException("实战平台-管教业务-月度考核(戒毒所)数据不存在");
        }
        if (!MonthlyAssmtJdsStatus.DJYQM.getCode().equals(monthlyAssmtJdsDO.getStatus())) {
            throw new ServerException("当前非【待戒员签名】状态，无需进行确认操作！");
        }
        MonthlyAssmtJdsDO monthlyAssmtJds = BeanUtils.toBean(confirmReqVO, MonthlyAssmtJdsDO.class);
        monthlyAssmtJds.setStatus(MonthlyAssmtJdsStatus.YWC.getCode());
        monthlyAssmtJds.setConfirmTime(new Date());
        updateById(monthlyAssmtJds);
    }


}
