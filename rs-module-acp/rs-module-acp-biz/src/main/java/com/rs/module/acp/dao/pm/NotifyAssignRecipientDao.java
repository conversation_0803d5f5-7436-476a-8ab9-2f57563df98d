package com.rs.module.acp.dao.pm;

import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.entity.pm.NotifyAssignRecipientDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
 * 实战平台-监管管理-通知交办与收件人关联
 *
 * <AUTHOR>
 */
@Mapper
public interface NotifyAssignRecipientDao extends IBaseDao<NotifyAssignRecipientDO> {

    default List<NotifyAssignRecipientDO> selectListByNotifyAssignId(String notifyAssignId) {
        return selectList(new LambdaQueryWrapperX<NotifyAssignRecipientDO>().eq(NotifyAssignRecipientDO::getNotifyAssignId, notifyAssignId));
    }

    default int deleteByNotifyAssignId(String notifyAssignId) {
        return delete(new LambdaQueryWrapperX<NotifyAssignRecipientDO>().eq(NotifyAssignRecipientDO::getNotifyAssignId, notifyAssignId));
    }

}
