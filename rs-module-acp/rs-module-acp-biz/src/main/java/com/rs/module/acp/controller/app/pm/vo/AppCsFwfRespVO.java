package com.rs.module.acp.controller.app.pm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 防误放信息
 * <AUTHOR>
 * @Date 2025/8/14 16:27
 */
@Data
@ApiModel(description = "防误放信息")
public class AppCsFwfRespVO {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("业务类型名称")
    private String businessTypeName;
    @ApiModelProperty("业务原因名称")
    private String businessReasonName;
    @ApiModelProperty("核验结果（01：待核验，02：验证成功，03：验证失败）")
    private String checkResultName;
    @ApiModelProperty("申请时间")
    private Date operateTime;
    @ApiModelProperty("经办民警")
    private String operatePolice;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String xb;
    @ApiModelProperty("年龄")
    private int age;

}
