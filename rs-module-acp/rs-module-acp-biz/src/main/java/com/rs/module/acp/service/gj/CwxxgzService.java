package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.CwxxgzSaveReqVO;
import com.rs.module.acp.entity.gj.CwxxgzDO;

import javax.validation.Valid;

/**
 * 实战平台-监管管理-错误信息更正 Service 接口
 *
 * <AUTHOR>
 */
public interface CwxxgzService extends IBaseService<CwxxgzDO>{

    /**
     * 创建实战平台-监管管理-错误信息更正
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCwxxgz(@Valid CwxxgzSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-错误信息更正
     *
     * @param updateReqVO 更新信息
     */
    void updateCwxxgz(@Valid CwxxgzSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-错误信息更正
     *
     * @param id 编号
     */
    void deleteCwxxgz(String id);

    /**
     * 获得实战平台-监管管理-错误信息更正
     *
     * @param id 编号
     * @return 实战平台-监管管理-错误信息更正
     */
    CwxxgzDO getCwxxgz(String id);

}
