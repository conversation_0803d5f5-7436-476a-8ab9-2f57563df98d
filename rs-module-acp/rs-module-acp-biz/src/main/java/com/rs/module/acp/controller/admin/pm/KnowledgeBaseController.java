package com.rs.module.acp.controller.admin.pm;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.KnowledgeBaseRespVO;
import com.rs.module.acp.controller.admin.pm.vo.KnowledgeBaseSaveReqVO;
import com.rs.module.acp.entity.pm.KnowledgeBaseDO;
import com.rs.module.acp.service.pm.KnowledgeBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合业务-知识库")
@RestController
@RequestMapping("/acp/pm/knowledgeBase")
@Validated
public class KnowledgeBaseController {

    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    @PostMapping("/create")
    @ApiOperation(value = "创建知识库")
    public CommonResult<String> createKnowledgeBase(@Valid @RequestBody KnowledgeBaseSaveReqVO createReqVO) {
        return success(knowledgeBaseService.createKnowledgeBase(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新知识库")
    public CommonResult<Boolean> updateKnowledgeBase(@Valid @RequestBody KnowledgeBaseSaveReqVO updateReqVO) {
        knowledgeBaseService.updateKnowledgeBase(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除知识库")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteKnowledgeBase(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           knowledgeBaseService.deleteKnowledgeBase(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得知识库信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<KnowledgeBaseRespVO> getKnowledgeBase(@RequestParam("id") String id) {
        KnowledgeBaseDO knowledgeBase = knowledgeBaseService.getKnowledgeBase(id);
        return success(BeanUtils.toBean(knowledgeBase, KnowledgeBaseRespVO.class));
    }

}
