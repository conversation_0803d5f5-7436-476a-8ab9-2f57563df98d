package com.rs.module.acp.service.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.db.vo.CsfwfSaveReqVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfInfoReqVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfPrisonerVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfRespVO;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfStatisticsVO;
import com.rs.module.acp.dao.db.CsfwfDao;
import com.rs.module.acp.entity.db.BiometricInfoDO;
import com.rs.module.acp.entity.db.CsfwfDO;
import com.rs.module.acp.entity.db.OutBiometricInfoDO;
import com.rs.module.acp.enums.db.CsFwfBusinessTypeEnum;
import com.rs.module.acp.enums.db.CsFwfCheckResultEnum;
import com.rs.module.acp.enums.db.CsFwfMandatoryTypeEnum;
import com.rs.module.acp.enums.db.VerificationMode;
import com.rs.module.acp.util.CommonUtils;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.entity.pm.PrisonerListDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.util.DicUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 实战平台-收押业务-出所防误放 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class CsfwfServiceImpl extends BaseServiceImpl<CsfwfDao, CsfwfDO> implements CsfwfService {

    @Resource
    private CsfwfDao csfwfDao;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private BiometricInfoService biometricInfoService;
    @Resource
    private OutBiometricInfoService outBiometricInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createKssCsFwf(String jgrybm, String businessId, String businessType, String businessReason) {
        CsfwfDO csfwf = new CsfwfDO();
        PrisonerInDO prisonerInOne = prisonerService.getPrisonerInOne(jgrybm);
        if (prisonerInOne == null) {
            throw new ServerException("新增-出所防误放数据失败，人员数据不在");
        }
        csfwf.setPrisonerId(prisonerInOne.getId());
        csfwf.setJgrybm(jgrybm);
        csfwf.setJgryxm(prisonerInOne.getXm());
        csfwf.setRoomId(prisonerInOne.getJsh());
        csfwf.setRoomName(prisonerInOne.getRoomName());
        csfwf.setXb(prisonerInOne.getXb());
        csfwf.setZjhm(prisonerInOne.getZjhm());
        csfwf.setBusinessType(businessType);
        csfwf.setBusinessId(businessId);
        csfwf.setBusinessReason(businessReason);
        csfwf.setCheckResult(CsFwfCheckResultEnum.WAIT_VERIFY.getCode());
        // TODO 下发比对信息
        // 把收押生物特征信息复制到出所特征信息
        List<BiometricInfoDO> biometricInfoByRybh = biometricInfoService.getBiometricInfoByRybh(jgrybm);
        List<OutBiometricInfoDO> outBiometricInfoDOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(biometricInfoByRybh)) {
            for (BiometricInfoDO biometricInfoDO : biometricInfoByRybh) {
                OutBiometricInfoDO infoDO = OutBiometricInfoDO.builder()
                        .jgrybm(jgrybm).jgryxm(prisonerInOne.getXm())
                        .cjxmlx(biometricInfoDO.getCjxmlx()).swtz(biometricInfoDO.getSwtz())
                        .swtzfj(biometricInfoDO.getSwtzfj()).hyjg("0")
                        .bz(biometricInfoDO.getBz())
                        .build();
                outBiometricInfoDOList.add(infoDO);
            }
        }
        if (outBiometricInfoDOList.size() > 0) {
            outBiometricInfoService.saveBatch(outBiometricInfoDOList);
        }
        csfwfDao.insert(csfwf);
        return csfwf.getId();
    }

    @Override
    public String createCsfwf(CsfwfSaveReqVO createReqVO) {
        // 插入
        CsfwfDO csfwf = BeanUtils.toBean(createReqVO, CsfwfDO.class);
        csfwfDao.insert(csfwf);
        // 返回
        return csfwf.getId();
    }

    @Override
    public void updateCsfwf(CsfwfSaveReqVO updateReqVO) {
        // 校验存在
        validateCsfwfExists(updateReqVO.getId());
        // 更新
        CsfwfDO updateObj = BeanUtils.toBean(updateReqVO, CsfwfDO.class);
        csfwfDao.updateById(updateObj);
    }

    @Override
    public void deleteCsfwf(String id) {
        // 校验存在
        validateCsfwfExists(id);
        // 删除
        csfwfDao.deleteById(id);
    }

    private void validateCsfwfExists(String id) {
        if (csfwfDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-出所防误放数据不存在");
        }
    }

    @Override
    public CsfwfDO getCsfwf(String id) {
        return csfwfDao.selectById(id);
    }

    @Override
    public List<AppCsFwfStatisticsVO> getCsfwfStatistics(String orgCode, String businessType) {
        List<AppCsFwfStatisticsVO> list = csfwfDao.getCsfwfStatistics(orgCode, businessType);
        Map<String, AppCsFwfStatisticsVO> tempMap = list.stream().collect(Collectors.toMap(AppCsFwfStatisticsVO::getCode, v -> v));
        Map<String, String> map = DicUtils.getMap("ZD_SFCSCSYY");
        List<AppCsFwfStatisticsVO> resultList = new ArrayList<>(map.size());
        map.forEach((k, v) -> {
            AppCsFwfStatisticsVO statisticsVO = tempMap.get(k);
            if (statisticsVO == null) {
                statisticsVO = new AppCsFwfStatisticsVO();
                statisticsVO.setCode(k);
                statisticsVO.setName(v);
                statisticsVO.setTotal(0);
            } else {
                statisticsVO.setName(v);
            }
            resultList.add(statisticsVO);
        });
        // 业务类型（1：出所、2：入所）
        List<String> priorityNames = new ArrayList<>(0);
        if ("1".equals(businessType)) {
            priorityNames.add("刑满释放");
            priorityNames.add("投送监狱");
            priorityNames.add("取保候审");
        }
        if ("2".equals(businessType)) {
            priorityNames.add("出所就医");
            priorityNames.add("提解出所");
            priorityNames.add("暂时离所");
        }
        resultList.sort((o1, o2) -> {
            boolean isO1Priority = priorityNames.contains(o1.getName());
            boolean isO2Priority = priorityNames.contains(o2.getName());
            if (isO1Priority && isO2Priority) {
                return priorityNames.indexOf(o1.getName()) - priorityNames.indexOf(o2.getName());
            } else if (isO1Priority) {
                return -1;
            } else if (isO2Priority) {
                return 1;
            } else {
                return 0;
            }
        });
        return resultList;
    }

    @Override
    public AppCsFwfPrisonerVO getPrisonerByPrisonerId(String prisonerId) {
        CsfwfDO csfwfDO = getFwfInfo(prisonerId);
        PrisonerInDO prisonerInOne = prisonerService.getPrisonerInOne(csfwfDO.getJgrybm());
        if (prisonerInOne == null) {
            throw new ServerException("出所防误放数据失败，人员数据不在");
        }
        AppCsFwfPrisonerVO csFwfPrisonerVO = new AppCsFwfPrisonerVO();
        csFwfPrisonerVO.setId(prisonerInOne.getId());
        csFwfPrisonerVO.setXm(prisonerInOne.getXm());
        csFwfPrisonerVO.setXb(prisonerInOne.getXb());
        csFwfPrisonerVO.setZjhm(prisonerInOne.getZjhm());
        csFwfPrisonerVO.setMz(prisonerInOne.getMz());
        csFwfPrisonerVO.setJg(prisonerInOne.getJg());
        csFwfPrisonerVO.setBusinessReasonName(getBusinessReasonName(csfwfDO.getBusinessReason()));
        csFwfPrisonerVO.setBusinessTypeName(CsFwfBusinessTypeEnum.getByCode(csfwfDO.getBusinessType()).getName());
        csFwfPrisonerVO.setFrontPhoto(prisonerInOne.getFrontPhoto());
        if (prisonerInOne.getCsrq() != null) {
            //计算年龄
            try {
                csFwfPrisonerVO.setAge(new Date().getYear() - prisonerInOne.getCsrq().getYear());
                csFwfPrisonerVO.setCsrq(DateUtil.format(prisonerInOne.getCsrq(), "yyyy年MM月dd日"));
            } catch (Exception e) {
                log.error("计算年龄失败", e);
            }
        }
        return csFwfPrisonerVO;
    }

    @Override
    public String receiveCompareResult(String prisonerId, String checkModel, String checkResult) {
        CsfwfDO csfwfDO = getFwfInfo(prisonerId);
        String verificationMode = csfwfDO.getVerificationMode();
        List<VerificationMode> verificationModes = new ArrayList<>(0);
        if (StrUtil.isNotBlank(verificationMode)) {
            List<VerificationMode> tempList = JSONUtil.toList(verificationMode, VerificationMode.class);
            for (VerificationMode temp : tempList) {
                if (StrUtil.equals(temp.getCode(), checkModel)) {
                    temp.setResult(checkResult);
                }
                verificationModes.add(temp);
            }
        } else {
            VerificationMode vm = new VerificationMode();
            vm.setCode(checkModel);
            vm.setResult(checkResult);
            verificationModes.add(vm);
        }
        CsfwfDO update = new CsfwfDO();
        update.setId(csfwfDO.getId());
        update.setVerificationMode(JSONUtil.toJsonStr(verificationModes));
        csfwfDao.updateById(update);
        return csfwfDO.getId();
    }

    @Override
    @Transactional
    public String completeCompare(AppCsFwfInfoReqVO reqVO) {
        CsfwfDO fwfInfo = getFwfInfo(reqVO.getPrisonerId());
        List<VerificationMode> verificationModes = reqVO.getVerificationModes();
        if (CollUtil.isEmpty(verificationModes)) {
            throw new ServerException("请选择验证方式");
        }
        CsfwfDO update = new CsfwfDO();
        update.setId(fwfInfo.getId());
        if (fwfInfo.getBusinessType().equals(CsFwfBusinessTypeEnum.OUT.getCode())) {
            // 出所
            if (reqVO.getMandatoryType().equals(CsFwfMandatoryTypeEnum.FORCE_IN.getCode())) {
                throw new ServerException("强制类型错误，不能强制带人");
            }
        }
        if (fwfInfo.getBusinessType().equals(CsFwfBusinessTypeEnum.IN.getCode())) {
            // 入所
            if (reqVO.getMandatoryType().equals(CsFwfMandatoryTypeEnum.FORCE_OUT.getCode())) {
                throw new ServerException("强制类型错误,不能强制带出");
            }
        }
        boolean result = verificationModes.stream().filter(vm -> vm.getResult().equals("1"))
                .findAny().isPresent();
        if (result) {
            update.setCheckResult(CsFwfCheckResultEnum.VERIFY_SUCCESS.getCode());
        } else {
            update.setCheckResult(CsFwfCheckResultEnum.VERIFY_FAIL.getCode());
        }
        update.setVerificationMode(JSONUtil.toJsonStr(verificationModes));
        update.setMandatoryReason(reqVO.getMandatoryReason());
        update.setMandatoryType(reqVO.getMandatoryType());
        update.setOperateTime(new Date());
        update.setOperatePolice(SessionUserUtil.getSessionUser().getIdCard());
        update.setOperatePolice(SessionUserUtil.getSessionUser().getName());
        csfwfDao.updateById(update);
        if (fwfInfo.getBusinessType().equals(CsFwfBusinessTypeEnum.OUT.getCode())) {
            // 更新生物特征
            List<OutBiometricInfoDO> outBiometricInfoRespVOS = outBiometricInfoService.getByJgrybmAndHyjg(fwfInfo.getJgrybm(), "0");
            Map<String, OutBiometricInfoDO> outBiometricInfoMap = outBiometricInfoRespVOS.stream()
                    .collect(Collectors.toMap(OutBiometricInfoDO::getCjxmlx, v -> v));
            List<OutBiometricInfoDO> updateList = new ArrayList<>();
            CsFwfMandatoryTypeEnum fwfMandatoryTypeEnum = null;
            if (update.getMandatoryType() != null) {
                fwfMandatoryTypeEnum = CsFwfMandatoryTypeEnum.getByCode(update.getMandatoryType());
            }
            for (VerificationMode vm : verificationModes) {
                OutBiometricInfoDO outBiometricInfoDO = outBiometricInfoMap.get(vm.getCode());
                if (outBiometricInfoDO != null) {
                    OutBiometricInfoDO updateBID = new OutBiometricInfoDO();
                    updateBID.setId(outBiometricInfoDO.getId());
                    if ("1".equals(vm.getResult())) {
                        updateBID.setHyjg("1");
                    } else {
                        updateBID.setHyjg("2");
                    }
                    if (fwfMandatoryTypeEnum != null) {
                        updateBID.setBz(String.format("%s(%s)", fwfMandatoryTypeEnum.getName(), update.getMandatoryReason()));
                    }
                    updateList.add(updateBID);
                }
            }
            if (updateList.size() > 0) {
                outBiometricInfoService.updateBatchById(updateList);
            }
            // 出所更新在押业务出所流程

        }
        return "";
    }

    @Override
    public List<AppCsFwfRespVO> listFwfInfo(String timeType, String businessType, String orgCode) {
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(timeType);
        LambdaQueryWrapper<CsfwfDO> wrapper = new LambdaQueryWrapper<CsfwfDO>();
        wrapper.eq(CsfwfDO::getOrgCode, orgCode)
                .in(CsfwfDO::getCheckResult, CsFwfCheckResultEnum.VERIFY_SUCCESS.getCode(), CsFwfCheckResultEnum.VERIFY_FAIL.getCode())
                .between(commonAppRecordPeriod.get("startTime") != null, CsfwfDO::getOperateTime,
                        commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"))
                .orderByDesc(CsfwfDO::getOperateTime);
        if (StrUtil.isNotBlank(businessType)) {
            wrapper.eq(CsfwfDO::getBusinessType, businessType);
        }
        List<CsfwfDO> list = csfwfDao.selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> jgrybms = list.stream().map(CsfwfDO::getJgrybm).collect(Collectors.toList());
        List<PrisonerListDO> prisonerListVm = prisonerService.getPrisonerListVm(jgrybms);
        Map<String, PrisonerListDO> map = prisonerListVm.stream().collect(Collectors.toMap(PrisonerListDO::getJgrybm,
                prisonerListDO -> prisonerListDO));
        List<AppCsFwfRespVO> resultList = new ArrayList<>();
        for (CsfwfDO csfwfDO : list) {
            AppCsFwfRespVO fwfRespVO = new AppCsFwfRespVO();
            fwfRespVO.setId(csfwfDO.getId());
            fwfRespVO.setRoomName(csfwfDO.getRoomName());
            CsFwfBusinessTypeEnum csFwfBusinessTypeEnum = CsFwfBusinessTypeEnum.getByCode(csfwfDO.getBusinessType());
            fwfRespVO.setBusinessTypeName(csFwfBusinessTypeEnum.getName());
            fwfRespVO.setBusinessReasonName(getBusinessReasonName(csfwfDO.getBusinessReason()));
            CsFwfCheckResultEnum csFwfCheckResultEnum = CsFwfCheckResultEnum.getByCode(csfwfDO.getCheckResult());
            fwfRespVO.setCheckResultName(csFwfCheckResultEnum.getName());
            fwfRespVO.setOperateTime(csfwfDO.getOperateTime());
            fwfRespVO.setOperatePolice(csfwfDO.getOperatePolice());
            PrisonerListDO prisonerListDO = map.get(csfwfDO.getJgrybm());
            if (prisonerListDO != null) {
                fwfRespVO.setXb(prisonerListDO.getXb());
                fwfRespVO.setJgryxm(prisonerListDO.getXm());
                if (prisonerListDO.getCsrq() != null) {
                    try {
                        fwfRespVO.setAge(new Date().getYear() - prisonerListDO.getCsrq().getYear());
                    } catch (Exception e) {
                        log.error("计算年龄失败", e);
                    }
                }
            }
            resultList.add(fwfRespVO);
        }
        return resultList;
    }

    @Override
    public List<AppCsFwfPrisonerVO> getWaitCheckPrisonerList(String orgCode) {
        LambdaQueryWrapper<CsfwfDO> queryWrapper = Wrappers.lambdaQuery(CsfwfDO.class);
        queryWrapper.eq(CsfwfDO::getOrgCode, orgCode)
                .eq(CsfwfDO::getCheckResult, CsFwfCheckResultEnum.WAIT_VERIFY.getCode())
                .between(CsfwfDO::getAddTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()));
        List<CsfwfDO> list = csfwfDao.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> jgrybms = list.stream().map(CsfwfDO::getJgrybm).collect(Collectors.toList());
        List<PrisonerListDO> prisonerListVm = prisonerService.getPrisonerListVm(jgrybms);
        Map<String, PrisonerListDO> map = prisonerListVm.stream().collect(Collectors.toMap(PrisonerListDO::getJgrybm,
                prisonerListDO -> prisonerListDO));
        List<AppCsFwfPrisonerVO> resultList = new ArrayList<>();
        for (CsfwfDO csfwfDO : list) {
            AppCsFwfPrisonerVO appCsFwfPrisonerVO = new AppCsFwfPrisonerVO();
            appCsFwfPrisonerVO.setId(csfwfDO.getPrisonerId());
            appCsFwfPrisonerVO.setXm(csfwfDO.getJgryxm());
            appCsFwfPrisonerVO.setXb(csfwfDO.getXb());
            PrisonerListDO prisonerListDO = map.get(csfwfDO.getJgrybm());
            if (prisonerListDO != null) {
                appCsFwfPrisonerVO.setFrontPhoto(csfwfDO.getJgrybm());
                appCsFwfPrisonerVO.setMz(prisonerListDO.getMz());
                appCsFwfPrisonerVO.setJg(prisonerListDO.getJg());
                appCsFwfPrisonerVO.setZjhm(prisonerListDO.getZjhm());
                if (prisonerListDO.getCsrq() != null) {
                    //计算年龄
                    try {
                        appCsFwfPrisonerVO.setAge(new Date().getYear() - prisonerListDO.getCsrq().getYear());
                        appCsFwfPrisonerVO.setCsrq(DateUtil.format(prisonerListDO.getCsrq(), "yyyy年MM月dd日"));
                    } catch (Exception e) {
                        log.error("计算年龄失败", e);
                    }
                }
            }
            resultList.add(appCsFwfPrisonerVO);
        }
        return resultList;
    }

    @Override
    public CsfwfDO getCsfwfByBusinessId(String businessId) {
        CsfwfDO csfwfDO = csfwfDao.selectOne(CsfwfDO::getBusinessId, businessId);
        return csfwfDO;
    }

    private CsfwfDO getFwfInfo(String prisonerId) {
        List<CsfwfDO> csfwfDOS = csfwfDao.selectList(CsfwfDO::getPrisonerId, prisonerId,
                CsfwfDO::getCheckResult, CsFwfCheckResultEnum.WAIT_VERIFY.getCode());
        if (CollUtil.isEmpty(csfwfDOS)) {
            throw new ServerException("出所防误放数据不存在,请核实出所流程是否正确");
        }
        return csfwfDOS.get(0);
    }

    private String getBusinessReasonName(String businessReason) {
        String translate = DicUtils.translate("ZD_SFCSCSYY", businessReason);
        if (StrUtil.isNotBlank(translate)) {
            return translate;
        }
        return "";
    }


}
