package com.rs.module.acp.controller.app.pi;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pi.vo.NightDoorRecordRespVO;
import com.rs.module.acp.controller.admin.pi.vo.NightDoorRecordSaveReqVO;
import com.rs.module.acp.controller.app.pi.vo.PatrolRecordAppSaveReqVO;
import com.rs.module.acp.entity.pi.NightDoorRecordDO;
import com.rs.module.acp.service.pi.NightDoorRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "仓外屏-夜间开门")
@RestController
@RequestMapping("/app/acp/pi/nightDoorRecord")
@Validated
public class NightDoorRecordAppController {

    @Resource
    private NightDoorRecordService nightDoorRecordService;


    @GetMapping("/list")
    @ApiOperation(value = "获得夜间开门列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomId", value = "监室编号")
    })
    public CommonResult<List<NightDoorRecordRespVO>> list(@RequestParam("orgCode") String orgCode,
                                                          @RequestParam("roomId") String roomId) {
        Date now = new Date();
        int nowHour = DateUtil.hour(now, true);
        if (nowHour > 8 && nowHour < 20) {
            return success(new ArrayList<>(), "未到夜间开门时间");
        }
        if (nowHour < 8) {
            now = DateUtil.offsetDay(now, -1);
        }
        now = DateUtil.beginOfDay(now);

        List<NightDoorRecordDO> list = nightDoorRecordService.list(new LambdaQueryWrapper<NightDoorRecordDO>()
                .eq(NightDoorRecordDO::getOrgCode, orgCode)
                .eq(NightDoorRecordDO::getRoomId, roomId)
                .eq(NightDoorRecordDO::getApplyOpenDoorDate, now)
                .isNull(NightDoorRecordDO::getActualOpenTime)
                .orderByDesc(NightDoorRecordDO::getApplyOpenDoorDate));
        return success(BeanUtils.toBean(list, NightDoorRecordRespVO.class));
    }

    @GetMapping("/openDoor")
    @ApiOperation(value = "开门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "记录id"),
            @ApiImplicitParam(name = "openTime", value = "开门时间"),
            @ApiImplicitParam(name = "closeTime", value = "关门时间")
    })
    public CommonResult<String> appCreatePatrolRecord(@RequestParam("id") String id,
                                                      @RequestParam("openTime") Date openTime,
                                                      @RequestParam(name = "closeTime", required = false) Date closeTime){
        nightDoorRecordService.update(new LambdaUpdateWrapper<NightDoorRecordDO>()
                .eq(NightDoorRecordDO::getId, id)
                .set(NightDoorRecordDO::getActualOpenTime, openTime)
                .set(NightDoorRecordDO::getActualCloseTime, closeTime));
        return success();
    }

}
