package com.rs.module.acp.listener.admin.area;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.StringUtils;
import com.rs.module.base.entity.pm.AreaData;
import com.rs.module.base.enums.AreaTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class AreaDataImportListener implements ReadListener<AreaData> {
    private final List<AreaData> cachedDataList;

    public AreaDataImportListener(List<AreaData> cachedDataList) {
        this.cachedDataList = cachedDataList;
    }

    @Override
    public void invoke(AreaData data, AnalysisContext context) {
        //校验每一行数据的合规性
        int index = context.readRowHolder().getRowIndex() + 1;
        if (data.getAreaName() == null || data.getAreaName().equals("")) {
            throw new RuntimeException("区域名称不能为空（第"+index+"行）");
        }

        if (data.getAreaType() == null || data.getAreaType().equals("")) {
            throw new RuntimeException("区域类型ID不能为空（第"+index+"行）");
        }
        //除了监所都要有 父级节点
        if (StrUtil.isBlank(  data.getParentId() ) && !AreaTypeEnum.DETENTION_FACILITY.getName().equals(data.getAreaType())) {
            throw new RuntimeException("父节点ID不能为空（第"+index+"行）");
        }

//        if (data.getAreaCode() == null || data.getAreaCode().equals("")) {
//            throw new RuntimeException("区域编码不能为空（第"+index+"行）");
//        }
        if(data.getAreaCode() != null && data.getAreaCode().equalsIgnoreCase(AreaTypeEnum.DETENTION_ROOM.getName())){
            //如果为监室类型，则需要判断监室的字段必填的是否满足
            if (data.getRoomArea() == null || data.getRoomArea().equals("")) {
                throw new RuntimeException("监室面积不能为空（第"+index+"行）");
            }
            if (data.getSelfAreaId() == null || data.getSelfAreaId().equals("")) {
                throw new RuntimeException("上游区域ID不能为空（第"+index+"行）");
            }
            if (data.getStatus() == null || data.getStatus().equals("")) {
                throw new RuntimeException("状态不能为空（第"+index+"行）");
            }

        }
        cachedDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
//        System.out.println("导入的区域数据数量: " + cachedDataList.size());
        // 后续处理逻辑
//        for (AreaData area : cachedDataList) {
//            System.out.println(area); // 如果用了 @Data，可打印对象内容
//        }
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        System.out.println("==== 表头原始内容（第一行） ====");
        headMap.forEach((key, value) -> {
            System.out.println("表头索引: " + key + ", 表头名称: " + value.getStringValue());
        });

        // 期待的字段头（第二行）
        List<String> expectedHeaders = Arrays.asList(
                "区域名称*", "父节点ID*", "区域类型ID*", "区域编码*", "状态*",
                "关押量", "监室类型", "性别类型", "设计关押量", "监室面积*",
                "人均铺位面积", "上游区域ID*"
        );

        // 从 context 中获取实际映射到 Java 字段的字段名（也就是第二行）
        List<String> actualHeaders = new ArrayList<>();

        Map<Integer, Head> headMapFromContext = context.readSheetHolder()
                .getExcelReadHeadProperty().getHeadMap();

        headMapFromContext.forEach((index, head) -> {
            List<String> headNames = head.getHeadNameList(); // 多层表头
            if (headNames != null && !headNames.isEmpty()) {
                String lastName = headNames.get(headNames.size() - 1); // 获取最后一层（第2行）
                if (!StringUtils.isEmpty(lastName)) {
                    actualHeaders.add(lastName);
                }
            }
        });

        // 打印调试信息
        System.out.println("==== 实际字段名（第二行） ====");
        actualHeaders.forEach(System.out::println);

        // 校验字段
        for (String expected : expectedHeaders) {
            if (!actualHeaders.contains(expected)) {
                //后续可扩展是否要校验excel 表头字段
//                throw new RuntimeException("缺少必需的表头字段: " + expected);
            }
        }
    }
}
