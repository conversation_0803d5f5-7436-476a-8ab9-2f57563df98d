package com.rs.module.acp.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-仓内外屏人脸信息维护列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CnpFaceListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员类型 1警员、2在押人员")
    private Integer personnelType;

    @ApiModelProperty("人员编号、警员id")
    private String personnelCode;

    @ApiModelProperty("照片")
    private String photo;

}
