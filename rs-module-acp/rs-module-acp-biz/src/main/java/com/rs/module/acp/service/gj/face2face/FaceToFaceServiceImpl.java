package com.rs.module.acp.service.gj.face2face;

import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFacePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceSaveReqVO;
import com.rs.module.acp.job.facetoface.bo.FaceToFaceBO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.FaceToFaceDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.FaceToFaceDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-面对面管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FaceToFaceServiceImpl extends BaseServiceImpl<FaceToFaceDao, FaceToFaceDO> implements FaceToFaceService {

    @Resource
    private FaceToFaceDao faceToFaceDao;

    @Override
    public String createFaceToFace(FaceToFaceSaveReqVO createReqVO) {
        // 插入
        FaceToFaceDO faceToFace = BeanUtils.toBean(createReqVO, FaceToFaceDO.class);
        faceToFaceDao.insert(faceToFace);
        // 返回
        return faceToFace.getId();
    }

    @Override
    public void updateFaceToFace(FaceToFaceSaveReqVO updateReqVO) {
        // 校验存在
        validateFaceToFaceExists(updateReqVO.getId());
        // 更新
        FaceToFaceDO updateObj = BeanUtils.toBean(updateReqVO, FaceToFaceDO.class);
        faceToFaceDao.updateById(updateObj);
    }

    @Override
    public void deleteFaceToFace(String id) {
        // 校验存在
        validateFaceToFaceExists(id);
        // 删除
        faceToFaceDao.deleteById(id);
    }

    private void validateFaceToFaceExists(String id) {
        if (faceToFaceDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-面对面管理数据不存在");
        }
    }

    @Override
    public FaceToFaceDO getFaceToFace(String id) {
        return faceToFaceDao.selectById(id);
    }

    @Override
    public PageResult<FaceToFaceDO> getFaceToFacePage(FaceToFacePageReqVO pageReqVO) {
        return faceToFaceDao.selectPage(pageReqVO);
    }

    @Override
    public List<FaceToFaceDO> getFaceToFaceList(FaceToFaceListReqVO listReqVO) {
        return faceToFaceDao.selectList(listReqVO);
    }

    @Override
    public List<FaceToFaceBO> getNeedRemindRoomId(List<String> excludeListOrg) {
        return faceToFaceDao.getNeedRemindRoomId(excludeListOrg);
    }


}
