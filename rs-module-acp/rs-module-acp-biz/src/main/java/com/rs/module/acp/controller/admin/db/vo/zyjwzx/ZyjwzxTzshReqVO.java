package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxTzshReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("是否组织听证（1、是；2、否）")
    private String sfzztz;

    @ApiModelProperty("听证日期")
    private Date tzrq;

    @ApiModelProperty("听证情况")
    private String tzqk;

    @ApiModelProperty("听证审核备注")
    private String tzshbz;

    @ApiModelProperty("听证审核材料")
    private String tzshcl;

}
