package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.FwdbxtSaveReqVO;
import com.rs.module.acp.entity.gj.FwdbxtDO;
import com.rs.module.acp.entity.gj.FwdbxtWsxxDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-管教业务-法务待办协同 Service 接口
 *
 * <AUTHOR>
 */
public interface FwdbxtService extends IBaseService<FwdbxtDO>{

    /**
     * 创建实战平台-管教业务-法务待办协同
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFwdbxt(@Valid FwdbxtSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-法务待办协同
     *
     * @param updateReqVO 更新信息
     */
    void updateFwdbxt(@Valid FwdbxtSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-法务待办协同
     *
     * @param id 编号
     */
    void deleteFwdbxt(String id);

    /**
     * 获得实战平台-管教业务-法务待办协同
     *
     * @param id 编号
     * @return 实战平台-管教业务-法务待办协同
     */
    FwdbxtDO getFwdbxt(String id);


    // ==================== 子表（实战平台-管教业务-法务待办协同关联文书信息） ====================

    /**
     * 获得实战平台-管教业务-法务待办协同关联文书信息列表
     *
     * @param fwdbxtId 法务待办协同ID
     * @return 实战平台-管教业务-法务待办协同关联文书信息列表
     */
    List<FwdbxtWsxxDO> getFwdbxtWsxxListByFwdbxtId(String fwdbxtId);

}
