package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxDjReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("申请人类型（1、罪犯本人；2、亲属或者监护人；3、看守所管教民警或者驻看守所医生；4、驻所检察室）")
    @NotEmpty(message = "申请人类型（1、罪犯本人；2、亲属或者监护人；3、看守所管教民警或者驻看守所医生；4、驻所检察室）不能为空")
    private String sqrlx;

    @ApiModelProperty("申请原因（1．患有属于《暂予监外执行规定》所附《保外就医严重疾病范围》的严重疾病，需要保外就医；2．怀孕或者正在哺乳自己婴儿的妇女；3．生活不能自理。）")
    @NotEmpty(message = "申请原因（1．患有属于《暂予监外执行规定》所附《保外就医严重疾病范围》的严重疾病，需要保外就医；2．怀孕或者正在哺乳自己婴儿的妇女；3．生活不能自理。）不能为空")
    private String sqyy;

    @ApiModelProperty("是否从严审批（1、是；2、否）")
    @NotEmpty(message = "是否从严审批（1、是；2、否）不能为空")
    private String sfcysp;

    @ApiModelProperty("从严审批原因")
    private String cyspyy;

    @ApiModelProperty("联系人")
    @NotEmpty(message = "联系人不能为空")
    private String lxr;

    @ApiModelProperty("联系方式")
    @NotEmpty(message = "联系方式不能为空")
    private String lxfs;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("附件材料")
    private String fjcl;


}
