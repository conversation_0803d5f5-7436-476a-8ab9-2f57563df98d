package com.rs.module.acp.service.gj.xzfyssjl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlUpdateReqVO;
import com.rs.module.acp.enums.gj.XzfyssjlStatusEnum;
import com.rs.module.acp.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.XzfyssjlDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.XzfyssjlDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-行政复议讼诉记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class XzfyssjlServiceImpl extends BaseServiceImpl<XzfyssjlDao, XzfyssjlDO> implements XzfyssjlService {

    @Resource
    private XzfyssjlDao xzfyssjlDao;

    @Override
    public String createXzfyssjl(XzfyssjlSaveReqVO createReqVO) {
        // 插入
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        XzfyssjlDO xzfyssjl = BeanUtils.toBean(createReqVO, XzfyssjlDO.class);
        xzfyssjl.setStatus(XzfyssjlStatusEnum.DZD.getCode());
        xzfyssjl.setHandlePoliceSfzh(sessionUser.getIdCard());
        xzfyssjl.setHandlePoliceXm(sessionUser.getName());
        xzfyssjl.setHandlePoliceTime(new Date());
        xzfyssjlDao.insert(xzfyssjl);
        // 返回
        return xzfyssjl.getId();
    }

    @Override
    public void updateXzfyssjl(XzfyssjlUpdateReqVO updateReqVO) {
        // 校验存在
        validateXzfyssjlExists(updateReqVO.getId());
        // 更新
        XzfyssjlDO updateObj = BeanUtils.toBean(updateReqVO, XzfyssjlDO.class);
        xzfyssjlDao.updateById(updateObj);
    }

    @Override
    public void deleteXzfyssjl(String id) {
        // 校验存在
        validateXzfyssjlExists(id);
        // 删除
        xzfyssjlDao.deleteById(id);
    }

    private void validateXzfyssjlExists(String id) {
        if (xzfyssjlDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-行政复议讼诉记录数据不存在");
        }
    }

    @Override
    public XzfyssjlDO getXzfyssjl(String id) {
        return xzfyssjlDao.selectById(id);
    }

    @Override
    public PageResult<XzfyssjlDO> getXzfyssjlPage(XzfyssjlPageReqVO pageReqVO) {
        return xzfyssjlDao.selectPage(pageReqVO);
    }

    @Override
    public List<XzfyssjlDO> getXzfyssjlList(XzfyssjlListReqVO listReqVO) {
        return xzfyssjlDao.selectList(listReqVO);
    }

    @Override
    public PageResult<XzfyssjlDO> getAppXzfyssjlPage(int pageNo, int pageSize, String jgrybm, String type) {
        Page<XzfyssjlDO> page = new Page<>(pageNo, pageSize);
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        Page<XzfyssjlDO> result = xzfyssjlDao.getAppXzfyssjlPage(page, jgrybm,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

    @Override
    public void forward(String id) {
        XzfyssjlDO xzfyssjlDO = xzfyssjlDao.selectById(id);
        if (xzfyssjlDO == null) {
            throw new ServerException("实战平台-管教业务-行政复议讼诉记录数据不存在");
        }
        if(!XzfyssjlStatusEnum.DZD.getCode().equals(xzfyssjlDO.getStatus())){
            throw new ServerException("非待转递状态，不能进行转递");
        }
        xzfyssjlDO.setStatus(XzfyssjlStatusEnum.YZD.getCode());
        xzfyssjlDO.setForwardTime(new Date());
        updateById(xzfyssjlDO);
    }


}
