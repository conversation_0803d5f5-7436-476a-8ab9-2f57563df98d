package com.rs.module.acp.service.pi;

import com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor.FixedScreenMonitorPrisonerSaveReqVO;
import com.rs.module.acp.entity.pi.FixedScreenMonitorDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rs.module.acp.entity.pi.FixedScreenMonitorPrisonerDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pi.FixedScreenMonitorPrisonerDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;

import java.util.ArrayList;
import java.util.List;


/**
 * 实战平台-巡视管控-定屏监控与被监管人员关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FixedScreenMonitorPrisonerServiceImpl extends BaseServiceImpl<FixedScreenMonitorPrisonerDao, FixedScreenMonitorPrisonerDO> implements FixedScreenMonitorPrisonerService {

    @Resource
    private FixedScreenMonitorPrisonerDao fixedScreenMonitorPrisonerDao;

    @Override
    public String createFixedScreenMonitorPrisoner(FixedScreenMonitorPrisonerSaveReqVO createReqVO) {
        // 插入
        FixedScreenMonitorPrisonerDO fixedScreenMonitorPrisoner = BeanUtils.toBean(createReqVO, FixedScreenMonitorPrisonerDO.class);
        fixedScreenMonitorPrisonerDao.insert(fixedScreenMonitorPrisoner);
        // 返回
        return fixedScreenMonitorPrisoner.getId();
    }

    @Override
    public void updateFixedScreenMonitorPrisoner(FixedScreenMonitorPrisonerSaveReqVO updateReqVO) {
        // 校验存在
        validateFixedScreenMonitorPrisonerExists(updateReqVO.getId());
        // 更新
        FixedScreenMonitorPrisonerDO updateObj = BeanUtils.toBean(updateReqVO, FixedScreenMonitorPrisonerDO.class);
        fixedScreenMonitorPrisonerDao.updateById(updateObj);
    }

    @Override
    public void deleteFixedScreenMonitorPrisoner(String id) {
        // 校验存在
        validateFixedScreenMonitorPrisonerExists(id);
        // 删除
        fixedScreenMonitorPrisonerDao.deleteById(id);
    }

    private void validateFixedScreenMonitorPrisonerExists(String id) {
        if (fixedScreenMonitorPrisonerDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-定屏监控与被监管人员关联数据不存在");
        }
    }

    @Override
    public FixedScreenMonitorPrisonerDO getFixedScreenMonitorPrisoner(String id) {
        return fixedScreenMonitorPrisonerDao.selectById(id);
    }

    @Override
    public void batchSavePrisoner(FixedScreenMonitorDO fixedScreenMonitorDO) {
        fixedScreenMonitorPrisonerDao.deleteByFixedScreenMonitorId(fixedScreenMonitorDO.getId());
        List<FixedScreenMonitorPrisonerDO> list = new ArrayList<>();
        String jgrybm = fixedScreenMonitorDO.getJgrybm();
        String jgryxm = fixedScreenMonitorDO.getJgryxm();
        String[] jgrybmArray = jgrybm.split(",");
        String[] jgryxmArray = jgryxm.split(",");
        for (int i = 0; i < jgrybmArray.length; i++) {
            FixedScreenMonitorPrisonerDO entity = new FixedScreenMonitorPrisonerDO();
            entity.setFixedScreenMonitorId(fixedScreenMonitorDO.getId());
            entity.setJgrybm(jgrybmArray[i]);
            entity.setJgryxm(i < jgryxmArray.length ? jgryxmArray[i] : "");
            entity.setOnScreenPersonInfo(fixedScreenMonitorDO.getOnScreenPersonInfo());
            entity.setStatus("0");
            list.add(entity);
        }
        fixedScreenMonitorPrisonerDao.insertBatch(list);
    }

}
