package com.rs.module.tem.service.dch.handler;

import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.TalkTypeEnum;
import com.rs.module.base.service.dch.context.DataChangeEventContext;
import com.rs.module.base.service.dch.enums.DataChangeEventTypeEnum;
import com.rs.module.base.service.dch.handler.AbstractDataChangeEventHandler;
import com.rs.module.base.service.dch.handler.DataChangeEventHandlerResult;
import com.rs.module.tem.controller.admin.talk.vo.TalkTaskSaveReqVO;
import com.rs.module.tem.service.talk.TalkTaskService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 监室调整
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
@AllArgsConstructor
public class AfterLawyerVisitTalkHandler extends AbstractDataChangeEventHandler {

    public final TalkTaskService talkTaskService;

    @Override
    public String getSupportedBusinessType() {
        return AfterLawyerVisitTalkHandler.class.getSimpleName();
    }

    @Override
    public int getPriority() {
        return 1;
    }

    @Override
    protected List<String> getSupportedTableNames() {
        return Arrays.asList(
                "acp_wb_lawyer_meeting"
        );
    }

    @Override
    protected Set<DataChangeEventTypeEnum> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(
                DataChangeEventTypeEnum.UPDATE
        ));
    }

    @Override
    protected DataChangeEventHandlerResult doHandle(DataChangeEventContext context) {
        PrisonerVwRespVO inJgry = getInJgry(context);
        if (inJgry == null) {
            return DataChangeEventHandlerResult.success();
        }
        if ("4".equals(getStatus(context))) {
            TalkTaskSaveReqVO talkRecordSaveReqVO = new TalkTaskSaveReqVO();
            talkRecordSaveReqVO.setTalkReason(TalkTypeEnum.AFTER_LAWYER_MEETING.getCode());
            talkTaskService.createTalkTask(talkRecordSaveReqVO);
        }
        return DataChangeEventHandlerResult.success();
    }

}
