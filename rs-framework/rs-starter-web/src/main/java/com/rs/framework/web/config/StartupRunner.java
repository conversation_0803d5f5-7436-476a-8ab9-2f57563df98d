package com.rs.framework.web.config;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.rs.framework.web.core.listener.PortListener;
import com.rs.framework.web.core.vo.PathsVO;
import com.rs.framework.web.utils.AcpDbUtil;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName StartupRunner
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/23 19:09
 * @Version 1.0
 */
@Component
@Slf4j
public class StartupRunner implements ApplicationRunner {

    private String cx[] = {"查询", "获取", "获得", "详情"};
    private String xz[] = {"新增", "增加", "创建", "登记", "保存"};
    private String xg[] = {"修改", "更新"};
    private String sc[] = {"删除"};

    @Autowired
    private PortListener portListener;
    public static Map<String, PathsVO> pathsMap = new HashMap<>();

    @Autowired
    DataSource ds;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        new Thread(() -> {
            //获取启动端口
            String host = "localhost";
            String url = "http://" + host + ":" + portListener.getPort() + "/v2/api-docs?group=1.0版本";
            String swagger = HttpUtil.get(url);
            Gson gson = new Gson();
            JsonObject jsonObject = gson.fromJson(swagger, JsonObject.class);
            JsonObject pathsObject = jsonObject.getAsJsonObject("paths");
            DataSource ds = AcpDbUtil.getDataSource();
            long start = System.currentTimeMillis();
            log.info("【RESTful接口】格式化开始......");
            for (Map.Entry<String, JsonElement> entry : pathsObject.entrySet()) {
                try {
                    String path = entry.getKey();
                    JsonObject pathObject = entry.getValue().getAsJsonObject();
                    JsonObject methodObject = pathObject.getAsJsonObject(pathObject.keySet().iterator().next());
                    JsonArray tagsArray = methodObject.getAsJsonArray("tags");
                    String tag = tagsArray != null && !tagsArray.isJsonNull() ? tagsArray.get(0).getAsString() : null;
                    String summary = methodObject.get("summary").getAsString();

                    try {
                        Entity where = Entity.create();
                        where.setTableName("swagger_paths");
                        where.set("path", path);
                        Db.use(ds).del(where);

                        Entity insert = Entity.create();
                        insert.setTableName("swagger_paths");
                        insert.set("id", IdUtil.fastSimpleUUID());
                        insert.set("path", path);
                        insert.set("summary", summary);
                        if (entry.getValue() != null) {
                            insert.set("describe", entry.getValue().toString());
                        }
                        Db.use(ds).insert(insert);
                    } catch (SQLException e) {
                    	e.printStackTrace();
                    }

                    String opType = "";
                    for (String s : cx) {
                        if (summary.contains(s)) {
                            opType = "1";
                            break;
                        }
                    }
                    for (String s : xz) {
                        if (summary.contains(s)) {
                            opType = "2";
                            break;
                        }
                    }
                    for (String s : xg) {
                        if (summary.contains(s)) {
                            opType = "3";
                            break;
                        }
                    }
                    for (String s : sc) {
                        if (summary.contains(s)) {
                            opType = "4";
                            break;
                        }
                    }

                    PathsVO pathsVO = new PathsVO();
                    pathsVO.setSummary(summary);
                    pathsVO.setTag(tag);
                    pathsVO.setOperateType(opType);
                    pathsMap.put(path, pathsVO);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            log.info("【RESTful接口】接口路径-格式化完成");
            
            JsonObject definitions = jsonObject.getAsJsonObject("definitions");
            for (Map.Entry<String, JsonElement> entry : definitions.entrySet()) {
                try {
                    String name = entry.getKey();
                    Entity where = Entity.create();
                    where.setTableName("swagger_dovo");
                    where.set("original_ref", name);
                    Db.use(ds).del(where);
                    Entity insert = Entity.create();
                    insert.setTableName("swagger_dovo");
                    insert.set("id", IdUtil.fastSimpleUUID());
                    insert.set("original_ref", name);
                    if (entry.getValue() != null) {
                        insert.set("describe", entry.getValue().toString());
                    }
                    Db.use(ds).insert(insert);
                } catch (Exception e) {

                }
            }
            log.info("【RESTful接口】接口定义-格式化完成");
            log.info("【RESTful接口】格式化结束（耗时-{}毫秒）", (System.currentTimeMillis() - start));
        }).start();
    }
}
