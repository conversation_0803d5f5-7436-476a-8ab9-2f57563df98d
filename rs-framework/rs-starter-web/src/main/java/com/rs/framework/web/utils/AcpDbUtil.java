package com.rs.framework.web.utils;

import javax.sql.DataSource;

import com.rs.framework.mybatis.config.GlobalConstant;
import com.rs.framework.mybatis.util.DatasourceUtil;

import cn.hutool.db.Db;

/**
 * @ClassName UacUerUtil
 * <AUTHOR>
 * @Date 2025/3/28 14:20
 * @Version 1.0
 */
public class AcpDbUtil {

    /**
     * 获取默认数据库
     * @return Db
     */
    public static Db getDb() {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.DEFAULT_DATASOURCE_KEY);
        Db use = Db.use(ds);
        return use;
    }

    /**
     * 获取默认数据源
     * @return DataSource
     */
    public static DataSource getDataSource() {
    	return DatasourceUtil.getDataSource(GlobalConstant.DEFAULT_DATASOURCE_KEY);
    }
}
