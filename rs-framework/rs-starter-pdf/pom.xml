<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.rs</groupId>
        <artifactId>rs-framework</artifactId>
        <version>${rs.version}</version>
    </parent>

    <artifactId>rs-starter-pdf</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>监所管理-技术组件-pdf组件，自定义打印</description>

    <dependencies>

        <!-- 工具类相关 begin -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all-u</artifactId>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project>
