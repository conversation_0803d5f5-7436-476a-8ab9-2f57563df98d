package com.rs.module.pdf.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * PDF 模板枚举
 * 目前仅包含：拒绝或放弃医学治疗风险告知书
 */
@AllArgsConstructor
@Getter
public enum PdfTemplateEnum {

    JJHFYXZLFXGZS("jjhfyxzlfxgzs", "拒绝或放弃医学治疗风险告知书", "拒绝或放弃医学治疗风险告知书.docx"),
    THBL("jjhfyxzlfxgzs", "谈话笔录", "谈话笔录.docx")
    ;

    private final String code;
    private final String name;
    private final String fileName;

    public static PdfTemplateEnum fromCode(String code) {
        if (code == null) return null;
        for (PdfTemplateEnum e : values()) {
            if (e.code.equals(code)) return e;
        }
        return null;
    }
}

