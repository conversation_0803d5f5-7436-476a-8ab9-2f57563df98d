package com.rs.module.integ.dao.haikang;


import com.rs.module.integ.controller.admin.haikang.vo.HkEventSaveReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 集成服务-海康应急报警事件数据 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface HkReportEventDao {
    boolean createHkEvent(@Param("storageTable") String storageTable,@Param("eventList") List<HkEventSaveReqVO> eventList);
}
