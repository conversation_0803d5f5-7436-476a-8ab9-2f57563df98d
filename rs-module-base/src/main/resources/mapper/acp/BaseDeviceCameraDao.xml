<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.rs.module.acp.dao.pm.BaseDeviceCameraDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.rs.module.acp.entity.pm.BaseDeviceCameraDO"
               id="baseDeviceCameraMap">
        <result property="deviceId" column="device_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="deviceIp" column="device_ip"/>
    </resultMap>

    <sql id="table_name">acp_pm_device_camera</sql>

    <sql id="all_entity_columns">
	 ${prefix}.device_id,
	 ${prefix}.channel_id,
	 ${prefix}.device_ip
	 </sql>

    <sql id="page_entity_columns">
	 ${prefix}.device_id,
	 ${prefix}.channel_id,
	 ${prefix}.device_ip
	 </sql>

    <select id="findByPage" resultType="com.rs.module.acp.controller.admin.pm.vo.BaseDeviceCameraPageVO">
        SELECT
        a.device_id as deviceId,
        a.channel_id as channelId,
        a.device_ip as deviceIp,
        b.device_name as deviceName,
        b.device_type_id as deviceTypeId,
        b.area_id as areaId,
        c.area_name as areaName,
        b.prison_id as prisonId
        FROM acp_pm_device_camera a,
        acp_pm_device b,acp_pm_area c

        <where>
            a.device_id=b.id AND c.id=b.area_id
            <if test="form.deviceId != null and form.deviceId != ''">
                AND a.device_id = #{form.deviceId}
            </if>
            <if test="form.areaId != null and form.areaId != ''">
                AND b.area_id = #{form.areaId}
            </if>
            <if test="form.deviceTypeId != null and form.deviceTypeId != ''">
                AND b.device_type_id = #{form.deviceTypeId}
            </if>
            <if test="form.deviceName != null and form.deviceName != ''">
                AND b.device_name like concat('%',#{form.deviceName},'%')
            </if>
            <if test="form.prisonId != null and form.prisonId != ''">
                AND b.prison_id = #{form.prisonId}
            </if>
        </where>

    </select>

    <select id="findOneById" resultType="com.rs.module.acp.controller.admin.pm.vo.BaseDeviceCameraPageVO">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>,
        b.device_name
        FROM
        <include refid="table_name"/>
        a inner join acp_pm_device b on a.device_id = b.id
        where a.device_id = #{id}
    </select>

    <select id="roomVideo" resultType="com.rs.module.base.vo.RoomVideoVO">
        select a.id device_id, a.device_code ,a.device_name ,c.channel_id, c.device_ip,a.gb_code
        from acp_pm_device a
                 inner join acp_pm_device_camera c on a.id = c.device_id
        where a.is_del = 0 and a.area_id  = #{roomId}
        order by a.id asc
    </select>

</mapper>