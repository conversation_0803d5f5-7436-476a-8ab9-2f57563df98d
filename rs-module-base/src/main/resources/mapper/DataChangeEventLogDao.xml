<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.rs.module.base.dao.pm.DataChangeEventLogDao">

    <resultMap id="BaseResultMap" type="com.rs.module.base.entity.pm.DataChangeEventLogDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="event_type" property="eventType" jdbcType="VARCHAR"/>
        <result column="table_name" property="tableName" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="primary_key_id" property="primaryKeyId" jdbcType="VARCHAR"/>
        <result column="old_data" property="oldData" jdbcType="VARCHAR"/>
        <result column="new_data" property="newData" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="handler_class" property="handlerClass" jdbcType="VARCHAR"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="max_retry_count" property="maxRetryCount" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="event_time" property="eventTime" jdbcType="TIMESTAMP"/>
        <result column="process_start_time" property="processStartTime" jdbcType="TIMESTAMP"/>
        <result column="process_end_time" property="processEndTime" jdbcType="TIMESTAMP"/>
        <result column="next_retry_time" property="nextRetryTime" jdbcType="TIMESTAMP"/>
        <result column="extend_info" property="extendInfo" jdbcType="VARCHAR"/>
        <result column="is_del" property="isDel" jdbcType="TINYINT"/>
        <result column="add_user" property="addUser" jdbcType="VARCHAR"/>
        <result column="add_user_name" property="addUserName" jdbcType="VARCHAR"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, event_type, table_name, business_type, primary_key_id, old_data, new_data,
        status, handler_class, retry_count, max_retry_count, error_message, event_time,
        process_start_time, process_end_time, next_retry_time, extend_info
    </sql>

    <!-- 查询待处理的事件 -->
    <select id="queryPendingEvents" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM acp_pm_data_change_event_log
        WHERE is_del = 0
          AND status = 'PENDING'
          AND (expectation_execute_date IS NULL OR expectation_execute_date &lt; (CURRENT_DATE + INTERVAL '1 day'))
        ORDER BY event_time ASC
        LIMIT #{limit}
    </select>

    <!-- 查询需要重试的事件 -->
    <select id="queryRetryEvents" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM acp_pm_data_change_event_log
        WHERE is_del = 0
          AND status = 'FAILED'
          AND retry_count &lt; max_retry_count
          AND (next_retry_time IS NULL OR next_retry_time &lt;= #{currentTime})
        ORDER BY next_retry_time ASC, event_time ASC
        LIMIT #{limit}
    </select>

    <!-- 根据状态查询事件 -->
    <select id="queryEventsByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM acp_pm_data_change_event_log
        WHERE is_del = 0 AND status = #{status}
        ORDER BY event_time ASC
        LIMIT #{limit}
    </select>

    <!-- 根据业务类型查询事件 -->
    <select id="queryEventsByBusinessType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM acp_pm_data_change_event_log
        WHERE is_del = 0
          AND business_type = #{businessType}
        <if test="status != null">
          AND status = #{status}
        </if>
        ORDER BY event_time ASC
        LIMIT #{limit}
    </select>

    <!-- 更新事件状态 -->
    <update id="updateEventStatus">
        UPDATE acp_pm_data_change_event_log
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id} AND is_del = 0
    </update>

    <!-- 更新事件处理信息 -->
    <update id="updateEventProcessInfo">
        UPDATE acp_pm_data_change_event_log
        SET status = #{status},
            process_start_time = #{processStartTime},
            process_end_time = #{processEndTime},
            error_message = #{errorMessage},
            retry_count = #{retryCount},
            next_retry_time = #{nextRetryTime},
            update_time = NOW()
        WHERE id = #{id} AND is_del = 0
    </update>

    <!-- 删除过期的成功事件 -->
    <delete id="deleteExpiredSuccessEvents">
        UPDATE acp_pm_data_change_event_log
        SET is_del = 1, update_time = NOW()
        WHERE is_del = 0
          AND status = 'SUCCESS'
          AND process_end_time &lt; #{expireTime}
    </delete>

    <!-- 统计各状态的事件数量 -->
    <select id="countEventsByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM acp_pm_data_change_event_log
        WHERE is_del = 0
        GROUP BY status
    </select>

    <update id="updateEventDealClass">
        UPDATE acp_pm_data_change_event_log
        SET handler_class = #{className}, update_time = NOW()
        WHERE id = #{id} AND is_del = 0
    </update>
</mapper>
