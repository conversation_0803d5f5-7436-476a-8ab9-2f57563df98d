package com.rs.module.acp.controller.admin.pm;

import com.rs.module.base.service.dch.job.DataChangeEventJob;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "数据变更任务")
@RestController
@RequestMapping("/acp/pm/datachange")
@Validated
public class DataChangeJobController {

    @Autowired
    DataChangeEventJob dataChangeEventJob;


    @GetMapping("/test")
    public void test() {
        dataChangeEventJob.processPendingEvents();
    }


}
