package com.rs.module.acp.controller.admin.pm;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountPageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountRespVO;
import com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountSaveReqVO;
import com.rs.module.acp.entity.pm.TerminalVirtualAccountDO;
import com.rs.module.acp.service.pm.TerminalVirtualAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-虚拟终端账号")
@RestController
@RequestMapping("/acp/pm/terminalVirtualAccount")
@Validated
public class TerminalVirtualAccountController {

    @Resource
    private TerminalVirtualAccountService terminalVirtualAccountService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-虚拟终端账号")
    public CommonResult<String> createTerminalVirtualAccount(@Valid @RequestBody TerminalVirtualAccountSaveReqVO createReqVO) {
        return success(terminalVirtualAccountService.createTerminalVirtualAccount(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-虚拟终端账号")
    public CommonResult<Boolean> updateTerminalVirtualAccount(@Valid @RequestBody TerminalVirtualAccountSaveReqVO updateReqVO) {
        terminalVirtualAccountService.updateTerminalVirtualAccount(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-虚拟终端账号")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteTerminalVirtualAccount(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            terminalVirtualAccountService.deleteTerminalVirtualAccount(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-虚拟终端账号")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TerminalVirtualAccountRespVO> getTerminalVirtualAccount(@RequestParam("id") String id) {
        TerminalVirtualAccountDO terminalVirtualAccount = terminalVirtualAccountService.getTerminalVirtualAccount(id);
        return success(BeanUtils.toBean(terminalVirtualAccount, TerminalVirtualAccountRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-虚拟终端账号分页")
    public CommonResult<PageResult<TerminalVirtualAccountRespVO>> getTerminalVirtualAccountPage(@Valid @RequestBody TerminalVirtualAccountPageReqVO pageReqVO) {
        PageResult<TerminalVirtualAccountDO> pageResult = terminalVirtualAccountService.getTerminalVirtualAccountPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TerminalVirtualAccountRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-虚拟终端账号列表")
    public CommonResult<List<TerminalVirtualAccountRespVO>> getTerminalVirtualAccountList(@Valid @RequestBody TerminalVirtualAccountListReqVO listReqVO) {
        List<TerminalVirtualAccountDO> list = terminalVirtualAccountService.getTerminalVirtualAccountList(listReqVO);
        return success(BeanUtils.toBean(list, TerminalVirtualAccountRespVO.class));
    }


    @GetMapping("/getAvailableAccount")
    @ApiOperation(value = "获得实战平台-监管管理-空闲虚拟终端账号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessId", value = "业务id"),
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编号")
    })
    public CommonResult<TerminalVirtualAccountRespVO> getAvailableAccount(@RequestParam("businessId") String businessId, @RequestParam("jgrybm") String jgrybm) {
        return success(terminalVirtualAccountService.getAvailableAccount(businessId, jgrybm));
    }

}
