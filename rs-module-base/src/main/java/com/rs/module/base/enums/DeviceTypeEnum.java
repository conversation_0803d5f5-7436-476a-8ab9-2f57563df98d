package com.rs.module.base.enums;

/**
 * 设备类型
 */
public enum DeviceTypeEnum {

    CAMERA("0001",	"摄像机"),
    FACE_CAMERA("0002",	"人脸摄像头"),
    CARD_SWIPING_ACCESS_CONTROL("0003",	"刷卡门禁"),
    FACE_ACCESS_CONTROL("0004",	"人脸门禁"),
    INFRARED_BEAM("0005",	"红外对射"),
    EMERGENCY_ALARM("0006",	"应急报警"),
    VOICE_INTERCOM("0007",	"语音对讲"),
    INDOOR_TERMINAL("0008",	"室内终端（仓内屏）"),
    AUDIO_VISUAL_EDUCATION("0009",	"电化教育"),
    POLICE_TERMINAL("0010",	"警务终端"),
    LAW_ENFORCEMENT_RECORDER("0011",	"执法记录仪"),
    ELECTRONIC_ANKLE_BRACELET("0012",	"电子脚镣"),
    MECHANICAL_INTERCOM("0013",	"机械对讲"),
    PATROL_POINT("0014",	"巡更点"),
    SCREEN_OUTSIDE_THE_WAREHOUSE("0015",	"仓外屏"),
    OTHER("0000",	"其他"),
    VIDEO_ANOMALY("0016",	"视频异动"),
    DEVICE_TYPE_WLRYZD("0020","外来人员终端"),
    DEVICE_TYPE_ZJHJZD("0021","自助会见终端"),
    INTERCOM_HOST("0029",	"对讲主机"),
    INTERCOM_EXTENSION("0022",	"对讲分机"),
    ANTI_MISOPERATION("0026","防误放终端");

    private final String code;
    private final String name;

    DeviceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static DeviceTypeEnum getByCode(String code) {
        for (DeviceTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据名称获取code
     * @param name 区域类型名称
     * @return 对应的code，如果找不到则返回null
     */
    public static String getCodeByName(String name) {
        for (DeviceTypeEnum type : values()) {
            if (type.name.equals(name)) {
                return type.code;
            }
        }
        return null;
    }

} 