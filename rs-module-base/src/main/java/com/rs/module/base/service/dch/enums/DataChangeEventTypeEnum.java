package com.rs.module.base.service.dch.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * 数据变更事件类型枚举
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
public enum DataChangeEventTypeEnum implements IEnum<String> {

    /**
     * 插入操作
     */
    INSERT("INSERT", "插入"),

    /**
     * 更新操作
     */
    UPDATE("UPDATE", "更新"),

    /**
     * 删除操作
     */
    DELETE("DELETE", "删除");

    private final String value;
    private final String description;

    DataChangeEventTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static DataChangeEventTypeEnum getByValue(String value) {
        for (DataChangeEventTypeEnum eventType : values()) {
            if (eventType.getValue().equals(value)) {
                return eventType;
            }
        }
        return null;
    }
}
