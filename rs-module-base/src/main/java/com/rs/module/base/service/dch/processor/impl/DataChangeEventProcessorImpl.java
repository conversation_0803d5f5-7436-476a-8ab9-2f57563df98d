package com.rs.module.base.service.dch.processor.impl;

import com.alibaba.fastjson.JSON;
import com.rs.module.base.entity.pm.DataChangeEventLogDO;
import com.rs.module.base.service.dch.DataChangeEventLogService;
import com.rs.module.base.service.dch.context.DataChangeEventContext;
import com.rs.module.base.service.dch.enums.DataChangeEventStatusEnum;
import com.rs.module.base.service.dch.enums.DataChangeEventTypeEnum;
import com.rs.module.base.service.dch.factory.DataChangeEventHandlerFactory;
import com.rs.module.base.service.dch.handler.DataChangeEventHandler;
import com.rs.module.base.service.dch.handler.DataChangeEventHandlerResult;
import com.rs.module.base.service.dch.processor.DataChangeEventProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据变更事件处理器实现
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Service
public class DataChangeEventProcessorImpl implements DataChangeEventProcessor {

    @Autowired
    private DataChangeEventLogService dataChangeEventLogService;

    @Autowired
    private DataChangeEventHandlerFactory handlerFactory;

    @Override
    public void processDataChangeEvent(DataChangeEventTypeEnum eventType, String tableName,
                                       String businessType, String primaryKeyId,
                                       Map<String, Object> oldDataMap, Map<String, Object> newDataMap) {
        DataChangeEventContext context = new DataChangeEventContext(eventType, tableName, businessType, primaryKeyId);
        context.setOldDataMap(oldDataMap);
        context.setNewDataMap(newDataMap);
        processDataChangeEvent(context);
    }

    @Override
    public void processDataChangeEvent(DataChangeEventContext context) {
        try {
            String eventId = null;
            // 创建事件日志
            if (context.getEventId() == null) {
                eventId = dataChangeEventLogService.createEventLog(context);
                context.setEventId(eventId);
            } else {
                eventId = context.getEventId();
            }

            // 查找处理器
            List<DataChangeEventHandler> handlers = handlerFactory.getHandlers(context);
            if (handlers.isEmpty()) {
                log.warn("未找到支持的处理器: businessType={}, tableName={}, eventType={}",
                        context.getBusinessType(), context.getTableName(), context.getEventType());
//                dataChangeEventLogService.updateEventStatus(eventId, DataChangeEventStatusEnum.SKIPPED);
                return;
            } else {
                //获取每个处理器的名字，逗号分割
                String handlerNames = handlers.stream().map(handler -> handler.getClass().getSimpleName())
                        .collect(Collectors.joining(","));
                dataChangeEventLogService.updateEventDealClass(eventId, handlerNames);
            }

            // 更新状态为处理中
            Date processStartTime = new Date();
            dataChangeEventLogService.updateEventStatus(eventId, DataChangeEventStatusEnum.PROCESSING);

            boolean allSuccess = true;
            StringBuilder errorMessages = new StringBuilder();

            // 执行所有支持的处理器
            for (DataChangeEventHandler handler : handlers) {
                try {
                    log.debug("执行处理器: {} -> {}", handler.getClass().getSimpleName(), context.getBusinessType());

                    DataChangeEventHandlerResult result = handler.handle(context);

                    if (!result.isSuccess()) {
                        allSuccess = false;
                        if (errorMessages.length() > 0) {
                            errorMessages.append("; ");
                        }
                        errorMessages.append(handler.getClass().getSimpleName())
                                .append(": ").append(result.getMessage());
                    }
                } catch (Exception e) {
                    allSuccess = false;
                    log.error("处理器执行异常: {}", handler.getClass().getSimpleName(), e);
                    if (errorMessages.length() > 0) {
                        errorMessages.append("; ");
                    }
                    errorMessages.append(handler.getClass().getSimpleName())
                            .append(": ").append(e.getMessage());
                }
            }

            // 更新处理结果
            Date processEndTime = new Date();
            if (allSuccess) {
                dataChangeEventLogService.updateEventProcessInfo(eventId, DataChangeEventStatusEnum.SUCCESS,
                        processStartTime, processEndTime, null, context.getRetryCount(), null);
                log.debug("数据变更事件处理成功: eventId={}", eventId);
            } else {
                // 计算下次重试时间（指数退避）
                Date nextRetryTime = calculateNextRetryTime(context.getRetryCount());

                dataChangeEventLogService.updateEventProcessInfo(eventId, DataChangeEventStatusEnum.FAILED,
                        processStartTime, processEndTime, errorMessages.toString(),
                        context.getRetryCount(), nextRetryTime);
                log.warn("数据变更事件处理失败: eventId={}, errors={}", eventId, errorMessages.toString());
            }

        } catch (Exception e) {
            log.error("处理数据变更事件异常: businessType={}, tableName={}, eventType={}",
                    context.getBusinessType(), context.getTableName(), context.getEventType(), e);

            if (context.getEventId() != null) {
                dataChangeEventLogService.updateEventProcessInfo(context.getEventId(), DataChangeEventStatusEnum.FAILED,
                        new Date(), new Date(), "处理异常: " + e.getMessage(),
                        context.getRetryCount(), calculateNextRetryTime(context.getRetryCount()));
                //出错设置出错状态
                dataChangeEventLogService.updateEventStatus(context.getEventId(), DataChangeEventStatusEnum.FAILED);
            }
        }
    }

    @Async
    @Override
    public void processDataChangeEventAsync(DataChangeEventContext context) {
        processDataChangeEvent(context);
    }

    @Override
    public int processPendingEvents(int limit) {
        List<DataChangeEventLogDO> pendingEvents = dataChangeEventLogService.queryPendingEvents(limit);

        int processedCount = 0;
        for (DataChangeEventLogDO eventLog : pendingEvents) {
            try {
                DataChangeEventContext context = convertToContext(eventLog);
                processDataChangeEvent(context);
                processedCount++;
            } catch (Exception e) {
                log.error("处理待处理事件异常: eventId={}", eventLog.getId(), e);
            }
        }

        return processedCount;
    }

    @Override
    public int processRetryEvents(int limit) {
        List<DataChangeEventLogDO> retryEvents = dataChangeEventLogService.queryRetryEvents(limit);

        int processedCount = 0;
        for (DataChangeEventLogDO eventLog : retryEvents) {
            try {
                DataChangeEventContext context = convertToContext(eventLog);
                context.incrementRetryCount();
                processDataChangeEvent(context);
                processedCount++;
            } catch (Exception e) {
                log.error("处理重试事件异常: eventId={}", eventLog.getId(), e);
            }
        }

        return processedCount;
    }

    @Override
    public int processEventsByBusinessType(String businessType, int limit) {
        List<DataChangeEventLogDO> events = dataChangeEventLogService.queryEventsByBusinessType(
                businessType, DataChangeEventStatusEnum.PENDING, limit);

        int processedCount = 0;
        for (DataChangeEventLogDO eventLog : events) {
            try {
                DataChangeEventContext context = convertToContext(eventLog);
                processDataChangeEvent(context);
                processedCount++;
            } catch (Exception e) {
                log.error("处理业务类型事件异常: eventId={}, businessType={}", eventLog.getId(), businessType, e);
            }
        }

        return processedCount;
    }

    /**
     * 将事件日志转换为事件上下文
     */
    private DataChangeEventContext convertToContext(DataChangeEventLogDO eventLog) {
        DataChangeEventContext context = new DataChangeEventContext();
        context.setEventId(eventLog.getId());
        context.setEventType(eventLog.getEventType());
        context.setTableName(eventLog.getTableName());
        context.setBusinessType(eventLog.getBusinessType());
        context.setPrimaryKeyId(eventLog.getPrimaryKeyId());
        context.setEventTime(eventLog.getEventTime());
        context.setRetryCount(eventLog.getRetryCount());
        context.setMaxRetryCount(eventLog.getMaxRetryCount());
        context.setRawData(eventLog);

        if (eventLog.getOldData() != null) {
            context.setOldDataMap(JSON.parseObject(eventLog.getOldData(), Map.class));
        }

        if (eventLog.getNewData() != null) {
            context.setNewDataMap(JSON.parseObject(eventLog.getNewData(), Map.class));
        }

        if (eventLog.getExtendInfo() != null) {
            context.setExtendInfo(JSON.parseObject(eventLog.getExtendInfo(), Map.class));
        }

        return context;
    }

    /**
     * 计算下次重试时间（指数退避）
     */
    private Date calculateNextRetryTime(int retryCount) {
        // 基础延迟时间：1分钟
        long baseDelayMs = 60 * 1000L;
        // 指数退避：2^retryCount * baseDelayMs
        long delayMs = (long) (Math.pow(2, retryCount) * baseDelayMs);
        // 最大延迟时间：1小时
        long maxDelayMs = 60 * 60 * 1000L;

        delayMs = Math.min(delayMs, maxDelayMs);

        return new Date(System.currentTimeMillis() + delayMs);
    }
}
