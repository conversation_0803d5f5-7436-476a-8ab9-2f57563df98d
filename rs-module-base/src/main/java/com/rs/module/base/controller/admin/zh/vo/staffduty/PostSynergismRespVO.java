package com.rs.module.base.controller.admin.zh.vo.staffduty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-值班管理-岗位协同 Response VO")
@Data
public class PostSynergismRespVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("值班岗位ID")
    private String postId;

    @ApiModelProperty("值班岗位名称")
    private String postName;

    @ApiModelProperty("值班日期")
    private String dutyDate;
    @ApiModelProperty("选中状态 0 否,1 是")
    private Integer selectStatus = 0;
    @ApiModelProperty("值班人员集合")
    private List<StaffDutyRecordPersoRespVO> personList;
}
