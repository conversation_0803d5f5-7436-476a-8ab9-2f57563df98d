package com.rs.module.base.service.dch.impl;

import com.alibaba.fastjson.JSON;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.dao.pm.DataChangeEventLogDao;
import com.rs.module.base.entity.pm.DataChangeEventLogDO;
import com.rs.module.base.service.dch.DataChangeEventLogService;
import com.rs.module.base.service.dch.context.DataChangeEventContext;
import com.rs.module.base.service.dch.enums.DataChangeEventStatusEnum;
import com.rs.module.base.vo.pm.DataChangeEventLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据变更事件日志服务实现
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Service
public class DataChangeEventLogServiceImpl implements DataChangeEventLogService {

    @Autowired
    private DataChangeEventLogDao dataChangeEventLogDao;

    @Override
    public String createEventLog(DataChangeEventContext context) {
        try {
            DataChangeEventLogDO eventLog = new DataChangeEventLogDO();
            eventLog.setEventType(context.getEventType());
            eventLog.setTableName(context.getTableName());
            eventLog.setBusinessType(context.getBusinessType());
            eventLog.setPrimaryKeyId(context.getPrimaryKeyId());
            eventLog.setOldData(context.getOldDataMap() != null ? JSON.toJSONString(context.getOldDataMap()) : null);
            eventLog.setNewData(context.getNewDataMap() != null ? JSON.toJSONString(context.getNewDataMap()) : null);
            eventLog.setStatus(DataChangeEventStatusEnum.PENDING);
            eventLog.setRetryCount(context.getRetryCount());
            eventLog.setMaxRetryCount(context.getMaxRetryCount());
            eventLog.setEventTime(context.getEventTime());
            eventLog.setExtendInfo(context.getExtendInfo() != null ? JSON.toJSONString(context.getExtendInfo()) : null);

            dataChangeEventLogDao.insert(eventLog);

            context.setEventId(eventLog.getId());

            log.debug("创建数据变更事件日志成功: eventId={}, businessType={}, tableName={}, eventType={}",
                    eventLog.getId(), context.getBusinessType(), context.getTableName(), context.getEventType());

            return eventLog.getId();
        } catch (Exception e) {
            log.error("创建数据变更事件日志失败: businessType={}, tableName={}, eventType={}",
                    context.getBusinessType(), context.getTableName(), context.getEventType(), e);
            throw new RuntimeException("创建事件日志失败", e);
        }
    }

    @Override
    public boolean updateEventStatus(String eventId, DataChangeEventStatusEnum status) {
        try {
            int result = dataChangeEventLogDao.updateEventStatus(eventId, status);
            return result > 0;
        } catch (Exception e) {
            log.error("更新事件状态失败: eventId={}, status={}", eventId, status, e);
            return false;
        }
    }

    @Override
    public int updateEventDealClass(String id, String className) {
        return dataChangeEventLogDao.updateEventDealClass(id, className);
    }

    @Override
    public boolean updateEventProcessInfo(String eventId, DataChangeEventStatusEnum status,
                                          Date processStartTime, Date processEndTime,
                                          String errorMessage, Integer retryCount, Date nextRetryTime) {
        try {
            int result = dataChangeEventLogDao.updateEventProcessInfo(eventId, status, processStartTime,
                    processEndTime, errorMessage, retryCount, nextRetryTime);
            return result > 0;
        } catch (Exception e) {
            log.error("更新事件处理信息失败: eventId={}, status={}", eventId, status, e);
            return false;
        }
    }

    @Override
    public List<DataChangeEventLogDO> queryPendingEvents(int limit) {
        return dataChangeEventLogDao.queryPendingEvents(limit);
    }

    @Override
    public List<DataChangeEventLogDO> queryRetryEvents(int limit) {
        return dataChangeEventLogDao.queryRetryEvents(new Date(), limit);
    }

    @Override
    public List<DataChangeEventLogDO> queryEventsByStatus(DataChangeEventStatusEnum status, int limit) {
        return dataChangeEventLogDao.queryEventsByStatus(status, limit);
    }

    @Override
    public List<DataChangeEventLogDO> queryEventsByBusinessType(String businessType,
                                                                DataChangeEventStatusEnum status, int limit) {
        return dataChangeEventLogDao.queryEventsByBusinessType(businessType, status, limit);
    }

    @Override
    public DataChangeEventLogVO getEventDetail(String eventId) {
        DataChangeEventLogDO eventLog = dataChangeEventLogDao.selectById(eventId);
        if (eventLog == null) {
            return null;
        }
        return BeanUtils.toBean(eventLog, DataChangeEventLogVO.class);
    }

    @Override
    public int deleteExpiredSuccessEvents(int expireDays) {
        Date expireTime = new Date(System.currentTimeMillis() - expireDays * 24 * 60 * 60 * 1000L);
        return dataChangeEventLogDao.deleteExpiredSuccessEvents(expireTime);
    }

    @Override
    public Map<String, Long> countEventsByStatus() {
        List<Map<String, Object>> results = dataChangeEventLogDao.countEventsByStatus();
        Map<String, Long> countMap = new HashMap<>();

        for (Map<String, Object> result : results) {
            String status = (String) result.get("status");
            Long count = ((Number) result.get("count")).longValue();
            countMap.put(status, count);
        }

        return countMap;
    }

    @Override
    public CommonResult<String> retryFailedEvent(String eventId) {
        try {
            DataChangeEventLogDO eventLog = dataChangeEventLogDao.selectById(eventId);
            if (eventLog == null) {
                return CommonResult.error("事件不存在");
            }

            if (!DataChangeEventStatusEnum.FAILED.equals(eventLog.getStatus())) {
                return CommonResult.error("只能重试失败的事件");
            }

            // 重置状态为待处理
            boolean success = updateEventStatus(eventId, DataChangeEventStatusEnum.PENDING);
            if (success) {
                return CommonResult.success("重试事件成功");
            } else {
                return CommonResult.error("重试事件失败");
            }
        } catch (Exception e) {
            log.error("重试失败事件异常: eventId={}", eventId, e);
            return CommonResult.error("重试事件异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> batchRetryFailedEvents(String businessType, int limit) {
        try {
            List<DataChangeEventLogDO> failedEvents = queryEventsByBusinessType(businessType,
                    DataChangeEventStatusEnum.FAILED, limit);

            int successCount = 0;
            for (DataChangeEventLogDO event : failedEvents) {
                boolean success = updateEventStatus(event.getId(), DataChangeEventStatusEnum.PENDING);
                if (success) {
                    successCount++;
                }
            }

            return CommonResult.success(String.format("批量重试完成，成功重试 %d 个事件", successCount));
        } catch (Exception e) {
            log.error("批量重试失败事件异常: businessType={}, limit={}", businessType, limit, e);
            return CommonResult.error("批量重试事件异常: " + e.getMessage());
        }
    }
}
