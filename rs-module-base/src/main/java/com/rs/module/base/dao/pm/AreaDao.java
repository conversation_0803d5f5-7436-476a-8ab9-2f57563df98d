package com.rs.module.base.dao.pm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.entity.pm.AreaDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-监管管理-区域 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface AreaDao extends IBaseDao<AreaDO> {


    default PageResult<AreaDO> selectPage(AreaPageReqVO reqVO) {
        Page<AreaDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<AreaDO> areaPage = selectPage(page, new LambdaQueryWrapperX<AreaDO>()
                .eqIfPresent(AreaDO::getIsDel, reqVO.getIsDel())
                .betweenIfPresent(AreaDO::getAddTime, reqVO.getAddTime())
                .eqIfPresent(AreaDO::getAddUser, reqVO.getAddUser())
                .eqIfPresent(AreaDO::getUpdateUser, reqVO.getUpdateUser())
                .eqIfPresent(AreaDO::getProCode, reqVO.getProCode())
                .likeIfPresent(AreaDO::getProName, reqVO.getProName())
                .eqIfPresent(AreaDO::getCityCode, reqVO.getCityCode())
                .likeIfPresent(AreaDO::getCityName, reqVO.getCityName())
                .eqIfPresent(AreaDO::getRegCode, reqVO.getRegCode())
                .likeIfPresent(AreaDO::getRegName, reqVO.getRegName())
                .eqIfPresent(AreaDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(AreaDO::getOrgName, reqVO.getOrgName())
                .likeIfPresent(AreaDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(AreaDO::getParentId, reqVO.getParentId())
                .eqIfPresent(AreaDO::getAllParentId, reqVO.getAllParentId())
                .eqIfPresent(AreaDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(AreaDO::getAreaType, reqVO.getAreaType())
                .eqIfPresent(AreaDO::getAreaCode, reqVO.getAreaCode())
                .eqIfPresent(AreaDO::getLevel, reqVO.getLevel())
                .orderByDesc(AreaDO::getId));
        return new PageResult<>(areaPage.getRecords(), areaPage.getTotal());
    }

    default List<AreaDO> selectList(AreaListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AreaDO>()
                .eqIfPresent(AreaDO::getIsDel, reqVO.getIsDel())
                .betweenIfPresent(AreaDO::getAddTime, reqVO.getAddTime())
                .eqIfPresent(AreaDO::getAddUser, reqVO.getAddUser())
                .eqIfPresent(AreaDO::getUpdateUser, reqVO.getUpdateUser())
                .eqIfPresent(AreaDO::getProCode, reqVO.getProCode())
                .likeIfPresent(AreaDO::getProName, reqVO.getProName())
                .eqIfPresent(AreaDO::getCityCode, reqVO.getCityCode())
                .likeIfPresent(AreaDO::getCityName, reqVO.getCityName())
                .eqIfPresent(AreaDO::getRegCode, reqVO.getRegCode())
                .likeIfPresent(AreaDO::getRegName, reqVO.getRegName())
                .eqIfPresent(AreaDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(AreaDO::getOrgName, reqVO.getOrgName())
                .likeIfPresent(AreaDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(AreaDO::getParentId, reqVO.getParentId())
                .eqIfPresent(AreaDO::getAllParentId, reqVO.getAllParentId())
                .eqIfPresent(AreaDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(AreaDO::getAreaType, reqVO.getAreaType())
                .eqIfPresent(AreaDO::getAreaCode, reqVO.getAreaCode())
                .eqIfPresent(AreaDO::getLevel, reqVO.getLevel())
                .orderByDesc(AreaDO::getId));
    }

    default List<AreaDO> selectList(String orgCode, String areaType) {
        return selectList(new LambdaQueryWrapperX<AreaDO>()
                .eqIfPresent(AreaDO::getOrgCode, orgCode)
                .eqIfPresent(AreaDO::getAreaType, areaType)
                .orderByDesc(AreaDO::getId));
    }



    List<AreaListRespVO> getAreaListByOrgCode(@Param("orgCode") String orgCode, @Param("areaType") String areaType, @Param("roomCodes") List<String> roomCodes);

    List<AreaInfoRespVO> getAreaListByOrgCodeAndAreaType(@Param("orgCode") String orgCode, @Param("areaType") String areaType);

    List<AreaDO> queryArea(@Param("allParentId") List<String> allParentIdList,@Param("orgCode") String orgCode);

    List<AreaInfoRespVO> selectByOrgCodeAndCondition(@Param("orgCode") String orgCode, @Param("areaName") String areaName, @Param("areaCode") String areaCode, @Param("areaType") String areaType);

    default PageResult<AreaDO> selecChildrentPage(String orgCode, String id, String areaName, String areaCode, String areaType,Integer pageNo,Integer pageSize){
        Page<AreaDO> page = new Page<>(pageNo, pageSize);
        Page<AreaDO> areaPage = selectPage(page, new LambdaQueryWrapperX<AreaDO>()
                .eqIfPresent(AreaDO::getOrgCode, orgCode)
                .likeIfPresent(AreaDO::getAreaName, areaName)
                .likeIfPresent(AreaDO::getAllParentId, id)
                .eqIfPresent(AreaDO::getAreaType, areaType)
                .eqIfPresent(AreaDO::getAreaCode, areaCode)
                .orderByDesc(AreaDO::getId));
        return new PageResult<>(areaPage.getRecords(), areaPage.getTotal());
    }

    /**
     * 模糊查询返回当前区域类型最大的id值
     * @param str
     * @return
     */
    String getMaxIdByAreaType(String str);




}
