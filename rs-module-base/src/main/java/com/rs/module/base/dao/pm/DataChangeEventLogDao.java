package com.rs.module.base.dao.pm;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.base.entity.pm.DataChangeEventLogDO;
import com.rs.module.base.service.dch.enums.DataChangeEventStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 数据变更事件日志DAO
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Mapper
public interface DataChangeEventLogDao extends IBaseDao<DataChangeEventLogDO> {

    /**
     * 查询待处理的事件
     *
     * @param limit 限制数量
     * @return 事件列表
     */
    List<DataChangeEventLogDO> queryPendingEvents(@Param("limit") int limit);

    /**
     * 查询需要重试的事件
     *
     * @param currentTime 当前时间
     * @param limit       限制数量
     * @return 事件列表
     */
    List<DataChangeEventLogDO> queryRetryEvents(@Param("currentTime") Date currentTime,
                                                @Param("limit") int limit);

    /**
     * 根据状态查询事件
     *
     * @param status 状态
     * @param limit  限制数量
     * @return 事件列表
     */
    List<DataChangeEventLogDO> queryEventsByStatus(@Param("status") DataChangeEventStatusEnum status,
                                                   @Param("limit") int limit);

    /**
     * 根据业务类型查询事件
     *
     * @param businessType 业务类型
     * @param status       状态
     * @param limit        限制数量
     * @return 事件列表
     */
    List<DataChangeEventLogDO> queryEventsByBusinessType(@Param("businessType") String businessType,
                                                         @Param("status") DataChangeEventStatusEnum status,
                                                         @Param("limit") int limit);

    /**
     * 更新事件状态
     *
     * @param id     事件ID
     * @param status 新状态
     * @return 更新行数
     */
    int updateEventStatus(@Param("id") String id,
                         @Param("status") DataChangeEventStatusEnum status);

    int updateEventDealClass(@Param("id") String id,
                          @Param("className") String className);

    /**
     * 更新事件处理信息
     *
     * @param id               事件ID
     * @param status           状态
     * @param processStartTime 开始处理时间
     * @param processEndTime   结束处理时间
     * @param errorMessage     错误信息
     * @param retryCount       重试次数
     * @param nextRetryTime    下次重试时间
     * @return 更新行数
     */
    int updateEventProcessInfo(@Param("id") String id,
                              @Param("status") DataChangeEventStatusEnum status,
                              @Param("processStartTime") Date processStartTime,
                              @Param("processEndTime") Date processEndTime,
                              @Param("errorMessage") String errorMessage,
                              @Param("retryCount") Integer retryCount,
                              @Param("nextRetryTime") Date nextRetryTime);

    /**
     * 删除过期的成功事件
     *
     * @param expireTime 过期时间
     * @return 删除行数
     */
    int deleteExpiredSuccessEvents(@Param("expireTime") Date expireTime);

    /**
     * 统计各状态的事件数量
     *
     * @return 统计结果
     */
    List<java.util.Map<String, Object>> countEventsByStatus();
}
