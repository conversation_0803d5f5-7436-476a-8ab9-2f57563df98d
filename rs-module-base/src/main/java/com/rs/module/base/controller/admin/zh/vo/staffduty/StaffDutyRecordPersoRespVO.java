package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



@Data
@ApiModel(description = "值班排版-值班人员表")
public class StaffDutyRecordPersoRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private String id;
    @ApiModelProperty(value = "record关联的id")
    private String recordId;

    @ApiModelProperty(value = "民警编号")
    private String policeId;

    @ApiModelProperty(value = "民警姓名")
    private String policeName;

    @ApiModelProperty(value = "民警类型 1-民警  2-非民警")
    private Integer policeType;
    @ApiModelProperty(value = "照片(base64编码)")
    private String photo;
    @ApiModelProperty(value = "岗位唯一关联key")
    private String postKey;

    @ApiModelProperty("选中状态 0 否,1 是")
    private Integer selectStatus = 0;

}
