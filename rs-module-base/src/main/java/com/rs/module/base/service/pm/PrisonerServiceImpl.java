package com.rs.module.base.service.pm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.core.trans.anno.TransMethodResult;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.collection.ArrayUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.ForeignPersonnelListRespVO;
import com.rs.module.base.controller.admin.pm.vo.PoreginPersonnelPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerListVwRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.dao.pm.PrisonerDao;
import com.rs.module.base.dao.pm.PrisonerInDao;
import com.rs.module.base.dao.pm.PrisonerListDao;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.entity.pm.PrisonerListDO;
import com.rs.module.base.entity.pm.PrisonerVwDO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.ry.fill.RxxxFillService;
import com.rs.module.base.util.AgeCalculatorUtil;
import com.rs.module.ihc.dao.ipm.VisitDao;
import com.rs.module.ihc.dao.ipm.appointment.IhcsInternalMedicalAppointmentDao;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 在所人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonerServiceImpl implements PrisonerService {

    private static final Logger log = LoggerFactory.getLogger(PrisonerServiceImpl.class);
    @Resource
    private PrisonerInDao prisonerInDao;
    @Resource
    private PrisonerDao prisonerDao;
    @Resource
    private PrisonerListDao prisonerListDao;
    @Resource
    private PrisonRoomWarderService prisonRoomWarderService;

    @Resource
    private IhcsInternalMedicalAppointmentDao ihcsInternalMedicalAppointmentDao;
    @Resource
    private VisitDao visitDao;

    @Resource
    private RxxxFillService rxyyFillService;


    @Override
    public PageResult<PrisonerListVwRespVO> getPrisonerSelectCompomentList(PrisonerVwPageReqVO pageReqVO) {
        PageResult<PrisonerListDO> pageResult = prisonerListDao.selectPage(pageReqVO);
        PageResult<PrisonerListVwRespVO> result = new PageResult<>();
        List<PrisonerListDO> list = pageResult.getList();
        List<PrisonerListVwRespVO> resultList = new ArrayList<>(list.size());
        for (PrisonerListDO prisonerListDO : list) {
            PrisonerListVwRespVO prisonerVO = BeanUtils.toBean(prisonerListDO, PrisonerListVwRespVO.class);
            Integer age = AgeCalculatorUtil.calculateAge(prisonerVO.getCsrq());
            if (prisonerVO.getRssj() != null) {
                prisonerVO.setRsts(DateUtil.betweenDay(prisonerVO.getRssj(), new Date(), true));
            }
            prisonerVO.setAge(age);
            resultList.add(prisonerVO);
        }
        result.setTotal(pageResult.getTotal());
        result.setList(resultList);
        return result;
    }

    @Override
    public PrisonerVwRespVO getPrisonerOne(String orgCode, String jgrybm, PrisonerQueryRyztEnum ryzt) {
        PrisonerVwRespVO respVO;
        switch (ryzt) {
            case ZS:
                PrisonerInDO prisonerInVwDO = prisonerInDao.selectOne(
                        new LambdaQueryWrapperX<PrisonerInDO>()
                                .eqIfPresent(PrisonerInDO::getOrgCode, orgCode)
                                .eq(PrisonerInDO::getJgrybm, jgrybm)
                );
                respVO = BeanUtil.copyProperties(prisonerInVwDO, PrisonerVwRespVO.class);
                break;
            case ALL:
                PrisonerVwDO prisonerVwDO = prisonerDao.selectOne(
                        new LambdaQueryWrapperX<PrisonerVwDO>().eq(PrisonerVwDO::getJgrybm, jgrybm)
                                .eqIfPresent(PrisonerVwDO::getOrgCode, orgCode)
                                .last("limit 1")
                );
                respVO = BeanUtils.toBean(prisonerVwDO, PrisonerVwRespVO.class);
                break;
            default:
                throw new IllegalArgumentException("查询人员类型错误");
        }
        if (respVO == null) {
            throw new ServerException("未查询到该人员信息");
        }
        // 查询主协管民警
        Map<String, List<PrisonRoomWarderRespVO>> polices = prisonRoomWarderService.getPrisonRoomWarderListByRoomId(respVO.getJsh());
        List<PrisonRoomWarderRespVO> respVOList = polices.get(respVO.getJsh());
        if (respVOList == null) {
            respVOList = new ArrayList<>();
        }
        respVOList.stream().filter(police -> "w".equals(police.getUserType()))
                .findFirst().ifPresent(p -> {
                    respVO.setZgmjName(p.getPoliceName());
                    respVO.setZgmjSfzh(p.getPoliceId());
                    respVO.setZgmjSfzh(p.getPoliceSfzh());
                });

        /**
         * 获取全部协管民警姓名,逗号分割
         */
        String xgmj = respVOList.stream().filter(police -> "a".equals(police.getUserType()))
                .map((PrisonRoomWarderRespVO::getPoliceName))
                .collect(Collectors.joining(","));
        respVO.setXgmjName(xgmj);
        respVO.setZgzjList(respVOList);
        if (respVO.getCsrq() != null) {
            //计算年龄
            try {
                respVO.setAge(new Date().getYear() - respVO.getCsrq().getYear());
            } catch (Exception e) {
                log.error("计算年龄失败", e);
            }
        }

        rxyyFillService.fill(respVO);
        return respVO;
    }

    @TransMethodResult
    @Override
    public PrisonerVwRespVO getPrisonerByJgrybm(String jgrybm) {
        return getPrisonerOne(null, jgrybm, PrisonerQueryRyztEnum.ALL);
    }

    @Override
    public List<PrisonerInDO> getPrisonerInOneList(Collection<String> jgrybms) {
        List<PrisonerInDO> prisonerInVwDOS = prisonerInDao.selectList(
                new LambdaQueryWrapperX<PrisonerInDO>()
                        .in(PrisonerInDO::getJgrybm, jgrybms));
        if (CollUtil.isEmpty(prisonerInVwDOS)) {
            return new ArrayList<>();
        }
        return prisonerInVwDOS;
    }

    @Override
    public List<PrisonerInDO> getPrisonerInList(String orgCode, String roomCode) {
        List<PrisonerInDO> prisonerInDOS = prisonerInDao.selectList(
                new LambdaQueryWrapperX<PrisonerInDO>()
                        .eq(PrisonerInDO::getOrgCode, orgCode)
                        .eq(PrisonerInDO::getJsh, roomCode));
        if (CollUtil.isEmpty(prisonerInDOS)) {
            return new ArrayList<>();
        }
        return prisonerInDOS;
    }

    @Override
    public List<PrisonerVwRespVO> getPrisonerListByJgrybm(String jgrybm, PrisonerQueryRyztEnum ryzt) {
        List<PrisonerVwRespVO> resultList = new ArrayList<>();
        LinkedList<String> strings = Arrays.stream(jgrybm.split(","))   // 分割并处理空格
                .map(String::trim)                   // 去除元素前后空格
                .collect(Collectors.toCollection(LinkedList::new));
        switch (ryzt) {
            case ZS:
                LambdaQueryWrapper<PrisonerInDO> queryWrapper = new LambdaQueryWrapperX<PrisonerInDO>()
                        .in(PrisonerInDO::getJgrybm, strings);
                List<PrisonerInDO> prisonerInVwDOS = prisonerInDao.selectList(queryWrapper);
                Map<String, PrisonerInDO> prisonerInVwDOMap = prisonerInVwDOS.stream()
                        .collect(Collectors.toMap(PrisonerInDO::getJgrybm, p -> p));
                for (String jgrybms : strings) {
                    PrisonerInDO prisonerInVwDO = prisonerInVwDOMap.get(jgrybms);
                    if (prisonerInVwDO != null) {
                        resultList.add(BeanUtil.copyProperties(prisonerInVwDO, PrisonerVwRespVO.class));
                    }
                }
                break;
            case ALL:
                LambdaQueryWrapper<PrisonerVwDO> wrapper = new LambdaQueryWrapperX<PrisonerVwDO>()
                        .in(PrisonerVwDO::getJgrybm, strings);
                List<PrisonerVwDO> prisonerVwDOS = prisonerDao.selectList(wrapper);
                Map<String, PrisonerVwDO> prisonerVwDOSMap = prisonerVwDOS.stream()
                        .collect(Collectors.toMap(PrisonerVwDO::getJgrybm, p -> p));
                for (String jgrybms : strings) {
                    PrisonerVwDO prisonerVwDO = prisonerVwDOSMap.get(jgrybms);
                    if (prisonerVwDO != null) {
                        resultList.add(BeanUtil.copyProperties(prisonerVwDO, PrisonerVwRespVO.class));
                    }
                }
                break;
            default:
                throw new IllegalArgumentException("查询人员类型错误");
        }
        if (CollUtil.isNotEmpty(resultList)) {
            String jshs = resultList.stream().map(PrisonerVwRespVO::getJsh).distinct().collect(Collectors.joining(","));
            Map<String, List<PrisonRoomWarderRespVO>> polices = prisonRoomWarderService.getPrisonRoomWarderListByRoomId(jshs);
            for (PrisonerVwRespVO respVO : resultList) {
                respVO.setAge(AgeCalculatorUtil.calculateAge(respVO.getCsrq()));
                List<PrisonRoomWarderRespVO> respVOList = polices.get(respVO.getJsh());
                if (CollUtil.isNotEmpty(respVOList)) {
                    respVO.setZgzjList(respVOList);
                    respVOList.stream().filter(police -> "w".equals(police.getUserType()))
                            .findFirst().ifPresent(p -> {
                                respVO.setZgmjName(p.getPoliceName());
                                respVO.setZgmjSfzh(p.getPoliceId());
                                respVO.setZgmjSfzh(p.getPoliceSfzh());
                            });
                }
            }
        }
        return resultList;
    }

    @Override
    public Integer getPrisonerCount(String orgCode, String roomCode) {
        return prisonerInDao.selectCount(new LambdaQueryWrapper<PrisonerInDO>()
                .eq(PrisonerInDO::getOrgCode, orgCode)
                .eq(PrisonerInDO::getJsh, roomCode));
    }

    @Override
    public Integer getPrisonerToDayInCount(String orgCode, String roomCode) {
        return prisonerInDao.selectCount(new LambdaQueryWrapper<PrisonerInDO>()
                .eq(PrisonerInDO::getOrgCode, orgCode)
                .eq(PrisonerInDO::getJsh, roomCode)
                .between(PrisonerInDO::getRssj, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
    }

    @Override
    public List<PrisonerVwRespVO> getPrisonerListByJsh(String jsh) {

        List<PrisonerVwRespVO> resultList = new ArrayList<>();
        LambdaQueryWrapper<PrisonerInDO> queryWrapper = new LambdaQueryWrapperX<PrisonerInDO>();
        queryWrapper.eq(PrisonerInDO::getJsh, jsh);
        List<PrisonerInDO> prisonerInVwDOS = prisonerInDao.selectList(queryWrapper);
        prisonerInVwDOS.forEach(p -> {
            resultList.add(BeanUtil.copyProperties(p, PrisonerVwRespVO.class));
        });
        resultList.forEach(respVO -> {
            respVO.setAge(AgeCalculatorUtil.calculateAge(respVO.getCsrq()));
            rxyyFillService.fill(respVO);
        });
        return resultList;
    }


    @Override
    public PrisonerVwRespVO getPrisonerWithMedicalInformationByJgrybm(String jgrybm) {
        PrisonerVwRespVO respVO = getPrisonerByJgrybm(jgrybm);
        if (respVO == null) {
            return null;
        }
        // 查询病案信息
        PrisonerVwRespVO prisonerVwRespVO = prisonerDao.selectPrisonerMedInfoByJgrybm(jgrybm);
        if (prisonerVwRespVO != null) {
            respVO.setZjjzsj(prisonerVwRespVO.getZjjzsj());
            respVO.setZjzdbq(prisonerVwRespVO.getZjzdbq());
        }
        return respVO;
    }

    @Override
    public PageResult<PrisonerListVwRespVO> getCivilizedPersonalPrisonerSelectCompomentList(PrisonerVwPageReqVO pageReqVO) {
        Page<PrisonerListDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Date[] addTime = pageReqVO.getAddTime();
        Date startAddTime = ArrayUtils.get(addTime, 0);
        Date endAddTime = ArrayUtils.get(addTime, 1);
        Date[] rssj = pageReqVO.getRssj();
        Date startRssj = ArrayUtils.get(rssj, 0);
        Date endRssj = ArrayUtils.get(rssj, 1);
        String ryzt = Objects.nonNull(pageReqVO.getRyzt()) &&
                pageReqVO.getRyzt().getCode().equals(PrisonerQueryRyztEnum.ZS.getCode()) ?
                pageReqVO.getRyzt().getCode() : null;
        String xmPy = StringUtils.isEmpty(pageReqVO.getXm()) ? null : pageReqVO.getXm().toUpperCase();
        Page<PrisonerListDO> tempPage = prisonerListDao.getCivilizedPersonalPrisonerSelectCompomentList(page, pageReqVO,
                startAddTime, endAddTime, startRssj, endRssj, ryzt, xmPy);
        PageResult<PrisonerListDO> pageResult = new PageResult<>(tempPage.getRecords(), tempPage.getTotal());
        PageResult<PrisonerListVwRespVO> result = new PageResult<>();
        List<PrisonerListDO> list = pageResult.getList();
        List<PrisonerListVwRespVO> resultList = new ArrayList<>(list.size());
        for (PrisonerListDO prisonerListDO : list) {
            PrisonerListVwRespVO prisonerVO = BeanUtils.toBean(prisonerListDO, PrisonerListVwRespVO.class);
            Integer age = AgeCalculatorUtil.calculateAge(prisonerVO.getCsrq());
            if (prisonerVO.getRssj() != null) {
                prisonerVO.setRsts(DateUtil.betweenDay(prisonerVO.getRssj(), new Date(), true));
            }

            prisonerVO.setAge(age);
            resultList.add(prisonerVO);
        }
        result.setTotal(pageResult.getTotal());
        result.setList(resultList);
        return result;
    }

    @Override
    public String getJgrybm(String ryId) {
        LambdaQueryWrapper<PrisonerListDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(PrisonerListDO::getJgrybm)
                .eq(PrisonerListDO::getId, ryId);
        List<PrisonerListDO> prisonerListDOS = prisonerListDao.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(prisonerListDOS)) {
            return prisonerListDOS.get(0).getJgrybm();
        }
        return "";
    }

    @Override
    public PageResult<ForeignPersonnelListRespVO> getForeinPersionnelSelectCompoment(PoreginPersonnelPageReqVO pageReqVO) {
        Page page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<ForeignPersonnelListRespVO> listRespVOPage = prisonerListDao.getForeinPersionnelSelectCompoment(page, pageReqVO);
        PageResult<ForeignPersonnelListRespVO> pageResult = new PageResult<>();
        pageResult.setTotal(listRespVOPage.getTotal());
        pageResult.setList(listRespVOPage.getRecords());
        return pageResult;
    }

    @Override
    public List<PrisonerListDO> getPrisonerListVm(List<String> jgrybms) {
        if (CollUtil.isEmpty(jgrybms)) {
            return Collections.emptyList();
        }
        return prisonerListDao.selectList(PrisonerListDO::getJgrybm, jgrybms);
    }

}
