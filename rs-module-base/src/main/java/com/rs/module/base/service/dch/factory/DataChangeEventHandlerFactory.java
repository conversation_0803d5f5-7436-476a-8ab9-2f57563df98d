package com.rs.module.base.service.dch.factory;

import com.rs.module.base.service.dch.context.DataChangeEventContext;
import com.rs.module.base.service.dch.handler.DataChangeEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据变更事件处理器工厂
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
public class DataChangeEventHandlerFactory {

    @Autowired(required = false)
    private List<DataChangeEventHandler> handlers = new ArrayList<>();

    /**
     * 按业务类型缓存处理器
     */
    private final Map<String, List<DataChangeEventHandler>> handlerCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        log.info("初始化数据变更事件处理器工厂，共发现 {} 个处理器", handlers.size());
        
        // 按优先级排序
        handlers.sort(Comparator.comparingInt(DataChangeEventHandler::getPriority));
        
        // 按业务类型分组缓存
        for (DataChangeEventHandler handler : handlers) {
            String businessType = handler.getSupportedBusinessType();
            handlerCache.computeIfAbsent(businessType, k -> new ArrayList<>()).add(handler);
            log.info("注册数据变更事件处理器: {} -> {}", businessType, handler.getClass().getSimpleName());
        }
    }

    /**
     * 获取支持处理指定事件的处理器列表
     *
     * @param context 事件上下文
     * @return 处理器列表
     */
    public List<DataChangeEventHandler> getHandlers(DataChangeEventContext context) {
        List<DataChangeEventHandler> result = new ArrayList<>();
        
        // 先从缓存中获取对应业务类型的处理器
        List<DataChangeEventHandler> cachedHandlers = handlerCache.get(context.getBusinessType());
        if (cachedHandlers != null) {
            for (DataChangeEventHandler handler : cachedHandlers) {
                if (handler.supports(context)) {
                    result.add(handler);
                }
            }
        }
        
        // 如果没有找到，则遍历所有处理器
        if (result.isEmpty()) {
            for (DataChangeEventHandler handler : handlers) {
                if (handler.supports(context)) {
                    result.add(handler);
                }
            }
        }
        
        return result;
    }

    /**
     * 获取第一个支持处理指定事件的处理器
     *
     * @param context 事件上下文
     * @return 处理器，如果没有找到则返回null
     */
    public DataChangeEventHandler getFirstHandler(DataChangeEventContext context) {
        List<DataChangeEventHandler> handlers = getHandlers(context);
        return handlers.isEmpty() ? null : handlers.get(0);
    }

    /**
     * 获取所有注册的处理器
     *
     * @return 处理器列表
     */
    public List<DataChangeEventHandler> getAllHandlers() {
        return new ArrayList<>(handlers);
    }

    /**
     * 根据业务类型获取处理器
     *
     * @param businessType 业务类型
     * @return 处理器列表
     */
    public List<DataChangeEventHandler> getHandlersByBusinessType(String businessType) {
        return handlerCache.getOrDefault(businessType, new ArrayList<>());
    }

    /**
     * 获取支持的业务类型列表
     *
     * @return 业务类型列表
     */
    public List<String> getSupportedBusinessTypes() {
        return new ArrayList<>(handlerCache.keySet());
    }
}
