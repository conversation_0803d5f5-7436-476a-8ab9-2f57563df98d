package com.rs.module.base.service.video;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.module.base.controller.admin.video.vo.*;
import com.rs.module.base.dao.pm.device.SplwTreeDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import com.rs.framework.common.pojo.PageResult;
/**
 * <AUTHOR>
 * @Date 2020/11/12 15:03
 */
@Service
@Slf4j
public class VideoRouteServiceImpl implements VideoRouteService {

        @Value("${video.getRealTimeStreamForWeb:}")
    private String getRealTimeStreamForWebUrl;

    @Value("${video.getSearchRecordRequest:}")
    private String getSearchRecordRequestUrl;

    @Value("${video.getFlvRealTimeStream:}")
    private String getFlvRealTimeStreamUrl;

    @Value("${video.recordFileOperRequestTransfer:}")
    private String recordFileOperRequestTransferUrl;

    @Value("${video.recordFileUrl:}")
    private String recordFileUrl;

    @Value("${video.getGoVideoOrGoVlinkRecordRequest:}")
    private String getGoVideoOrGoVlinkRecordRequestUrl;

    @Value("${video.ptzCtrl:}")
    private String ptzCtrlUrl;

    @Value("${video.turnToDefaultPreset:}")
    private String turnToDefaultPresetUrl;

    @Value("${video.presetCtrl:}")
    private String presetCtrlUrl;

    @Value("${video.savePtzLock:}")
    private String savePtzLockUrl;

    @Value("${video.ptzCtrl2:}")
    private String ptzCtrlUrl2;

    @Value("${video.goVideoPtzCtrl:}")
    private String goVideoPtzCtrl;

    @Value("${video.govidoToken:}")
    private String govidoToken;

    @Value("${video.getRealTimeStreamForApp:}")
    private String getRealTimeStreamForApp;

    @Value("${video.getChannelTree:}")
    private String getChannelTree;

    @Value("${video.uapUserName:}")
    private String uapUserName;

    @Value("${video.uapPassword:}")
    private String uapPassword;

    @Value("${video.recordMonthRetrievalRequest:}")
    private String recordMonthRetrievalRequest;

    @Value("${video.recordFileOperRequestTransfer2:}")
    private String recordFileOperRequestTransfer;

    @Value("${video.queryRecordRequest:}")
    private String queryRecordRequest;

    @Value("${video.govDevChnnInfoAv:}")
    private String govDevChnnInfoAv;

    @Value("${video.getSingleDevChnnRequest:}")
    private String getSingleDevChnnRequest;

    @Value("${video.govDevChnnInfo:}")
    private String govDevChnnInfo;

    @Value("${video.addTreeNodeRequest:}")
    private String addTreeNodeRequest;

    @Value("${video.clearTreeNodeRequest:}")
    private String clearTreeNodeRequest;

    @Value("${video.moveTreeNodeLeafRequest:}")
    private String moveTreeNodeLeafRequest;

    @Value("${video.deleteTreeNodeRequest:}")
    private String deleteTreeNodeRequest;

    @Value("${video.getPresetByChannelId:}")
    private String getPresetByChannelId;

    @Value("${video.deletePreset:}")
    private String deletePreset;

    @Value("${video.setDevChnnRequest:}")
    private String setDevChnnRequest;

    @Value("${video.getAllDeviceRequest:}")
    private String getAllDeviceRequest;

    @Value("${sply.treeCode:}")
    public String rootCode;

    @Value("${video.getChannelIds:}")
    private String getChannelIds;

    @Value("${sply.ip:}")
    private String ip;

    @Value("${sply.port:}")
    private String port;



    /*@Autowired
    private CommonDao commonDao;*/

//    @Value("${sply.rootId}")
//    private String rootId;

    private static JSONArray treeJsonArray = null;

    private static List<VideoTreeVO> videoTreeVOS = new ArrayList<>();

    private static Date date = null;

    private static String token = null;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SplwTreeDao splwTreeDao;


    /**
     * 把JavaBean转化为map
     *
     * @param bean
     * @return
     * @throws Exception
     */
    public static Map<String, Object> beanTomap(Object bean) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //获取JavaBean的描述器
        BeanInfo b = Introspector.getBeanInfo(bean.getClass(), Object.class);
        //获取属性描述器
        PropertyDescriptor[] pds = b.getPropertyDescriptors();
        //对属性迭代
        for (PropertyDescriptor pd : pds) {
            //属性名称
            String propertyName = pd.getName();

            //属性值,用getter方法获取
            Method m = pd.getReadMethod();

            //用对象执行getter方法获得属性值
            Object properValue = m.invoke(bean);

            //把属性名-属性值 存到Map中
            map.put(propertyName, properValue);
        }
        return map;
    }

    /**
     * 发送post请求 携带string数据的
     *
     * @param url
     */
    public StringBuffer sendPostMethod(String url, String param) throws Exception {
        //1.创建httpClient
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //2.创建post请求方式实例
        String url2 = url + "?" + param;
        HttpPost httpPost = new HttpPost(url2);
        //2.1设置请求头 发送的是json数据格式
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setHeader("Connection", "keep-alive");
        if (url.contains("checkLogin")||url.contains("oauth/token")) {
            httpPost.setHeader("Authorization", null);
        } else {
            httpPost.setHeader("Authorization",getUapToken());
        }
        //3.设置参数---设置消息实体 也就是携带的数据
        StringEntity entity = new StringEntity(param, "utf-8");
        //设置编码格式
        entity.setContentEncoding("UTF-8");
        // 发送Json格式的数据请求
        entity.setContentType("application/json");
        //把请求消息实体塞进去
        httpPost.setEntity(entity);


        log.info("请求地址====>:" + httpPost.getURI().toString());
        log.info("请求头====>：" + Arrays.toString(httpPost.getAllHeaders()));
        log.info("请求参数====>：" + new BufferedReader(new InputStreamReader(httpPost.getEntity().getContent())));


        //4.执行http的post请求
        CloseableHttpResponse httpResponse = null;
        InputStream inputStream = null;
        StringBuffer stringBuffer = new StringBuffer("");
        try {
            httpResponse = httpClient.execute(httpPost);
            //5.对返回的数据进行处理
            //5.2对数据进行处理
            HttpEntity httpEntity = httpResponse.getEntity();
            //获取content实体内容
            inputStream = httpEntity.getContent();
            //封装成字符流来输出
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            String line = "";
            while ((line = bufferedReader.readLine()) != null) {
                line = new String(line.getBytes());
                stringBuffer.append(line);
            }

        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            //6.关闭inputStream和httpResponse
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (httpResponse != null) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return stringBuffer;
    }

    /**
     * 发送post请求 携带string数据的
     *
     * @param url
     */
    public static StringBuffer sendTokenPostMethod(String url, String jsonParam, String token) throws Exception {
        //1.创建httpClient
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //2.创建post请求方式实例
        HttpPost httpPost = new HttpPost(url);

        //2.1设置请求头 发送的是json数据格式
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setHeader("Connection", "keep-alive");
        httpPost.setHeader("Authorization", token);

        //3.设置参数---设置消息实体 也就是携带的数据
        StringEntity stringEntity = new StringEntity(jsonParam, "UTF-8");
        //设置编码格式
        stringEntity.setContentEncoding("UTF-8");
        // 发送Json格式的数据请求
        stringEntity.setContentType("application/json");
        log.info("请求{}接口的参数为{}", url, stringEntity);
        //把请求消息实体塞进去
        httpPost.setEntity(stringEntity);


        log.info("请求地址====>:" + httpPost.getURI().toString());
        log.info("请求头====>：" + Arrays.toString(httpPost.getAllHeaders()));
        log.info("请求参数====>：" + new BufferedReader(new InputStreamReader(httpPost.getEntity().getContent())));


        //4.执行http的post请求
        CloseableHttpResponse httpResponse = null;
        InputStream inputStream = null;
        StringBuffer stringBuffer = new StringBuffer("");
        try {
            httpResponse = httpClient.execute(httpPost);
            //5.对返回的数据进行处理
            //5.2对数据进行处理
            HttpEntity httpEntity = httpResponse.getEntity();
            //获取content实体内容
            inputStream = httpEntity.getContent();
            //封装成字符流来输出
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            String line = "";
            while ((line = bufferedReader.readLine()) != null) {
                line = new String(line.getBytes());
                stringBuffer.append(line);
            }

        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            //6.关闭inputStream和httpResponse
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (httpResponse != null) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return stringBuffer;
    }

    /**
     * 发起get请求
     *
     * @param url
     * @param param
     * @return
     */
    public String sendGet(String url, String param) {
        String result = "";
        BufferedReader in = null;
        String urlNameString = "";
        try {
            if(StringUtil.isNotEmpty(param)){
                urlNameString =  url + "/" + param;
            }else {
                urlNameString = url;
            }
            log.info("urlNameString----------->" + urlNameString);
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            if(!urlNameString.contains("oauth2")){
                connection.setRequestProperty("Authorization", getUapToken());
            }
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");

            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            for (String key : map.keySet()) {
                System.out.println(key + "--->" + map.get(key));
            }
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送GET请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }


    /**
     * 发起视频服务器请求
     *
     * @param url
     * @param params
     * @return
     */
    public String ajaxWdaRequest(String url, String params) {
        StringBuffer buffer = null;
        try {
            buffer = sendPostMethod(url, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buffer == null ? null : buffer.toString();
    }

    /**
     * 发起视频服务器请求
     *
     * @param url
     * @param jsonParam
     * @return
     */
    public String ajaxWdaRequest2(String url, String jsonParam, String token) {
        StringBuffer buffer = null;
        try {
            buffer = sendTokenPostMethod(url, jsonParam, token);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buffer == null ? null : buffer.toString();
    }

    @Override
    public String getOtherRealTimeStream(OtherRealTimeStreamDto dto) throws Exception {
//        String str = dealDto(dto);
        OtherRealTimeStreamAppDto appDto = new OtherRealTimeStreamAppDto();
        appDto.setStreamType(1);
        appDto.setChanId(Long.valueOf(dto.getChannelId()));
        String realTimeStreamForApp = getRealTimeStreamForApp(appDto);
        //发起请求
        return realTimeStreamForApp;
    }

    @Override
    public String getRealTimeStreamForWeb(OtherRealTimeStreamDto dto) {
        String str = null;
        try {
            str = dealDto(dto);
        } catch (Exception e) {
            log.info(e.toString());
        }
        //发起请求
        return ajaxWdaRequest(getRealTimeStreamForWebUrl, str);
    }

    @Override
    public String getRealTimeStreamForApp(OtherRealTimeStreamAppDto dto) throws Exception {
        /*Map<String, Object> map = new HashMap<>();
        map.put("username", uapUserName);
        map.put("password", uapPassword);*/
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("standardStream", 1);
        dto.setClientType(107);
        dto.setEncodeInfo(jsonObject);
        String uapToken = getUapToken();
        String s = JSONObject.toJSONString(dto);
        return ajaxWdaRequest2(getRealTimeStreamForApp, s, uapToken);
    }

    @Override
    public String getSearchRecordRequest(Map<String, Object> dto) throws Exception {
        String str = dealDto(dto);
        //发起请求
        return ajaxWdaRequest(getSearchRecordRequestUrl, str);
    }

    @Override
    public String getUapToken() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("username", uapUserName);
//        map.put("password", uapPassword);
//        String str = null;
//        String accessToken = null;
//        try {
//            str = dealDto(map);
//            String s = "";
//            s = ajaxWdaRequest(govidoToken, str);
//            JSONObject jsonObject = JSONObject.parseObject(s);
//            JSONObject data = jsonObject.getJSONObject("data");
//            accessToken = data.get("accessToken").toString();
//            //发起请求
//        } catch (Exception e) {
//            log.info(e.toString());
//
//        }
        return getUapTokenFeNew();
    }

    @Override
    public JSONObject getUapTokenFe() {
        JSONObject jsonObjectResult = new JSONObject();
        try {
            jsonObjectResult.put("token", getUapTokenFeNew());
            jsonObjectResult.put("tokenType", "bearer");
            //发起请求
        } catch (Exception e) {
            log.info(e.toString());
        }
        return jsonObjectResult;
    }
    private String UAP_TOKEN_REDIS_KEY = "rs:acp:splw:uap_token_fe_new:%s";
    private String getTokenKey() {
        return String.format(UAP_TOKEN_REDIS_KEY, uapUserName);
    }
    @Override
    public String getUapTokenFeNew() {
        String redisKey = getTokenKey();
        String cachedToken = (String) redisTemplate.opsForValue().get(redisKey);
        if (StringUtil.isNotEmpty(cachedToken)) {
            log.info("从Redis缓存中获取token: " + cachedToken);
            return cachedToken;
        }
        String url = "http://"+ip+":"+port+"/oauth2/secure/encrypt?content=";
        if(StringUtil.isNotEmpty(uapPassword)){
            String s = sendGet(url + uapPassword, null);
            log.info("获取密码加密接口返回为"+s);
            JSONObject jsonObject = JSONObject.parseObject(s);
            String data = jsonObject.getString("data");
            String url2 = "http://"+ip+":"+port+"/oauth2/oauth/token";
            HashMap<String, Object> map = new HashMap<>();
            map.put("username",uapUserName);
            map.put("password",data);
            map.put("grant_type","password");
            map.put("client_id","client_id");
            map.put("client_secret","client_secret");
            map.put("isLdap","0");
            try {
                String s1 = doFormDataPost(url2, map);
                log.info("oauth2/oauth/token的接口返回"+s1);
                JSONObject jsonObject1 = JSONObject.parseObject(s1);
                String access_token = jsonObject1.getString("access_token");
                String token = "bearer "+access_token;
                // 将token存入Redis，设置30分钟过期时间
                redisTemplate.opsForValue().set(redisKey, token, 30, TimeUnit.MINUTES);
                log.info("将新的token存入Redis缓存，30分钟后过期");
                return token;
            } catch (Exception e) {
                log.error(e+"");
            }

        }
        return null;
    }

    @Override
    public String getFlvRealTimeStream(FlvRealTimeStreamDto dto) throws Exception {
        String str = dealDto(dto);
        //发起请求
        return ajaxWdaRequest(getFlvRealTimeStreamUrl, str);
    }

    @Override
    public String recordFileOperRequestTransfer(FileOperRequestTransferDto dto) throws Exception {
        Map<String, Object> map = beanTomap(dto);
        StringBuffer buffer = new StringBuffer();
        for (Map.Entry<String, Object> map1 : map.entrySet()) {
            String key = map1.getKey();
            String value = map1.getValue() == null ? null : map1.getValue().toString();
            String upKey = null;
            if (key != null) {
                if ("ip".equals(key)) {
                    upKey = key;
                } else {
                    upKey = key.substring(0, 1).toUpperCase().concat(key.substring(1));
                }
            }
            if (upKey != null && value != null) {
                buffer.append(upKey).append("=").append(value).append("&");
            }
        }
        String str = null;
        if (buffer.length() > 0) {
            str = buffer.substring(0, buffer.length() - 1);
            str = str.replaceAll(" ", "%20");
        }
        //发起请求
        return ajaxWdaRequest(recordFileOperRequestTransferUrl, str);
    }

    @Override
    public String recordFileUrl(HttpServletRequest request) {
        String params = request.getQueryString();
        return sendGet(recordFileUrl, params);
    }

    @Override
    public String getGoVideoOrGoVlinkRecordRequest(GoVideoOrGoVlinkRecordRequestDto dto) throws Exception {
        String str = dealDto(dto);
        //发起请求
        return ajaxWdaRequest(getGoVideoOrGoVlinkRecordRequestUrl, str);
    }

    @Override
    public String ptzCtrl(PtzCtrlDto dto) throws Exception {
        String str = dealDto(dto);
        //发起请求
        return ajaxWdaRequest(ptzCtrlUrl2, str);
    }

    @Override
    public String ptzCtrlRequest(PtzCtrlNewDto dto) throws Exception {
        PtzCtrlDto ptzCtrlDto = new PtzCtrlDto();
        BeanUtils.copyProperties(dto, ptzCtrlDto);
        ptzCtrlDto.setChannelId(dto.getChanId());
        String str = dealDto(ptzCtrlDto);
        //发起请求
        return ajaxWdaRequest(goVideoPtzCtrl, str);
    }

    @Override
    public Object recordMonthRetrievalRequest(RecordMonthRetrievalDTO dto) throws Exception {
        dto.setChannelId(dto.getChanId());
        String str = dealDto(dto);
        JSONObject jsonObject = null;
        List<String> strings = new ArrayList<>();
        List<String> dayList = new ArrayList<>();
        try {
            String s = ajaxWdaRequest(recordMonthRetrievalRequest, str);
            jsonObject = JSONObject.parseObject(s);
            JSONObject message = jsonObject.getJSONObject("message");
            String valid = message.getString("valid");
            String strip = StringUtils.strip(valid, "[]");
            String[] split = strip.split(",");
            strings = Arrays.asList(split);
        }catch (Exception e){
            if(dto.getStorageType().equals("0")){
                return jsonObject;
            }else {
                //strings = DateUtils.getDayListByMonth(dto.getYear(),dto.getMonth());
            }
        }
        int dayCount = 1;
        for (String day : strings) {
            String days = "";
            if (day.equals("1")) {
                String month = Integer.valueOf(dto.getMonth()) > 9 ? dto.getMonth() + "" : "0" + dto.getMonth();
                days = dayCount < 10 ? "0" + dayCount : dayCount + "";
                String date = dto.getYear() + "-" + month + "-" + days;
               // DateUtils.stringToDate(date, DateUtils.DATE_PATTERN);
                dayList.add(date);
            }
            dayCount++;
        }
        return dayList;
    }

    @Override
    public String ptzCtrl2(PtzCtrlDto dto) throws Exception {
        String str = dealDto(dto);
        //发起请求
        return ajaxWdaRequest(ptzCtrlUrl, str);
    }

    @Override
    public String turnToDefaultPreset(TurnToDefaultPresetDto dto) throws Exception {
        String str = dealDto(dto);
        //发起请求
        return ajaxWdaRequest(turnToDefaultPresetUrl, str);
    }

    @Override
    public String deletePreset(DeletePresetVO vo) throws Exception {
        vo.setChannelId(vo.getChanId());
        String str = dealDto(vo);
        return ajaxWdaRequest(deletePreset, str);
    }

    @Override
    public String turnToDefaultPreset2(String chanId) throws Exception {
        HashMap<String, String> map = new HashMap<>();
        map.put("channelId", chanId);
        String param = dealDto(map);
        return ajaxWdaRequest(turnToDefaultPresetUrl, param);
    }

    @Override
    public String presetCtrl(PresetCtrlDto dto) throws Exception {
        dto.setChannelId(dto.getChanId());
        String str = dealDto(dto);
        //发起请求
        return ajaxWdaRequest(presetCtrlUrl, str);
    }

    @Override
    public List<PreseDTO> getPresetByChannelId(String chanId) {
        List<PreseDTO> presetList = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("channelId", chanId);
        try {
            String param = dealDto(map);
            String s = ajaxWdaRequest(getPresetByChannelId, param);
            JSONObject jsonObject = JSONObject.parseObject(s);
            JSONObject data = jsonObject.getJSONObject("data");
            JSONObject data1 = data.getJSONObject("data");
            Integer operResult = data1.getInteger("operResult");
            if (operResult == 0) {
                presetList = JSONObject.parseArray(data1.getString("presetList"), PreseDTO.class);

            }
            return presetList;
        } catch (Exception e) {
            log.info(e.toString());
        }
        return presetList;
    }

    @Override
    public String savePtzLock(SavePtzLockDto dto) throws Exception {
        String str = dealDto(dto);
        //发起请求
        return ajaxWdaRequest(savePtzLockUrl, str);
    }
    Integer JGJG_TYPE_JGZD =4;
    @Override
    public List<VideoTreeVO> getVideoTree(VideoTreeDto dto) throws Exception {

        List<VideoTreeVO> voArrayList = new ArrayList<>();
        List<VideoTreeVO> voArrayList3 = new ArrayList<>();
        List<VideoTreeVO> voArrayList1 = deepCopy(videoTreeVOS);
        Integer prisonType = 0;//commonDao.getPrisonType(dto.getPrisonId());
        if(prisonType!=null&&prisonType==JGJG_TYPE_JGZD){//机构类型：监管支队
            /*List<String> prisonListByZd = videoSplitScreenMapper.getPrisonListByZd(dto.getPrisonId());
            voArrayList1 = voArrayList1.stream()
                    .filter(videoTreeVO -> prisonListByZd.contains(videoTreeVO.getPrisonId())||videoTreeVO.getId()==1)
                    .collect(Collectors.toList());*/
        }else {
            voArrayList1 = voArrayList1.stream()
                    .filter(videoTreeVO -> dto.getPrisonId().equals(videoTreeVO.getPrisonId())||videoTreeVO.getId()==1)
                    .collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(dto.getChnAbility()) || StringUtil.isNotEmpty(dto.getDevFacade()) || StringUtil.isNotEmpty(dto.getOnlineStatus()) || StringUtil.isNotEmpty(dto.getDevLockStatus()) || StringUtil.isNotEmpty(dto.getPtzLockStatus())) {
            List<VideoTreeVO> voArrayList2 = voArrayList1.stream().filter((VideoTreeVO s) -> s.getPid().equals(dto.getId()) && s.getType() == 1).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(dto.getChnAbility())) {
                voArrayList1 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getChnAbility().contains(s.getChnAbility() + "")).collect(Collectors.toList()));
                voArrayList3 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getChnAbility().contains(s.getChnAbility() + "") && s.getPid().equals(dto.getId()) && s.getType() == 2).collect(Collectors.toList()));
            }
            if (StringUtil.isNotEmpty(dto.getDevFacade())) {
                voArrayList1 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getDevFacade().contains(s.getFacade() + "")).collect(Collectors.toList()));
                voArrayList3 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getDevFacade().contains(s.getFacade() + "")  && s.getType() == 2).collect(Collectors.toList()));
            }
            if (StringUtil.isNotEmpty(dto.getOnlineStatus())) {
                voArrayList1 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getOnlineStatus().contains(s.getStatus() + "")).collect(Collectors.toList()));
                voArrayList3 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getOnlineStatus().contains(s.getStatus() + "") && s.getPid().equals(dto.getId()) && s.getType() == 2).collect(Collectors.toList()));
            }
            if (StringUtil.isNotEmpty(dto.getDevLockStatus())) {
                voArrayList1 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getDevLockStatus().contains(s.getDevLockStatus() + "")).collect(Collectors.toList()));
                voArrayList3 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getDevLockStatus().contains(s.getDevLockStatus() + "") && s.getPid().equals(dto.getId()) && s.getType() == 2).collect(Collectors.toList()));
            }
            if (StringUtil.isNotEmpty(dto.getPtzLockStatus())) {
                voArrayList1 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getPtzLockStatus().equals(s.getPtzLockStatus() + "")).collect(Collectors.toList()));
                voArrayList3 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getPtzLockStatus().equals(s.getPtzLockStatus() + "") && s.getPid().equals(dto.getId()) && s.getType() == 2).collect(Collectors.toList()));
            }
            for (VideoTreeVO treeVO : voArrayList2) {
                Integer total = 0;
                Integer onlineStatus = 0;
                treeVO.setTotalNum(0);
                treeVO.setOnlineNum(0);
                for (VideoTreeVO chanVo : voArrayList1) {
                    String[] split = chanVo.getParentPathId().split("/");
                    List<String> pathIds = Arrays.asList(split);
                    if (pathIds.contains(treeVO.getId() + "")) {
                        treeVO.setTotalNum(total += 1);
                        if (chanVo.getStatus() == 0) {
                            treeVO.setOnlineNum(onlineStatus += 1);
                        }
                    }
                }
            }

            voArrayList2.addAll(voArrayList3);
            Collections.sort(voArrayList2, Comparator.comparingInt(VideoTreeVO::getId));
            return voArrayList2;
        } else if (dto.getId() == -1) {//-1为树的最初展开 过滤当前监所的树
            Integer allNum = 0; //初始化总数
            Integer totalNum = 0; //初始化总数
            Integer onlineNum = 0; //初始化在线总数
            if(prisonType!=null&&prisonType==JGJG_TYPE_JGZD){//支队情况
                    for (VideoTreeVO videoTreeVO : voArrayList1) {
                        if ( videoTreeVO.getPid() == 1) {
                            voArrayList.add(videoTreeVO);
                            allNum += videoTreeVO.getAllNum();
                            totalNum += videoTreeVO.getTotalNum();
                            onlineNum += videoTreeVO.getOnlineNum();
                        }
                    }

            }else {
                for (VideoTreeVO videoTreeVO : voArrayList1) {//非支队
                    if (dto.getPrisonId().equals(videoTreeVO.getPrisonId()) && videoTreeVO.getPid() == 1) {
                        voArrayList.add(videoTreeVO);
                        allNum += videoTreeVO.getAllNum();
                        totalNum += videoTreeVO.getTotalNum();
                        onlineNum += videoTreeVO.getOnlineNum();
                    }
                }
            }
            for (VideoTreeVO videoTreeVO : voArrayList1) {
                if (videoTreeVO.getId() != null && videoTreeVO.getPid().equals(-1)) {
                    voArrayList.add(videoTreeVO);
                    videoTreeVO.setAllNum(allNum);
                    videoTreeVO.setTotalNum(totalNum);
                    videoTreeVO.setOnlineNum(onlineNum);
                }

            }
            Collections.sort(voArrayList, Comparator.comparingInt(VideoTreeVO::getId));
            return voArrayList;
        } else {
            for (VideoTreeVO videoTreeVO : voArrayList1) {
                if (videoTreeVO.getId() != null && videoTreeVO.getPid().equals(dto.getId())) {
                    voArrayList.add(videoTreeVO);
                }
            }
            if (StringUtil.isNotEmpty(dto.getRoomId())) {
                voArrayList1 = (voArrayList1.stream().filter((VideoTreeVO s) -> dto.getRoomId().equals(s.getGb28181Code())).collect(Collectors.toList()));
                voArrayList.addAll(voArrayList1);
            }
            return voArrayList;
        }

    }

    @Override
    public List<VideoTreeVO> getVideoByPathId(VideoPageDto dto) throws Exception {
        ArrayList<VideoTreeVO> voArrayList = new ArrayList<>();
        String[] split = dto.getParentPathId().split("/");
        Integer endLevel = split.length;
        Integer beginLevel = 0;
        StringBuilder pathStart = new StringBuilder("");
        for (int i = 0; i < split.length; i++) {
            if (dto.getPathId().equals(split[i])) {
                pathStart.append(split[i]);
                beginLevel = i + 1;
                break;
            } else {
                pathStart.append(split[i]).append("/");
            }
        }
        Integer forCount = endLevel - beginLevel;
        for (VideoTreeVO videoTreeVO : videoTreeVOS) {
            if (videoTreeVO.getTreeLevel() >= beginLevel && videoTreeVO.getTreeLevel() <= endLevel && videoTreeVO.getParentPathId().contains(pathStart.toString())) {
                String[] split1 = videoTreeVO.getParentPathId().split("/");
                Integer treeLevel = videoTreeVO.getTreeLevel();
                for (int i = 0; i <= forCount; i++) {
                    if (treeLevel == beginLevel + i && split[beginLevel + i - 1].equals(split1[beginLevel + i - 1])) {
                        voArrayList.add(videoTreeVO);
                    }
                }
            }
        }
        return voArrayList;
    }

    @Override
    public PageResult getVideoByPage(VideoPageDto dto) throws Exception {
        ArrayList<VideoTreeVO> voArrayList = new ArrayList<>();
        String channelName = dto.getChannelName();
        List<Integer> pidList = new ArrayList<>();
        if (StringUtil.isNotEmpty(channelName)) {
            for (VideoTreeVO vo : videoTreeVOS) {
                //模糊搜索符合区域的节点
                if (StringUtil.isNotEmpty(vo.getName())&&vo.getName().contains(channelName)) {
                    if (vo.getType() == 1 && dto.getType() != 2) {
                        pidList.add(vo.getId());
                    } else if (vo.getType() == 2 && dto.getType() != 3) {
                        voArrayList.add(vo);
                    }
                } else {
                    if (pidList.contains(vo.getPid()) && vo.getType() == 2 && dto.getType() != 2) {
                        voArrayList.add(vo);
                    }
                }
            }
        }
        //List<VideoTreeVO> collect = voArrayList.stream().filter((DistinctUtils.distinctByKey(VideoTreeVO::getId))).collect(Collectors.toList());
        //PageResult listToPageData = ListToPageDataUtils.getListToPageData(dto.getCurPage(), dto.getPageSize(), collect);
        //return listToPageData;
        return null;
    }

    @Override
    public List<VideoTreeVO> getVideoListById(String id, Integer num,String prisonId) {
        List<VideoTreeVO> voArrayList1 = new ArrayList<>();
        String pathStart = null;
        Integer prisonType = 0;//commonDao.getPrisonType(prisonId);
        if(prisonType!=null&&prisonType==JGJG_TYPE_JGZD){//机构类型：监管支队
            /*List<String> prisonListByZd = videoSplitScreenMapper.getPrisonListByZd(prisonId);
            voArrayList1 = videoTreeVOS.stream()
                    .filter(videoTreeVO -> prisonListByZd.contains(videoTreeVO.getPrisonId())||videoTreeVO.getId()==1)
                    .collect(Collectors.toList());*/
        }else {
            voArrayList1 = videoTreeVOS.stream()
                    .filter(videoTreeVO -> prisonId.equals(videoTreeVO.getPrisonId())||videoTreeVO.getId()==1)
                    .collect(Collectors.toList());
        }
        List<VideoTreeVO> list = new ArrayList<>();
        List<VideoTreeVO> result = voArrayList1.stream()
                .filter(vo -> vo.getId().toString().equals(id))
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(result)&&!"1".equals(id)){
           pathStart = result.get(0).getParentPathId()+"/"+id;
        }else {
            pathStart = "1/";
        }
        if(StringUtil.isNotEmpty(pathStart)){
            for (VideoTreeVO videoTreeVO : voArrayList1) {
                if ( videoTreeVO.getParentPathId().contains(pathStart)&& videoTreeVO.getType() == 2) {
                   list.add(videoTreeVO);
                }
            }
        }
        if (!list.isEmpty()&&num>0) {
            int endIndex = Math.min(num, list.size());
            list = list.subList(0, endIndex);
        }
        return list;
    }

    //分组获取关联设备数据
    public List<VideoTreeVO> getVideoTreeVOS() {
        List<VideoTreeVO> list = videoTreeVOS.stream().filter(o -> o.getType() == 2).collect(Collectors.toList());
        return list;
    }


    @Override
    public JSONObject recordFileOperRequestTransfer(RecordFileOperRequestTransferDTO transferDTO) {
        String str = null;
        try {
            transferDTO.setOperType(2);
            transferDTO.setRelevantReason(-1);
            transferDTO.setChannelId(transferDTO.getChanId());
            transferDTO.setId(transferDTO.getChanId());
            transferDTO.setStreamAgentType("6");
            str = dealDto(transferDTO);
        } catch (Exception e) {
            log.info(e.toString());
        }
        String s = ajaxWdaRequest(recordFileOperRequestTransfer, str);
        JSONObject jsonObject = JSONObject.parseObject(s);
        return jsonObject;
    }

    @Override
    public List<QueryRecordRequestVO> queryRecordRequest(QueryRecordRequestDTO dto) throws Exception {
        dto.setChannelIds(dto.getChanId());
        dto.setLocked(0);
        dto.setPageNum(500);
        dto.setPageStart(1);
        dto.setRelevantReason(-1);
        String str = dealDto(dto);
        String s = ajaxWdaRequest(queryRecordRequest, str);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject message = jsonObject.getJSONObject("message");
        String rcdInfoList = message.getString("rcdInfoList");
        List<QueryRecordRequestVO> queryRecordRequestVOS = JSONObject.parseArray(rcdInfoList, QueryRecordRequestVO.class);
        List<QueryRecordRequestVO> list = new ArrayList<>();
        if (StringUtil.isNotEmpty(dto.getStartTime())) {
            /*Date dd = DateUtils.stringToDate(dto.getStartTime(), DateUtils.DATE_TIME_PATTERN);
            String dd1 = DateUtils.format(dd, "yyyy-MM-dd") + " 00:00:00";
            String dd2 = DateUtils.format(dd, "yyyy-MM-dd") + " 23:59:59";
            Date date = DateUtils.stringToDate(dd1, DateUtils.DATE_TIME_PATTERN);
            Date date1 = DateUtils.stringToDate(dd2, DateUtils.DATE_TIME_PATTERN);
            String endTime = "";
            String startTime = "";
            Integer count = 0;

            for (QueryRecordRequestVO vo : queryRecordRequestVOS) {
                Date rcdStartDate = DateUtils.stringToDate(vo.getRcdStartTime(), DateUtils.DATE_TIME_PATTERN);
                Date rcdEndDate = DateUtils.stringToDate(vo.getRcdEndTime(), DateUtils.DATE_TIME_PATTERN);
                rcdStartDate = rcdStartDate.before(date) ? date : rcdStartDate.after(date1) ? date1 : rcdStartDate;
                rcdEndDate = rcdEndDate.before(date) ? date : rcdEndDate.after(date1) ? date1 : rcdEndDate;
                vo.setRcdStartTime(DateUtils.format(rcdStartDate, DateUtils.DATE_TIME_PATTERN));
                vo.setRcdEndTime(DateUtils.format(rcdEndDate, DateUtils.DATE_TIME_PATTERN));
                if (!endTime.equals(vo.getRcdStartTime())) {
                    startTime = vo.getRcdStartTime();
                    endTime = vo.getRcdEndTime();
                    QueryRecordRequestVO vo1 = new QueryRecordRequestVO();
                    vo1.setRcdStartTime(startTime);
                    vo1.setRcdEndTime(endTime);
                    vo1.setRelevantReason(vo.getRelevantReason());
                    vo1.setChanId(vo.getChanId());
                    vo1.setName(vo.getName());
                    list.add(vo1);
                    count++;
                } else {
                    endTime = vo.getRcdEndTime();
                    QueryRecordRequestVO vo1 = list.get(count - 1);
                    vo1.setRcdEndTime(vo.getRcdEndTime());
                }
            }*/
        }
        return list;
    }

    @Override
    public String govDevChnnInfoAv(String chanId) {
        HashMap<String, String> map = new HashMap<>();
        map.put("channelId", chanId);
        try {
            String param = dealDto(map);
            String result = sendGet(govDevChnnInfoAv, "?" + param);
            return result;
        } catch (Exception e) {
            log.info(e.toString());
        }
        return null;
    }

    @Override
    public String govDevChnnInfo(String chanId) {
        HashMap<String, String> map = new HashMap<>();
        map.put("channelId", chanId);
        try {
            String param = dealDto(map);
            String result = sendGet(govDevChnnInfo, "?" + param);
            return result;
        } catch (Exception e) {
            log.info(e.toString());
        }
        return null;
    }

    @Override
    public DevChnnInfoVO govChanInfo(String chanId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("type", "request");
        map.put("action", "GetSingleDevChnnRequest");
        map.put("sequenceId", "2");
        JSONObject message = new JSONObject();
        message.put("type", "1");
        message.put("id", chanId);
        map.put("message", message);
        String s = "";
        DevChnnInfoVO devChnnInfoVO = new DevChnnInfoVO();
        try {
            String s1 = JSONObject.toJSONString(map);
            s = ajaxWdaRequest2(getSingleDevChnnRequest, s1, getUapToken());
            JSONObject jsonObject = JSONObject.parseObject(s);
            JSONObject message1 = jsonObject.getJSONObject("message");
            List<DevChnnInfoVO> chanInfoList = JSONObject.parseArray(message1.getString("chanInfoList"), DevChnnInfoVO.class);

            HashMap<String, String> map1 = new HashMap<>();
            map1.put("channelId", chanId);
            String param = dealDto(map1);
            String result = sendGet(govDevChnnInfo, "?" + param);
            JSONObject jsonObject1 = JSONObject.parseObject(result);
            JSONObject data = jsonObject1.getJSONObject("data");
            if (!CollectionUtils.isEmpty(chanInfoList)) {
                chanInfoList.get(0).setTreeCodeName(data.getString("treeCodeName"));
                chanInfoList.get(0).setPlatformName(data.getString("platformName"));
            }
            String devChnnInfoAv = govDevChnnInfoAv(chanId);
            JSONObject devChnnInfoAvJsonObject = JSONObject.parseObject(devChnnInfoAv);
            List<DevChnnInfoAvVO> devAvList = JSONObject.parseArray(devChnnInfoAvJsonObject.getString("data"), DevChnnInfoAvVO.class);
            chanInfoList.get(0).setDevAvList(devAvList);
            return chanInfoList.get(0);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return devChnnInfoVO;
    }

    /*@Override
    public AreaPrisonRoomPageResult getRoomInfoByChanId(String chanId) {
        AreaPrisonRoomPageResult roomInfoByChanId = videoSplitScreenMapper.getRoomInfoByChanId(chanId);
        return roomInfoByChanId;
    }*/

    String dealDto(Object dto) throws Exception {
        Map<String, Object> map = null;
        if (dto instanceof Map) {
            map = (Map<String, Object>) dto;
        } else {
            map = beanTomap(dto);
        }
        StringBuffer buffer = new StringBuffer();
        for (Map.Entry<String, Object> map1 : map.entrySet()) {
            String key = map1.getKey();
            String value = map1.getValue() == null ? null : map1.getValue().toString();
            if (key != null && value != null) {
                buffer.append(key).append("=").append(value).append("&");
            }
        }
        String str = null;
        if (buffer.length() > 0) {
            str = buffer.substring(0, buffer.length() - 1);
            str = str.replaceAll(" ", "%20");
        }
        return str;
    }

    public String doFormDataPost(String url, Map<String, Object> map) throws Exception {
        String result = "";
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(550000).setConnectTimeout(550000).setConnectionRequestTimeout(550000).setStaleConnectionCheckEnabled(true).build();
        client = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
        // client = HttpClients.createDefault();
        URIBuilder uriBuilder = new URIBuilder(url);

        HttpPost httpPost = new HttpPost(uriBuilder.build());
        httpPost.setHeader("Connection", "Keep-Alive");
        httpPost.setHeader("Charset", Charset.defaultCharset().name());
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        if( map.get("token")!=null){
            httpPost.setHeader("Authorization", map.get("token").toString());
        }
        map.remove("token");
        Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
        List<NameValuePair> params = new ArrayList<NameValuePair>();
        while (it.hasNext()) {
            Map.Entry<String, Object> entry = it.next();
            NameValuePair pair = new BasicNameValuePair(entry.getKey(), entry.getValue().toString());
            params.add(pair);
        }

        httpPost.setEntity(new UrlEncodedFormEntity(params, Charset.defaultCharset()));

        // 最多重试3次
        int maxRetries = 3;
        int retryCount = 0;

        try {
            while (retryCount < maxRetries) {
                response = client.execute(httpPost);
                if (response != null) {
                    HttpEntity resEntity = response.getEntity();
                    if (resEntity != null) {
                        result = EntityUtils.toString(resEntity, Charset.defaultCharset());
                    }
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    // 检查是否是401未授权错误
                    if (jsonObject != null && jsonObject.containsKey("code") && "401".equals(jsonObject.getString("code"))) {
                        retryCount++;
                        if (retryCount < maxRetries) {
                            // 更新token后重试
                            httpPost.setHeader("Authorization", getUapTokenFeNew());
                            continue; // 重试
                        } else {
                            // 3次重试后仍然失败，跳出循环
                            log.error("请求token失败3次重试后仍然失败，响应结果: {}", result);
                            break;
                        }
                    } else {
                        log.error("请求失败，响应结果: {}", result);
                        // 不是401错误或者成功响应，直接返回结果
                        break;
                    }
                }
            }
        } catch (ClientProtocolException e) {
            throw new RuntimeException("创建连接失败" + e);
        } catch (IOException e) {
            throw new RuntimeException("创建连接失败" + e);
        }

        return result;
    }

    public void init() throws Exception { //程序启动后会自动执行该方法
        //设置状态为同步中
        try {
            JedisConnectionFactory  connectionFactory = (JedisConnectionFactory) redisTemplate.getConnectionFactory();
            //if (connectionFactory != null) {
            //    connectionFactory.setDatabase(2);
            //    /**
            //     * 在重置db之后:connectionFactory.setDatabase(i);
            //     * 需要将链接刷新:connectionFactory.afterPropertiesSet();
            //     */
            //    connectionFactory.afterPropertiesSet();
            //    this.redisTemplate.setConnectionFactory(connectionFactory);
            //    connectionFactory.afterPropertiesSet();
            //}
            redisTemplate.opsForValue().set("isSysVideoTree", 1);
            String prisonId = null;
            String prisonId2 = null;
            //获取父节点
            Map<String, Object> map = new HashMap<>();
            map.put("username", uapUserName);
            map.put("password", uapPassword);
            if (StringUtil.isNotEmpty(getUapToken())) {
                String uapToken = getUapToken();
                map.put("token", uapToken);
                if("001".equals(rootCode)){
                    map.put("treeCode", "");
                }else {
                    map.put("treeCode", "001");
                }
                String s = doFormDataPost(getChannelTree, map);
                JSONObject jsonObject = JSONObject.parseObject(s);
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    videoTreeVOS.clear();
                    JSONArray treeNodeList = data.getJSONArray("treeNodeList");
                    JSONObject parentObject = treeNodeList.getJSONObject(0);
                    String treeCode = parentObject.get("treeCode").toString();
                    //加入父节点到我的返回集合中
                    VideoTreeVO videoTreeVO1 = new VideoTreeVO();
                    videoTreeVO1.setId(1);
                    videoTreeVO1.setTreeCode(treeCode);
                    videoTreeVO1.setType(1);
                    videoTreeVO1.setTreeLevel(0);
                    videoTreeVO1.setPid(-1);
                    videoTreeVO1.setParentPathId("1");
                    videoTreeVO1.setName(parentObject.get("name").toString());
                    videoTreeVO1.setAllNum(Integer.valueOf(parentObject.get("allNum").toString()));
                    videoTreeVO1.setIsLocked(Integer.valueOf(parentObject.get("isLocked").toString()));
                    videoTreeVO1.setOnlineNum(Integer.valueOf(parentObject.get("onlineNum").toString()));
                    videoTreeVO1.setPlatformId(Integer.valueOf(parentObject.get("platformId").toString()));
                    videoTreeVO1.setTotalNum(Integer.valueOf(parentObject.get("totalNum").toString()));
                    videoTreeVOS.add(videoTreeVO1);
                    //获取第一层所有父节点区域
                    map.put("treeCode", treeCode);
                    map.put("token", uapToken);
                    String s1 = doFormDataPost(getChannelTree, map);
                    JSONObject allParentObject = JSONObject.parseObject(s1);
                    JSONObject data2 = allParentObject.getJSONObject("data");
                    String treePathIds = null;
                    String treePathName = null;
                    Integer treeLevel = 0;
                    treeJsonArray = data2.getJSONArray("treeNodeList");
                    log.info("treeJsonArray值为========》" + treeJsonArray.toJSONString());
                    //初始化ID
                    Integer id = 0 + 2;
                    //List<String> prisonList = videoSplitScreenMapper.getPrisonList(DictionaryDataCodeConstants.JGJG_TYPE_KSS);
                    Iterator<Object> iterator = treeJsonArray.iterator();
                    //遍历一遍找到我们平台的子节点
                    while (iterator.hasNext()) {
                        JSONObject treeJsonObject = (JSONObject) iterator.next();
                        if (treeJsonObject.get("remark") != null) {
                            String remark = treeJsonObject.get("remark").toString();
                            /*if (!prisonList.contains(remark)) {
                                iterator.remove();
                            }*/
                        }
                    }
                    Iterator<Object> iterator1 = treeJsonArray.iterator();
                    //遍历所有父节点的区域集合并赋值ID
                    while (iterator1.hasNext()) {
                        log.info("=================>开始循环同步设备");
                        JSONObject treeJsonObject = (JSONObject) iterator1.next();
                        JSONArray treeCodeList = getTreeList(treeJsonObject.get("treeCode").toString());
                        VideoTreeVO videoTreeVO = new VideoTreeVO();
                        videoTreeVO.setId(id);
                        videoTreeVO.setTreeCode(treeJsonObject.get("treeCode").toString());
                        videoTreeVO.setAllNum(Integer.valueOf(treeJsonObject.get("allNum").toString()));
                        videoTreeVO.setGb28181Code(treeJsonObject.get("gb28181Code")!=null?treeJsonObject.get("gb28181Code").toString():"");
                        videoTreeVO.setIsLocked(Integer.valueOf(treeJsonObject.get("isLocked").toString()));
                        videoTreeVO.setOnlineNum(Integer.valueOf(treeJsonObject.get("onlineNum").toString()));
                        videoTreeVO.setPlatformId(Integer.valueOf(treeJsonObject.get("platformId").toString()));
                        videoTreeVO.setTotalNum(Integer.valueOf(treeJsonObject.get("totalNum").toString()));
                        if(treeJsonObject.get("remark")!=null){
                            prisonId = treeJsonObject.get("remark").toString();
                        }
                        videoTreeVO.setPrisonId(StringUtil.isNullBlank(prisonId)?prisonId2:prisonId);
                        videoTreeVO.setType(1);
                        //视频联网新老本版本升级适配 001=v1.3.2 以前版本
                        Map<String, String> map1 = new HashMap<>();
                        if("001".equals(rootCode)){
                            treeLevel = getTreeLevel(treeJsonObject.get("treeCode").toString(), 1);
                            map1 = getTreePathIds(treeJsonObject.get("treeCode").toString(), treeLevel);
                        }else {
                            treeLevel = getTreeLevel(treeJsonObject.get("treeCode").toString(), 3);
                            map1 = getTreePathIds2(treeJsonObject.get("treeCode").toString(), treeLevel);
                        }
                        treePathIds = map1.get("pathId");
                        treePathName = map1.get("pathName");
                        videoTreeVO.setTreeLevel(treeLevel);
                        videoTreeVO.setParentPathId(treePathIds);
                        videoTreeVO.setPathName(treePathName);
                        videoTreeVO.setPid(Integer.valueOf(treePathIds.substring(treePathIds.lastIndexOf("/") + 1)));
                        videoTreeVO.setName(treeJsonObject.get("name").toString());
                        videoTreeVOS.add(videoTreeVO);
                        JSONArray chanInfoList = getChanInfoList(treeJsonObject.get("treeCode").toString());
                        for (int j = 0; j < chanInfoList.size(); j++) {
                            JSONObject chanInfoJsonObject = chanInfoList.getJSONObject(j);
                            //修改通道号对应的设备在线状态
                            String status = chanInfoJsonObject.get("status").toString().equals("0") ? "001" : "002";
                            /*videoSplitScreenMapper.updateDeviceStatus(chanInfoJsonObject.get("chanId").toString(), status);
                            videoSplitScreenMapper.updateChanDeviceStatus(chanInfoJsonObject.get("chanId").toString(), Integer.valueOf(chanInfoJsonObject.get("facade").toString()));
                            videoSplitScreenMapper.updateDeviceGbCode(chanInfoJsonObject.get("chanId").toString(),chanInfoJsonObject.get("gb28181Code").toString());*/
                            VideoTreeVO treeVO = new VideoTreeVO();
                            treeVO.setId(id + j + 1);
                            treeVO.setTreeCode(chanInfoJsonObject.get("treeCode").toString());
                            treeVO.setType(2);
                            if (j == 0) {
                                //视频联网新老本版本升级适配 001=v1.3.2 以前版本
                                Map<String, String> stringMap = new HashMap<>();
                                if("001".equals(rootCode)){
                                    treeLevel = getTreeLevel(chanInfoJsonObject.get("treeCode").toString(), 2);
                                    stringMap = getTreePathIds(chanInfoJsonObject.get("treeCode").toString(), treeLevel);
                                }else {
                                    treeLevel = getTreeLevel(chanInfoJsonObject.get("treeCode").toString(), 1);
                                    stringMap = getTreePathIds2(chanInfoJsonObject.get("treeCode").toString(), treeLevel);
                                }
                                treePathIds = stringMap.get("pathId");
                                treePathName = stringMap.get("pathName");
                            }
                            treeVO.setTreeLevel(treeLevel);
                            treeVO.setParentPathId(treePathIds);
                            treeVO.setPathName(treePathName);
                            treeVO.setPid(Integer.valueOf(treePathIds.substring(treePathIds.lastIndexOf("/") + 1)));
                            if(chanInfoJsonObject.get("chanNameAbbr")!=null){
                                treeVO.setName(chanInfoJsonObject.get("chanNameAbbr").toString());
                            }
                            treeVO.setChanId(Long.valueOf(chanInfoJsonObject.get("chanId").toString()));
                            treeVO.setChanTypeId(Integer.valueOf(chanInfoJsonObject.get("chanTypeId").toString()));
                            treeVO.setChnAbility(chanInfoJsonObject.get("chnAbility").toString());
                            treeVO.setControlType(Integer.valueOf(chanInfoJsonObject.get("controlType").toString()));
                            treeVO.setDefaultStreamType(Integer.valueOf(chanInfoJsonObject.get("defaultStreamType").toString()));
                            treeVO.setDevId(Long.valueOf(chanInfoJsonObject.get("devId").toString()));
                            treeVO.setDevLockStatus(Integer.valueOf(chanInfoJsonObject.get("devLockStatus").toString()));
                            treeVO.setFacade(Integer.valueOf(chanInfoJsonObject.get("facade").toString()));
                            treeVO.setDevLockStatus(Integer.valueOf(chanInfoJsonObject.get("devLockStatus").toString()));
                            treeVO.setPtzLockStatus(Integer.valueOf(chanInfoJsonObject.get("ptzLockStatus").toString()));
                            treeVO.setGb28181Code(chanInfoJsonObject.get("gb28181Code").toString());
                            treeVO.setIsFocus(Integer.valueOf(chanInfoJsonObject.get("isFocus").toString()));
                            treeVO.setPlatformId(Integer.valueOf(chanInfoJsonObject.get("platformId").toString()));
                            treeVO.setPtzLockStatus(Integer.valueOf(chanInfoJsonObject.get("ptzLockStatus").toString()));
                            treeVO.setStatus(Integer.valueOf(chanInfoJsonObject.get("status").toString()));
                            treeVO.setStorageType(Integer.valueOf(chanInfoJsonObject.get("storageType").toString()));
                            treeVO.setUsageType(Integer.valueOf(chanInfoJsonObject.get("usageType").toString()));
                            treeVO.setPrisonId(StringUtil.isEmpty(prisonId)?prisonId2:prisonId);
                            videoTreeVOS.add(treeVO);
                        }
                        iterator1.remove();
                        if (treeCodeList.size() > 0) {
                            treeCodeList.addAll(treeJsonArray);
                            treeJsonArray = treeCodeList;
                            if(StringUtil.isNotEmpty(prisonId)){
                                prisonId2 = prisonId;
                            }
                            iterator1 = treeJsonArray.iterator();
//                treeJsonArray.addAll(treeCodeList);
                        }
                        id += chanInfoList.size() + 1;
                    }
                    for (VideoTreeVO videoTreeVO : videoTreeVOS) {
                        Integer i = 0;
                        if (videoTreeVO.getType() == 1) {
                            for (VideoTreeVO vo : videoTreeVOS) {
                                if (videoTreeVO.getId().equals(vo.getPid())) {
                                    i++;
                                }
                                videoTreeVO.setChildNumber(i);
                            }
                        }
                    }
                }
                redisTemplate.opsForValue().set("isSysVideoTree", 0);
            }
        }catch (Exception e){
            log.error(e.toString());
        }
        
    }

    public void initNew(String prisonId){
        videoTreeVOS.clear();
       // videoTreeVOS = videoSplitScreenMapper.getTreeList(null);
        for (VideoTreeVO videoTreeVO : videoTreeVOS) {
            Integer i = 0;
            if (videoTreeVO.getType() == 1) {
                for (VideoTreeVO vo : videoTreeVOS) {
                    if (videoTreeVO.getId().equals(vo.getPid())) {
                        i++;
                    }
                    videoTreeVO.setChildNumber(i);
                }
            }
        }
    }



    @Override
    public String addTreeNodeRequest(AddTreeNodeRequestVO addTreeNodeRequestVO) {
        Map<String, Object> map = new HashMap<>();
        map.put("action", "AddTreeNodeRequest");
        map.put("sequenceId", "123");
        map.put("token", "gxx");
        map.put("type", "request");
        JSONObject message = new JSONObject();
        message.put("gb28181Code", addTreeNodeRequestVO.getGb28181Code());
        message.put("name", addTreeNodeRequestVO.getName());
        message.put("parentTreeCode", addTreeNodeRequestVO.getParentTreeCode());
        message.put("remark", addTreeNodeRequestVO.getPrisonId());
        map.put("message", message);
        String s = JSON.toJSONString(map);
        String s2 = ajaxWdaRequest2(addTreeNodeRequest, s, getUapToken());
        return s2;
    }

    @Override
    public String clearTreeNodeRequest(AddTreeNodeRequestVO addTreeNodeRequestVO) {
        //获取第一层所有父节点区域
        Map<String, Object> map1 = new HashMap<>();
        map1.put("treeCode", getParentTreeCode());
        map1.put("token", getUapToken());
        String lev1 = null;
        try {
            lev1 = doFormDataPost(getChannelTree, map1);
        } catch (Exception e) {
            log.info(e.toString());
        }
        JSONObject allParentObject = JSONObject.parseObject(lev1);
        JSONObject data2 = allParentObject.getJSONObject("data");
        JSONArray treeNodeList = data2.getJSONArray("treeNodeList");
        if(!treeNodeList.isEmpty()){
            Iterator<Object> iterator = treeNodeList.stream().iterator();
            while (iterator.hasNext()){
                JSONObject next = (JSONObject) iterator.next();
                if(next.getString("name").equals(addTreeNodeRequestVO.getName())){
                     addTreeNodeRequestVO.setParentTreeCode(next.getString("treeCode"));
                }
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("action", "ClearTreeNodeRequest");
        map.put("sequenceId", "123");
        map.put("token", "gxx");
        map.put("type", "request");
        JSONObject message = new JSONObject();
        message.put("treeCode", addTreeNodeRequestVO.getParentTreeCode());
        map.put("message", message);
        String s = JSON.toJSONString(map);
        String s2 = ajaxWdaRequest2(clearTreeNodeRequest, s, getUapToken());
        JSONObject result = JSONObject.parseObject(s2);
        JSONObject resultMessage = result.getJSONObject("message");
        Integer operResult = resultMessage.getInteger("operResult");
        if (operResult == 0) {
            log.info("父节点清空成功---------------》" + addTreeNodeRequestVO.getParentTreeCode());
            String s1 = deleteTreeNodeRequest(addTreeNodeRequestVO.getParentTreeCode());
            result = JSONObject.parseObject(s1);
            resultMessage = result.getJSONObject("message");
            operResult = resultMessage.getInteger("operResult");
            if (operResult == 0) {
                log.info("父节点删除成功---------------》" + addTreeNodeRequestVO.getParentTreeCode());
            }
        }
        return s2;
    }

    @Override
    public String moveTreeNodeLeafRequest(AddTreeNodeRequestVO addTreeNodeRequestVO) {
        Map<String, Object> map = new HashMap<>();
        map.put("action", "MoveTreeNodeLeafRequest");
        map.put("sequenceId", "123");
        map.put("token", "gxx");
        map.put("type", "request");
        JSONObject message = new JSONObject();
        message.put("srcTreeCode", addTreeNodeRequestVO.getParentTreeCode());
        message.put("dstTreeCode", addTreeNodeRequestVO.getDstTreeCode());
        message.put("isCopy", 1);
        Long[] strings = new Long[1];
        strings[0] = Long.valueOf(addTreeNodeRequestVO.getGb28181Code());
        message.put("objIdList", strings);
        map.put("message", message);
        String s = JSON.toJSONString(map);
        String s2 = ajaxWdaRequest2(moveTreeNodeLeafRequest, s, getUapToken());
        JSONObject result = JSONObject.parseObject(s2);
        JSONObject resultMessage = result.getJSONObject("message");
        Integer operResult = resultMessage.getInteger("operResult");
        if (operResult == 0) {
            log.info("设备复制成功---------------》" + addTreeNodeRequestVO.getParentTreeCode() + "-------->" + addTreeNodeRequestVO.getDstTreeCode());
        }else {
            log.info("复制参数为:"+s+"报错信息为"+resultMessage.toJSONString());
        }
        return s2;
    }

    @Override
    public String deleteTreeNodeRequest(String treeCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("action", "DeleteTreeNodeRequest");
        map.put("sequenceId", "123");
        map.put("token", "gxx");
        map.put("type", "request");
        JSONObject message = new JSONObject();
        List<String> strings = new ArrayList<>();
        strings.add(treeCode);
        message.put("treeCodeList", strings);
        map.put("message", message);
        String s = JSON.toJSONString(map);
        String s2 = ajaxWdaRequest2(deleteTreeNodeRequest, s, getUapToken());
        return s2;
    }

    public JSONArray getTreeList(String treeCode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("username", uapUserName);
        map.put("password", uapPassword);
        String uapToken = getUapToken();
        map.put("token", uapToken);
        map.put("treeCode", treeCode);
        String s = doFormDataPost(getChannelTree, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray treeNodeList = data.getJSONArray("treeNodeList");
        return treeNodeList;
    }

    public JSONArray getChanInfoList(String treeCode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("username", uapUserName);
        map.put("password", uapPassword);
        String uapToken = getUapToken();
        map.put("token", uapToken);
        map.put("treeCode", treeCode);
        String s = doFormDataPost(getChannelTree, map);
        JSONObject jsonObject = JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray chanInfoList = data.getJSONArray("chanInfoList");
        return chanInfoList;
    }

    @Override
    public String getParentTreeCode() {
        //获取第一层所有父节点区域
        log.info("获取第一层所有父节点区域======>");
        //获取父节点
        Map<String, Object> map = new HashMap<>();
        map.put("username", uapUserName);
        map.put("password", uapPassword);
        String s = null;
        if (StringUtil.isNotEmpty(getUapToken())) {
            String uapToken = getUapToken();
            map.put("token", uapToken);
            if ("001001".equals(rootCode)) {
                map.put("treeCode", "001");
            } else {
                map.put("treeCode", "");
            }
            try {
                s = doFormDataPost(getChannelTree, map);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            log.info("父节点返回数据为" + s);
        }
        JSONObject allParentObject = JSONObject.parseObject(s);
        JSONObject data2 = allParentObject.getJSONObject("data");
        JSONArray treeNodeList = data2.getJSONArray("treeNodeList");
        JSONObject parentObject = treeNodeList.getJSONObject(0);
        String treeCode = parentObject.get("treeCode").toString();
        log.info("最顶级的父节点为=================》"+treeCode);
        return treeCode;
    }

    public JSONArray getSingleDevChnnRequest(String chanId) {
        JSONArray chanInfoList = new JSONArray();
        Map<String, Object> map = new HashMap<>();
        map.put("action", "GetSingleDevChnnRequest");
        map.put("sequenceId", "2");
        map.put("token", "gxx");
        map.put("type", "request");
        JSONObject message = new JSONObject();
        message.put("id", chanId);
        message.put("type", 1);
        map.put("message", message);
        String s = JSON.toJSONString(map);
        String s2 = ajaxWdaRequest2(getSingleDevChnnRequest, s, getUapToken());
        JSONObject result = JSONObject.parseObject(s2);
        JSONObject resultMessage = result.getJSONObject("message");
        Integer operResult = resultMessage.getInteger("operResult");
        if (operResult == 0) {
            chanInfoList = resultMessage.getJSONArray("chanInfoList");
        }
        return chanInfoList;
    }

    public void setDevChnnRequest(String chanId, String name, String treeCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("action", "SetDevChnnRequest");
        map.put("sequenceId", "2");
        map.put("token", "gxx");
        map.put("type", "request");
        map.put("chanCount", 1);
        JSONObject message = new JSONObject();
        JSONArray singleDevChnnRequest = getSingleDevChnnRequest(chanId);
        for (int i = 0; i < singleDevChnnRequest.size(); i++) {
            singleDevChnnRequest.getJSONObject(i).put("chanNameAbbr", name);
            singleDevChnnRequest.getJSONObject(i).put("treeCode", treeCode);
        }
        if (singleDevChnnRequest.size() > 0) {
            message.put("chanInfoList", singleDevChnnRequest);
            map.put("message", message);
            String s = JSON.toJSONString(map);
            ajaxWdaRequest2(setDevChnnRequest, s, getUapToken());
        }
    }

    public Integer getTreeLevel(String treeCode, Integer type) {
        if (type == 2) {
            return treeCode.length() / 3;
        }else if (type == 3) {
            return (treeCode.length() - 6) / 3;
        } else {
            return (treeCode.length() - 3) / 3;
        }
    }

    public Map<String, String> getTreePathIds(String treeCode, Integer treeLevel) {
        StringBuilder stringBuilder = new StringBuilder("1");
        StringBuilder stringBuilder2 = new StringBuilder("默认区域树");
        Map<String, String> map = new HashMap<>();
        if (treeLevel > 1) {
            for (int i = 2; i < treeLevel + 1; i++) {
                String substring = treeCode.substring(0, i * 3);
                Optional<VideoTreeVO> employeeOptional = videoTreeVOS.stream().filter(e -> e.getTreeCode().equals(substring)).findFirst();
                VideoTreeVO videoTreeVO = employeeOptional.get();
                stringBuilder.append("/").append(videoTreeVO.getId());
                stringBuilder2.append("/").append(videoTreeVO.getName());
            }
            map.put("pathId", stringBuilder.toString());
            map.put("pathName", stringBuilder2.toString());
            return map;
        } else {
            map.put("pathId", stringBuilder.toString());
            map.put("pathName", stringBuilder2.toString());
            return map;
        }


    }

    public Map<String, String> getTreePathIds2(String treeCode, Integer treeLevel) {
        StringBuilder stringBuilder = new StringBuilder("1");
        StringBuilder stringBuilder2 = new StringBuilder("默认区域树");
        Map<String, String> map = new HashMap<>();
        if (treeLevel > 1) {
            for (int i = 2; i < treeLevel + 1; i++) {
                String substring = treeCode.substring(0, (i + 1) * 3);
                Optional<VideoTreeVO> employeeOptional = videoTreeVOS.stream().filter(e -> e.getTreeCode().equals(substring)).findFirst();
                VideoTreeVO videoTreeVO = employeeOptional.get();
                stringBuilder.append("/").append(videoTreeVO.getId());
                stringBuilder2.append("/").append(videoTreeVO.getName());
            }
            map.put("pathId", stringBuilder.toString());
            map.put("pathName", stringBuilder2.toString());
            return map;
        } else {
            map.put("pathId", stringBuilder.toString());
            map.put("pathName", stringBuilder2.toString());
            return map;
        }


    }

    public Map<String, String> getTreePathIds3(String treeCode, Integer treeLevel) {
        StringBuilder stringBuilder = new StringBuilder("1");
        StringBuilder stringBuilder2 = new StringBuilder("默认区域树");
        Map<String, String> map = new HashMap<>();
        if (treeLevel > 1) {
            for (int i = 2; i < treeLevel + 1; i++) {
                String substring = treeCode.substring(0, (i + 1) * 3);
                VideoTreeVO videoTreeVO = splwTreeDao.getPathNameByTreeCode(substring);
                if(videoTreeVO==null){
                   log.info("视频联网treecode查找为null"+treeCode+"级别为"+treeLevel);
                   return null;
                }
                stringBuilder.append("/").append(videoTreeVO.getId());
                stringBuilder2.append("/").append(videoTreeVO.getName());
            }
            map.put("pathId", stringBuilder.toString());
            map.put("pathName", stringBuilder2.toString());
            return map;
        } else {
            map.put("pathId", stringBuilder.toString());
            map.put("pathName", stringBuilder2.toString());
            return map;
        }


    }

    /**
     * 深度复制list对象,先序列化对象，再反序列化对象
     *
     * @param src 需要复制的对象列表
     * @return 返回新的对象列表
     * @throws IOException            读取Object流信息失败
     * @throws ClassNotFoundException 泛型类不存在
     */
    public static <T> List<T> deepCopy(List<T> src) {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = null;
        try {
            out = new ObjectOutputStream(byteOut);
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            out.writeObject(src);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = null;
        try {
            in = new ObjectInputStream(byteIn);
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            return (List<T>) in.readObject();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getTreeCode(String url, String chanId,Map map) {
        try {
            map.put("token",getUapToken());
            String s = doFormDataPost(url, map);
            JSONObject jsonObject = JSONObject.parseObject(s);
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray rows = data.getJSONArray("rows");
            Iterator<Object> iterator = rows.stream().iterator();
            while (iterator.hasNext()){
                JSONObject next = (JSONObject) iterator.next();
                if(chanId.equals(next.getString("chanId"))){
                    return next.getString("treeCode");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getChannelIds(String chanId) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("token",getUapToken());
            map.put("channelList",chanId);
            String s = doFormDataPost(getChannelIds, map);
            JSONObject jsonObject = JSONObject.parseObject(s);
            JSONArray data = jsonObject.getJSONArray("data");
            if(!data.isEmpty()){
                return data.getJSONObject(0).getString("tree_code");
            }
        } catch (Exception e) {
            log.info(e.toString());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Integer getChannelStatus(String chanId) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("token",getUapToken());
            map.put("channelList",chanId);
            String s = doFormDataPost(getChannelIds, map);
            JSONObject jsonObject = JSONObject.parseObject(s);
            JSONArray data = jsonObject.getJSONArray("data");
            if(!data.isEmpty()){
                return data.getJSONObject(0).getInteger("status");
            }
        } catch (Exception e) {
            log.info(e.toString());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getAllDeviceRequest(String devId) {
        Map<String, Object> map = new HashMap<>();
        map.put("action", "GetAllDeviceRequest");
        map.put("sequenceId", "2");
        map.put("token", "gxx");
        map.put("type", "request");
        JSONObject message = new JSONObject();
        List<String> devIdList = new ArrayList<>();
        devIdList.add(devId);
        message.put("devIdList",devIdList);
        map.put("message",message);
        String s = JSON.toJSONString(map);
        String result = ajaxWdaRequest2(getAllDeviceRequest, s, getUapToken());
        return result;
    }


    @Override
    public void sysDeviceStatus() {
        for(VideoTreeVO vo:videoTreeVOS){
            if(vo.getType()==2){
                Integer channelStatus = getChannelStatus(vo.getChanId()+"");
                vo.setStatus(channelStatus);
            }
        }
    }

    /**
     * 根据区域编码，分层查询设备树信息，并支持多条件筛选通道
     * @param treeCode
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject getSplwAreaByTreeCode(String treeCode) throws Exception {
       Map<String, Object> map = new HashMap<>();
        /* map.put("username", uapUserName);
        map.put("password", uapPassword);*/
        JSONObject jsonObject = new JSONObject();
        if (StringUtil.isNotEmpty(getUapToken())) {
            String uapToken = getUapToken();
            map.put("token", uapToken);
            map.put("treeCode", treeCode);
            log.info("getSplwAreaByTreeCode 获取树信息参数：{},{}",uapToken,treeCode);
            String s = doFormDataPost(getChannelTree, map);
            log.info("getSplwAreaByTreeCode 获取树信息成功：{}", s);
            jsonObject = JSONObject.parseObject(s);
            jsonObject = jsonObject.getJSONObject("data");
        }
        return jsonObject;
    }

    @Override
    public String getRootCode() {
        return rootCode;
    }


    @Override
    @Async
    public void sysDeviceTree(String prisonId) {
        log.info("设备管理开始推送设备>>视频联网>>>>>>>>>>>>>开始");
        //baseDeviceService.videoTree(prisonId);
        log.info("设备管理开始推送设备>>视频联网>>>>>>>>>>>>>结束");
        try {
            log.info("开始同步视频联网数据");
            init();
            log.info("结束同步视频联网数据");
        } catch (Exception e) {
            log.info(e.toString());
        }
    }

}
