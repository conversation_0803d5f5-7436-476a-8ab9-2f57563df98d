package com.rs.module.base.vo.pm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.service.dch.enums.DataChangeEventStatusEnum;
import com.rs.module.base.service.dch.enums.DataChangeEventTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 数据变更事件日志VO
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "数据变更事件日志VO")
public class DataChangeEventLogVO extends BaseVO {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("事件类型")
    private DataChangeEventTypeEnum eventType;

    @ApiModelProperty("业务表名")
    private String tableName;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("主键ID")
    private String primaryKeyId;

    @ApiModelProperty("变更前数据")
    private String oldData;

    @ApiModelProperty("变更后数据")
    private String newData;

    @ApiModelProperty("事件状态")
    private DataChangeEventStatusEnum status;

    @ApiModelProperty("处理器类名")
    private String handlerClass;

    @ApiModelProperty("重试次数")
    private Integer retryCount;

    @ApiModelProperty("最大重试次数")
    private Integer maxRetryCount;

    @ApiModelProperty("错误信息")
    private String errorMessage;

    @ApiModelProperty("事件触发时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventTime;

    @ApiModelProperty("开始处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processStartTime;

    @ApiModelProperty("处理完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processEndTime;

    @ApiModelProperty("下次重试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextRetryTime;

    @ApiModelProperty("扩展信息")
    private String extendInfo;

    @ApiModelProperty("期望执行时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectationExecuteDate;
}
