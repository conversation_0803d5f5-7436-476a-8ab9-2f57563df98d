package com.rs.module.base.service.dch.handler;

import com.rs.module.base.service.dch.context.DataChangeEventContext;

/**
 * 数据变更事件处理器接口
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
public interface DataChangeEventHandler {

    /**
     * 获取处理器支持的业务类型
     *
     * @return 业务类型
     */
    String getSupportedBusinessType();

    /**
     * 获取处理器优先级（数字越小优先级越高）
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 是否支持处理该事件
     *
     * @param context 事件上下文
     * @return true-支持，false-不支持
     */
    boolean supports(DataChangeEventContext context);

    /**
     * 处理数据变更事件
     *
     * @param context 事件上下文
     * @return 处理结果
     */
    DataChangeEventHandlerResult handle(DataChangeEventContext context);

    /**
     * 处理失败时的回调
     *
     * @param context   事件上下文
     * @param exception 异常信息
     */
    default void onFailure(DataChangeEventContext context, Exception exception) {
        // 默认空实现
    }

    /**
     * 处理成功时的回调
     *
     * @param context 事件上下文
     * @param result  处理结果
     */
    default void onSuccess(DataChangeEventContext context, DataChangeEventHandlerResult result) {
        // 默认空实现
    }
}
