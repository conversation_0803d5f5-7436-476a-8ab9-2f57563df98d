package com.rs.module.base.service.dch.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.dch.context.DataChangeEventContext;
import com.rs.module.base.service.dch.enums.DataChangeEventTypeEnum;
import com.rs.module.base.service.pm.PrisonerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

/**
 * 数据变更事件处理器抽象基类
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
public abstract class AbstractDataChangeEventHandler implements DataChangeEventHandler {

    /**
     * 获取支持的表名列表
     *
     * @return 表名列表
     */
    protected abstract List<String> getSupportedTableNames();

    /**
     * 获取支持的事件类型
     *
     * @return 事件类型集合
     */
    protected abstract Set<DataChangeEventTypeEnum> getSupportedEventTypes();

    @Override
    public boolean supports(DataChangeEventContext context) {
        // 检查业务类型
        if (!getSupportedBusinessType().equals(context.getBusinessType())) {
            return false;
        }

        // 检查表名
        List<String> supportedTables = getSupportedTableNames();
        if (supportedTables != null && !supportedTables.isEmpty()) {
            if (!supportedTables.contains(context.getTableName())) {
                return false;
            }
        }

        // 检查事件类型
        Set<DataChangeEventTypeEnum> supportedEventTypes = getSupportedEventTypes();
        if (supportedEventTypes != null && !supportedEventTypes.isEmpty()) {
            if (!supportedEventTypes.contains(context.getEventType())) {
                return false;
            }
        }

        return true;
    }

    @Override
    public DataChangeEventHandlerResult handle(DataChangeEventContext context) {
        long startTime = System.currentTimeMillis();

        try {
            log.debug("开始处理数据变更事件: 业务类型={}, 表名={}, 事件类型={}, 主键ID={}", context.getBusinessType(), context.getTableName(), context.getEventType(), context.getPrimaryKeyId());

            // 执行具体的处理逻辑
            DataChangeEventHandlerResult result = doHandle(context);

            long processingTime = System.currentTimeMillis() - startTime;
            result.setProcessingTime(processingTime);

            if (result.isSuccess()) {
                log.debug("数据变更事件处理成功: 业务类型={}, 表名={}, 事件类型={}, 主键ID={}, 耗时={}ms", context.getBusinessType(), context.getTableName(), context.getEventType(), context.getPrimaryKeyId(), processingTime);
                onSuccess(context, result);
            } else {
                log.warn("数据变更事件处理失败: 业务类型={}, 表名={}, 事件类型={}, 主键ID={}, 错误信息={}, 耗时={}ms", context.getBusinessType(), context.getTableName(), context.getEventType(), context.getPrimaryKeyId(), result.getMessage(), processingTime);
            }

            return result;
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("数据变更事件处理异常: 业务类型={}, 表名={}, 事件类型={}, 主键ID={}, 耗时={}ms", context.getBusinessType(), context.getTableName(), context.getEventType(), context.getPrimaryKeyId(), processingTime, e);

            onFailure(context, e);

            DataChangeEventHandlerResult result = DataChangeEventHandlerResult.failure("处理异常: " + e.getMessage(), true);
            result.setProcessingTime(processingTime);
            return result;
        }
    }

    /**
     * 执行具体的处理逻辑
     *
     * @param context 事件上下文
     * @return 处理结果
     */
    protected abstract DataChangeEventHandlerResult doHandle(DataChangeEventContext context);

    /**
     * 获取变更前的字段值
     *
     * @param context   事件上下文
     * @param fieldName 字段名
     * @return 字段值
     */
    protected Object getOldValue(DataChangeEventContext context, String fieldName) {
        if (context.getOldDataMap() == null) {
            return null;
        }
        return context.getOldDataMap().get(fieldName);
    }

    /**
     * 获取变更后的字段值
     *
     * @param context   事件上下文
     * @param fieldName 字段名
     * @return 字段值
     */
    protected Object getNewValue(DataChangeEventContext context, String fieldName) {
        if (context.getNewDataMap() == null) {
            return null;
        }
        return context.getNewDataMap().get(fieldName);
    }

    /**
     * 判断字段是否发生变更
     *
     * @param context   事件上下文
     * @param fieldName 字段名
     * @return true-发生变更，false-未发生变更
     */
    protected boolean isFieldChanged(DataChangeEventContext context, String fieldName) {
        Object oldValue = getOldValue(context, fieldName);
        Object newValue = getNewValue(context, fieldName);

        if (oldValue == null && newValue == null) {
            return false;
        }

        if (oldValue == null || newValue == null) {
            return true;
        }

        return !oldValue.equals(newValue);
    }

    public String getBizId(DataChangeEventContext context) {
        return context.getNewDataMap().get("id").toString();
    }

    public String getJgrybm(DataChangeEventContext context) {
        return context.getNewDataMap().get("jgrybm").toString();
    }

    public PrisonerVwRespVO getJgry(DataChangeEventContext context) {
        PrisonerService prisonerService = SpringUtil.getBean(PrisonerService.class);
        return prisonerService.getPrisonerByJgrybm(getJgrybm(context));
    }

    public PrisonerVwRespVO getInJgry(DataChangeEventContext context) {
        PrisonerService prisonerService = SpringUtil.getBean(PrisonerService.class);
        String jgrybm = getJgrybm(context);
        if (StringUtils.isBlank(jgrybm)) {
            return null;
        }
        List<PrisonerVwRespVO> prisonerListByJgrybm = prisonerService.getPrisonerListByJgrybm(jgrybm, PrisonerQueryRyztEnum.ZS);
        if (prisonerListByJgrybm != null && !prisonerListByJgrybm.isEmpty()) {
            return prisonerListByJgrybm.get(0);
        }
        return null;
    }

    public boolean isDelete(DataChangeEventContext context) {
        String isDel = context.getNewDataMap().get("is_del").toString();
        if ("1".equals(isDel)) {
            return true;
        }
        return false;
    }

    public String getValue(String key, DataChangeEventContext context) {
        try {
            return context.getNewDataMap().get(key).toString();
        } catch (Exception e) {

        }
        return "";
    }
    public String getStatus(DataChangeEventContext context) {
        return getValue("status", context);
    }

}
