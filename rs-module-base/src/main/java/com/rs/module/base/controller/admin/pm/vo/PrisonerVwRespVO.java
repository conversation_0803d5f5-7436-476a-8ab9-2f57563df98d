package com.rs.module.base.controller.admin.pm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.common.annotation.Format;
import com.rs.module.base.service.ry.tag.RxxxTagService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 所有在所人员列表 Response VO")
@Data
public class PrisonerVwRespVO implements TransPojo {

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("org_code")
    private String orgCode;
    @ApiModelProperty("org_name")
    private String orgName;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("人员编号")
    private String rybh;
    @ApiModelProperty("别名")
    private String bm;
    @ApiModelProperty("备注")
    private String bz;
    @ApiModelProperty("出所去向")
    private String csqx;
    @ApiModelProperty("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date csrq;
    @ApiModelProperty("出所时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cssj;
    @ApiModelProperty("出所原因")
    private String csyy;
    @ApiModelProperty("床位号")
    private String cwh;
    @ApiModelProperty("单位代码")
    private String dwdm;
    @ApiModelProperty("风险等级")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JGRY_FXDJ")
    private String fxdj;
    @ApiModelProperty("国籍")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ")
    private String gj;
    @ApiModelProperty("关押期限")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gyqx;
    @ApiModelProperty("工作单位")
    private String gzdw;
    @ApiModelProperty("户籍地")
    @Trans(type = TransType.DICTIONARY, key = "ZD_HJ")
    private String hjd;
    @ApiModelProperty("户籍地详址")
    private String hjdxz;
    @ApiModelProperty("婚姻状况")
    @Trans(type = TransType.DICTIONARY, key = "ZD_HYZK")
    private String hyzk;
    @ApiModelProperty("籍贯")
    @Trans(type = TransType.DICTIONARY, key = "ZD_HJ")
    private String jg;
    @ApiModelProperty("经办人")
    private String jbr;
    @ApiModelProperty("经办时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date jbsj;
    @ApiModelProperty("简要案情")
    private String jyaq;
    @ApiModelProperty("健康状况")
    private String jkzk;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("拘留日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date jlrq;
    @ApiModelProperty("民族")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MZ")
    private String mz;
    @ApiModelProperty("入所时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rssj;
    @ApiModelProperty("入所原因")
    private String rsyy;
    @ApiModelProperty("人员状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_RYZT")
    private String ryzt;
    @ApiModelProperty("识别服号")
    private String sbfh;
    @ApiModelProperty("识别服标识")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SBFBS")
    private String sbfbs;
    @ApiModelProperty("身份")
    private String sf;
    @ApiModelProperty("身份核实")
    private String sfhs;
    @ApiModelProperty("身高")
    private String sg;
    @ApiModelProperty("收押凭证")
    private String sypz;
    @ApiModelProperty("收押凭证文书号")
    private String sypzwsh;
    @ApiModelProperty("同案编号")
    private String tabh;
    @ApiModelProperty("特殊身份")
    private String tssf;
    @ApiModelProperty("体重")
    private String tz;
    @ApiModelProperty("特长(专长)")
    private String tc;
    @ApiModelProperty("文化程度")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WHCD")
    private String whcd;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("姓名拼音")
    private String xmpy;
    @ApiModelProperty("现住址")
    private String xzz;
    @ApiModelProperty("现住址详址")
    private String xzzxz;
    @ApiModelProperty("足长")
    private String zc;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZJLX")
    private String zjlx;
    @ApiModelProperty("职务")
    private String zw;
    @ApiModelProperty("职务级别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZWJB")
    private String zwjb;
    @ApiModelProperty("职业")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZY")
    private String zy;
    @ApiModelProperty("政治面貌")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZZMM")
    private String zzmm;
    @ApiModelProperty("最终处置日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date zzczrq;
    @ApiModelProperty("正面照片")
    private String frontPhoto;
    @ApiModelProperty("左侧照片")
    private String leftPhoto;
    @ApiModelProperty("右侧照片")
    private String rightPhoto;
    @ApiModelProperty("涉嫌罪名")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SXZM")
    private String sxzm;
    @ApiModelProperty("档案编号")
    private String dabh;
    @ApiModelProperty("办案单位")
    private String badw;
    @ApiModelProperty("办案单位类型")
    private String badwlx;
    @ApiModelProperty("办案人")
    private String bar;
    @ApiModelProperty("办案人联系方式")
    private String barlxff;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("监区id")
    private String areaId;
    @ApiModelProperty("监区名称")
    private String areaName;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB", ref = "xbName")
    private String xb;
    private String xbName;
    @ApiModelProperty("诉讼环节")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SSJD")
    private String sshj;
    @ApiModelProperty("管理类别")
    private String gllb;
    @ApiModelProperty("最终处置结果")
    private String zzczjg;
    @ApiModelProperty("刑期起始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xqqsrq;
    @ApiModelProperty("刑期截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xqjzrq;
    @ApiModelProperty("案件编号")
    private String ajbh;
    @ApiModelProperty("案件类别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_AJLB")
    private String ajlb;
    @ApiModelProperty("送押单位")
    private String sydw;
    @ApiModelProperty("送押人")
    private String syr;
    @ApiModelProperty("被监管人员类型（01：在押人员，02：被拘人员，03：戒毒人员，99：其他）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BJGRYLX")
    private String bjgrylx;
    @ApiModelProperty("年龄")
    private Integer age;
    @ApiModelProperty("联系方式")
    private String lxfs;

    @ApiModelProperty("主管、协管民警")
    private List<PrisonRoomWarderRespVO> zgzjList;
    @ApiModelProperty("主管民警名称")
    private String zgmjName;
    @ApiModelProperty("主管民警身份证号")
    private String zgmjSfzh;
    @ApiModelProperty("协管民警名称")
    private String xgmjName;

    /**
     * 医疗信息
     **/
    @ApiModelProperty("最近就诊时间")
    private String zjjzsj;
    @ApiModelProperty("最近诊断病情")
    private String zjzdbq;

    @ApiModelProperty("有无过敏史")
    private Boolean hasAllergyHistory;

    @ApiModelProperty("当日报病次数")
    private int numberOfReportedIllnesses;

    @ApiModelProperty("是否已预约")
    private Boolean haveMadeAnAppointment;

    @ApiModelProperty("预约时间")
    private Date appointmentDate;

    @ApiModelProperty("是否有戒具")
    private Boolean haveRestraints;



    @ApiModelProperty("人员标签")
    @Format(service = RxxxTagService.class, method = "getTag", value = "jgrybm")
    private List<PrisonerTagRespVO> tags;

    @ApiModelProperty("新增时间")
    private Date addTime;

}
