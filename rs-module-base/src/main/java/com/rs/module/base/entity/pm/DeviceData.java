package com.rs.module.base.entity.pm;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备状态数据-Excel导入
 * <AUTHOR>
 * @date 2025/5/21 10:15
 */
@Data
public class DeviceData {

    @ExcelProperty("设备编码*")
    private String deviceCode;

    @ExcelProperty("设备名称*")
    private String deviceName;

    @ExcelProperty("设备类型编码*")
    private String deviceType;

    @ExcelProperty("所属区域编码*")
    private String areaId;

    @ExcelProperty("点位名称")
    private String pointName;

    @ExcelProperty("设备状态*")
    private String deviceStatus;

    @ExcelProperty("通道id")
    private String channelId;

    @ExcelProperty("通道名称")
    private String channelName;

    @ExcelProperty("ip地址")
    private String ipAddress;

    @ApiModelProperty("mac地址")
    private String macAddress;
}

