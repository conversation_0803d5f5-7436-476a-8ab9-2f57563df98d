package com.rs.module.base.service.bsp;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.adapter.bsp.api.BspApi;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @ClassName BspBaseService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/12 17:31
 * @Version 1.0
 */
public abstract class BspBaseServiceImpl<DAO extends IBaseDao<DO>, DO> extends BaseServiceImpl<DAO, DO> implements BspBaseService {
    @Resource
    public BspApi bspApi;

    @Override
    public String getSerialNumber(Map<String, Object> formData) {
        JSONObject formDataTemp = null;
        if (formData != null && formData.get("orgCode") != null) {
            formDataTemp = JSONUtil.createObj().set("orgCode", formData.get("orgCode").toString());
        }
        if (formDataTemp != null) {
            return bspApi.executeByRuleCode(getRuleCode(), formDataTemp.toJSONString(4));
        }
        return bspApi.executeByRuleCode(getRuleCode(), null);
    }

    public abstract String getRuleCode();
}
