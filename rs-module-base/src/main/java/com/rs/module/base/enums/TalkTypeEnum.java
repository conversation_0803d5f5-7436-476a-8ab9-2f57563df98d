package com.rs.module.base.enums;

/**
 * 谈话类型枚举
 * 注意：code 为字符串，与数据库保持一致
 */
public enum TalkTypeEnum {

    DAILY_TALK("1", "日常谈话"),
    WITHIN_24H_AFTER_ENTRY("2", "入所后二十四小时内谈话"),
    SECOND_DAY_AFTER_ENTRY("3", "入所第二天谈话"),
    THIRD_DAY_AFTER_ENTRY("4", "入所第三天谈话"),
    FIFTH_DAY_AFTER_ENTRY("5", "入所第五天谈话"),
    SIXTH_DAY_AFTER_ENTRY("6", "入所第六天谈话"),
    SEVENTH_DAY_AFTER_ENTRY("7", "入所第七天谈话"),
    WITHIN_7DAYS_AFTER_ENTRY("24", "入所七天内谈话"),
    AFTER_ARREST("8", "逮捕后谈话"),
    AFTER_INDICTMENT("9", "起诉后谈话"),
    AFTER_FIRST_INSTANCE("10", "一审后谈话"),
    AFTER_SECOND_INSTANCE("11", "二审后谈话"),
    AFTER_DEATH_PENALTY_REVIEW("12", "死刑复核后谈话"),
    BEFORE_RELEASE("13", "出所前谈话"),
    BEFORE_RELEASE_260("260", "出所前谈话"),
    WEEKLY_TALK_WITH_DEATH_ROW("14", "死刑犯每周谈话"),
    AFTER_TRANSFER_TO_COURT("15", "移交法院后谈话"),
    AFTER_INTERROGATION("16", "提讯后谈话"),
    AFTER_LAWYER_MEETING("17", "律师会见后谈话"),
    AFTER_COURT_HEARING("18", "庭审后谈话"),
    AFTER_FAMILY_ACCIDENT("19", "在押人员家庭发生发生变故后谈话"),
    AFTER_RESTRAINT("20", "加戴戒具后谈话"),
    AFTER_PUNISHMENT("21", "处罚后谈话"),
    AFTER_ROOM_CHANGE("22", "监室调整后谈话"),
    AFTER_REPORT_ROOM_DYNAMIC("23", "要求反映监室动态谈话"),
    AFTER_RESERVED_TALK("25", "在押人员预约谈话"),
    AFTER_EMERGENCY("26", "发生警情事件后谈话"),
    AFTER_HIGH_RISK("28", "重大安全风险人员谈话"),
    AFTER_FAMILY_VISIT("27", "会见后谈话"),
    AFTER_VIOLATION("33", "违规登记谈话"),
    INFO_MEMBER_TALK("240", "信息员谈话"),
    LITIGATION_CHANGE("250", "诉讼环节发生变化");

    private final String code;
    private final String description;

    TalkTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据 code 获取枚举
     */
    public static TalkTypeEnum fromCode(String code) {
        for (TalkTypeEnum type : TalkTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据 description 获取枚举
     */
    public static TalkTypeEnum fromDescription(String description) {
        for (TalkTypeEnum type : TalkTypeEnum.values()) {
            if (type.description.equals(description)) {
                return type;
            }
        }
        return null;
    }
}
