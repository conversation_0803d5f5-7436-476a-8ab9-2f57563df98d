package com.rs.module.base.util;

import com.bsp.common.cache.RedisClient;
import com.bsp.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Redis分布式锁工具类
 * 基于Redis实现的分布式锁，用于替代本地AtomicBoolean锁
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
public class RedisDistributedLockUtil {

    /**
     * 默认锁过期时间（秒）
     */
    private static final int DEFAULT_EXPIRE_TIME = 300; // 5分钟

    /**
     * 默认获取锁超时时间（毫秒）
     */
    private static final long DEFAULT_TIMEOUT = 3000; // 3秒

    /**
     * 锁值前缀，用于标识锁的持有者
     */
    private static final String LOCK_VALUE_PREFIX = "lock_";

    /**
     * 尝试获取分布式锁（非阻塞）
     *
     * @param lockKey 锁的key
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey) {
        return tryLock(lockKey, DEFAULT_EXPIRE_TIME);
    }

    /**
     * 尝试获取分布式锁（非阻塞）
     *
     * @param lockKey 锁的key
     * @param expireTime 锁过期时间（秒）
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey, int expireTime) {
        String lockValue = generateLockValue();
        try {
            // 检查锁是否已存在
            if (!RedisClient.exists(lockKey)) {
                // 锁不存在，尝试设置锁
                RedisClient.set(lockKey, lockValue, expireTime);
                // 再次检查是否成功获取锁（防止并发情况下的竞争）
                String currentValue = RedisClient.get(lockKey);
                if (lockValue.equals(currentValue)) {
                    log.debug("成功获取分布式锁: {}, 锁值: {}, 过期时间: {}秒", lockKey, lockValue, expireTime);
                    return true;
                } else {
                    log.debug("获取分布式锁失败，锁已被其他线程占用: {}", lockKey);
                    return false;
                }
            } else {
                log.debug("获取分布式锁失败，锁已被占用: {}", lockKey);
                return false;
            }
        } catch (Exception e) {
            log.error("获取分布式锁异常: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 尝试获取分布式锁（阻塞等待）
     *
     * @param lockKey 锁的key
     * @param timeout 等待超时时间（毫秒）
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey, long timeout) {
        return tryLock(lockKey, DEFAULT_EXPIRE_TIME, timeout);
    }

    /**
     * 尝试获取分布式锁（阻塞等待）
     *
     * @param lockKey 锁的key
     * @param expireTime 锁过期时间（秒）
     * @param timeout 等待超时时间（毫秒）
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey, int expireTime, long timeout) {
        long startTime = System.currentTimeMillis();
        String lockValue = generateLockValue();

        while (System.currentTimeMillis() - startTime < timeout) {
            try {
                // 检查锁是否已存在
                if (!RedisClient.exists(lockKey)) {
                    // 锁不存在，尝试设置锁
                    RedisClient.set(lockKey, lockValue, expireTime);
                    // 再次检查是否成功获取锁（防止并发情况下的竞争）
                    String currentValue = RedisClient.get(lockKey);
                    if (lockValue.equals(currentValue)) {
                        log.debug("成功获取分布式锁: {}, 锁值: {}, 过期时间: {}秒", lockKey, lockValue, expireTime);
                        return true;
                    }
                }

                // 等待一小段时间后重试
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待获取分布式锁被中断: {}", lockKey);
                return false;
            } catch (Exception e) {
                log.error("获取分布式锁异常: {}", lockKey, e);
                return false;
            }
        }

        log.debug("获取分布式锁超时: {}, 等待时间: {}ms", lockKey, timeout);
        return false;
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁的key
     * @return 是否成功释放锁
     */
    public void releaseLock(String lockKey) {
        RedisClient.remove(lockKey);
    }

    /**
     * 检查锁是否存在
     *
     * @param lockKey 锁的key
     * @return 锁是否存在
     */
    public boolean isLocked(String lockKey) {
        try {
            return RedisClient.exists(lockKey);
        } catch (Exception e) {
            log.error("检查分布式锁状态异常: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 续期锁（延长锁的过期时间）
     *
     * @param lockKey 锁的key
     * @param expireTime 新的过期时间（秒）
     * @return 是否成功续期
     */
    public boolean renewLock(String lockKey, int expireTime) {
        try {
            if (RedisClient.exists(lockKey)) {
                RedisClient.expire(lockKey, expireTime);
                log.debug("成功续期分布式锁: {}, 新过期时间: {}秒", lockKey, expireTime);
                return true;
            } else {
                log.debug("续期分布式锁失败，锁不存在: {}", lockKey);
                return false;
            }
        } catch (Exception e) {
            log.error("续期分布式锁异常: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 获取锁的剩余过期时间
     *
     * @param lockKey 锁的key
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示key不存在
     */
    public long getLockTtl(String lockKey) {
        try {
            return RedisClient.getExpire(lockKey);
        } catch (Exception e) {
            log.error("获取分布式锁TTL异常: {}", lockKey, e);
            return -2;
        }
    }

    /**
     * 生成锁值
     * 使用UUID + 时间戳确保唯一性
     *
     * @return 锁值
     */
    private String generateLockValue() {
        return LOCK_VALUE_PREFIX + StringUtil.getGuid32() + "_" + System.currentTimeMillis();
    }

    /**
     * 执行带锁的操作
     *
     * @param lockKey 锁的key
     * @param task 要执行的任务
     * @param expireTime 锁过期时间（秒）
     * @return 是否成功执行（获取到锁并执行完成）
     */
    public boolean executeWithLock(String lockKey, Runnable task, int expireTime) {
        if (tryLock(lockKey, expireTime)) {
            try {
                task.run();
                return true;
            } catch (Exception e) {
                log.error("执行带锁任务异常: {}", lockKey, e);
                throw e;
            } finally {
                releaseLock(lockKey);
            }
        }
        return false;
    }

    /**
     * 执行带锁的操作（使用默认过期时间）
     *
     * @param lockKey 锁的key
     * @param task 要执行的任务
     * @return 是否成功执行（获取到锁并执行完成）
     */
    public boolean executeWithLock(String lockKey, Runnable task) {
        return executeWithLock(lockKey, task, DEFAULT_EXPIRE_TIME);
    }
}
