package com.rs.module.base.entity.pm.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * 实战平台-监管管理-设备信息 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_device")
@KeySequence("acp_pm_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseDeviceDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备类型（字典：ZD_SBLXDM）
     */
    private String deviceTypeId;
    /**
     * 厂家
     */
    private String factory;
    /**
     * 型号
     */
    private String model;
    /**
     * 协议
     */
    private String protocol;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 端口
     */
    private Integer port;
    /**
     * 设备用户名
     */
    private String devUserName;
    /**
     * 设备密码
     */
    private String devPassword;
    /**
     * 点位名称
     */
    private String pointName;
    /**
     * 通道编号
     */
    private String channelId;
    /**
     * 通道名称
     */
    private String channelName;
    /**
     * 设备国标编号
     */
    private String gbCode;
    /**
     * mac地址
     */
    private String macAddress;
    /**
     * 在线时间
     */
    private Date onlineTime;
    /**
     * 所属区域
     */
    private String areaId;
    /**
     * 监室号
     */
    private String roomId;
    /**
     * 是否启用
     */
    private Integer isEnabled;
    /**
     * 设备状态（字典：ZD_SBZTDM）
     */
    private String deviceStatus;

    // 摄像头关联id，多个逗号分割
    private String refDeviceId;
    //是否配置过
    private String configed;

}
