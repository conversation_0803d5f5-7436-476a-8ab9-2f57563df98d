package com.rs.module.base.service.pm.device;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.dao.pm.BaseDeviceCameraDao;
import com.rs.module.acp.dao.pm.BaseDeviceFaceCameraDao;
import com.rs.module.acp.dao.pm.BaseDeviceVideoMoveDao;
import com.rs.module.acp.entity.pm.BaseDeviceCameraDO;
import com.rs.module.acp.entity.pm.BaseDeviceFaceCameraDO;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.entity.pm.BaseDeviceVideoMoveDO;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.acp.service.pm.BaseDeviceInscreenService;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceListReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDevicePageReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceRespVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceSaveReqVO;
import com.rs.module.base.controller.admin.video.vo.AddTreeNodeRequestVO;
import com.rs.module.base.dao.pm.device.BaseDeviceDao;
import com.rs.module.base.dao.pm.device.BaseVidVideoDao;
import com.rs.module.base.dao.pm.device.SplwTreeDao;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.DeviceData;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.entity.pm.device.BaseVidVideoDO;
import com.rs.module.base.entity.pm.device.SplwTreeDO;
import com.rs.module.base.enums.DeviceStatusEnum;
import com.rs.module.base.enums.DeviceTypeEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.video.VideoRouteService;
import com.rs.module.base.util.TreeUtil;
import com.rs.module.base.vo.TreeNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 实战平台-监管管理-设备信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BaseDeviceServiceImpl extends BaseServiceImpl<BaseDeviceDao, BaseDeviceDO> implements BaseDeviceService {

    @Resource
    private BaseDeviceDao baseDeviceDao;

    @Autowired
    private VideoRouteService videoRouteService;

    @Autowired
    private BaseVidVideoDao baseVidVideoDao;

    @Resource
    private BaseDeviceCameraService baseDeviceCameraService;

    @Resource
    private SplwTreeDao splwTreeDao;

    @Autowired
    private BaseDeviceVideoMoveDao videoMoveDao;

    @Autowired
    private BaseDeviceCameraDao baseDeviceCameraDao;

    @Resource
    private BaseDeviceVideoMoveDao baseDeviceVideoMoveDao;

    @Resource
    private BaseDeviceFaceCameraDao baseDeviceFaceCameraDao;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Resource
    private BaseDeviceInscreenService baseDeviceInscreenService;

    private final static String BASE_DEVICE_TYPE = "02";

    private final static String BASE_DEVICE_START = "0001";

    @Override
    public String createBaseDevice(BaseDeviceSaveReqVO createReqVO) {
        // 插入
        BaseDeviceDO baseDevice = BeanUtils.toBean(createReqVO, BaseDeviceDO.class);

        if (baseDevice != null && StrUtil.isNotBlank(baseDevice.getAreaId())) {
            AreaPrisonRoomDO areaPrisonRoomDO = areaPrisonRoomService.getAreaPrisonRoom(baseDevice.getAreaId());
            if (areaPrisonRoomDO != null) {
                baseDevice.setRoomId(areaPrisonRoomDO.getRoomCode());
                baseDevice.setAreaId(areaPrisonRoomDO.getAreaId());
            }
        }
        baseDeviceDao.insert(baseDevice);

        //摄像头
        if (DeviceTypeEnum.CAMERA.getCode().equals(baseDevice.getDeviceTypeId())) {
            BaseDeviceCameraDO cameraEntity = new BaseDeviceCameraDO();
            BeanUtils.copyProperties(baseDevice, cameraEntity);
            cameraEntity.setDeviceId(baseDevice.getId());
            cameraEntity.setDeviceIp(StrUtil.isBlank(baseDevice.getIpAddress()) ? StrUtil.EMPTY : baseDevice.getIpAddress());
            baseDeviceCameraDao.insert(cameraEntity);
        }

        //人脸摄像头
        if (DeviceTypeEnum.FACE_CAMERA.getCode().equals(baseDevice.getDeviceTypeId())) {
            BaseDeviceFaceCameraDO baseDeviceFaceCameraDO = new BaseDeviceFaceCameraDO();
            BeanUtils.copyProperties(baseDevice, baseDeviceFaceCameraDO);
            baseDeviceFaceCameraDO.setDeviceId(baseDevice.getId());
            baseDeviceFaceCameraDO.setDeviceIp(StrUtil.isBlank(baseDevice.getIpAddress()) ? StrUtil.EMPTY : baseDevice.getIpAddress());
            baseDeviceFaceCameraDao.insert(baseDeviceFaceCameraDO);
        }

        //视频异动
        if (DeviceTypeEnum.VIDEO_ANOMALY.getCode().equals(baseDevice.getDeviceTypeId())) {
            //视频异动
            BaseDeviceVideoMoveDO videoMoveEntity = new BaseDeviceVideoMoveDO();
            BeanUtils.copyProperties(baseDevice, videoMoveEntity);
            videoMoveEntity.setDeviceId(baseDevice.getId());
            videoMoveEntity.setDeviceIp(StrUtil.isBlank(baseDevice.getIpAddress()) ? StrUtil.EMPTY : baseDevice.getIpAddress());
            videoMoveEntity.setFactoryDeviceId(StrUtil.isBlank(baseDevice.getFactory()) ? StrUtil.EMPTY : baseDevice.getFactory());
            baseDeviceVideoMoveDao.insert(videoMoveEntity);
        }

        //仓内屏  仓外屏  对讲主机，对讲分机
        if (DeviceTypeEnum.INDOOR_TERMINAL.getCode().equals(baseDevice.getDeviceTypeId())
                || DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode().equals(baseDevice.getDeviceTypeId())
                || DeviceTypeEnum.INTERCOM_EXTENSION.getCode().equals(baseDevice.getDeviceTypeId())
                || DeviceTypeEnum.INTERCOM_HOST.getCode().equals(baseDevice.getDeviceTypeId())
                || DeviceTypeEnum.ANTI_MISOPERATION.getCode().equals(baseDevice.getDeviceTypeId())) {
            baseDeviceInscreenService.saveOrUpdateDeviceInfo(createReqVO, baseDevice);
        }

        return baseDevice.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBaseDevice(BaseDeviceSaveReqVO updateReqVO) {
        // 校验存在
        validateBaseDeviceExists(updateReqVO.getId());

        //修改区域及监室
        if (updateReqVO != null && StrUtil.isNotBlank(updateReqVO.getAreaId())) {
            AreaPrisonRoomDO areaPrisonRoomDO = areaPrisonRoomService.getAreaPrisonRoom(updateReqVO.getAreaId());
            if (areaPrisonRoomDO != null) {
                updateReqVO.setRoomId(areaPrisonRoomDO.getRoomCode());
                updateReqVO.setAreaId(areaPrisonRoomDO.getAreaId());
            }
        }

        // 更新
        BaseDeviceDO updateObj = BeanUtils.toBean(updateReqVO, BaseDeviceDO.class);
        baseDeviceDao.updateById(updateObj);


        //摄像头
        if (DeviceTypeEnum.CAMERA.getCode().equals(updateObj.getDeviceTypeId())) {
            BaseDeviceCameraDO cameraEntity = new BaseDeviceCameraDO();
            BeanUtils.copyProperties(updateObj, cameraEntity);
            cameraEntity.setDeviceId(updateObj.getId());
            cameraEntity.setDeviceIp(StrUtil.isBlank(updateObj.getIpAddress()) ? StrUtil.EMPTY : updateObj.getIpAddress());
            //先干
            baseDeviceCameraDao.delete(new LambdaQueryWrapper<BaseDeviceCameraDO>().eq(BaseDeviceCameraDO::getDeviceId, updateReqVO.getId()));
            baseDeviceCameraDao.insert(cameraEntity);
        }

        //人脸摄像头
        if (DeviceTypeEnum.FACE_CAMERA.getCode().equals(updateObj.getDeviceTypeId())) {
            BaseDeviceFaceCameraDO baseDeviceFaceCameraDO = new BaseDeviceFaceCameraDO();
            BeanUtils.copyProperties(updateObj, baseDeviceFaceCameraDO);
            baseDeviceFaceCameraDO.setDeviceId(updateObj.getId());
            baseDeviceFaceCameraDO.setDeviceIp(StrUtil.isBlank(updateObj.getIpAddress()) ? StrUtil.EMPTY : updateObj.getIpAddress());

            baseDeviceFaceCameraDao.delete(new LambdaQueryWrapper<BaseDeviceFaceCameraDO>().eq(BaseDeviceFaceCameraDO::getDeviceId, updateReqVO.getId()));
            baseDeviceFaceCameraDao.insert(baseDeviceFaceCameraDO);
        }

        //视频异动
        if (DeviceTypeEnum.VIDEO_ANOMALY.getCode().equals(updateObj.getDeviceTypeId())) {
            //视频异动
            BaseDeviceVideoMoveDO videoMoveEntity = new BaseDeviceVideoMoveDO();
            BeanUtils.copyProperties(updateObj, videoMoveEntity);
            videoMoveEntity.setDeviceId(updateObj.getId());
            videoMoveEntity.setDeviceIp(StrUtil.isBlank(updateObj.getIpAddress()) ? StrUtil.EMPTY : updateObj.getIpAddress());
            videoMoveEntity.setFactoryDeviceId(StrUtil.isBlank(updateObj.getFactory()) ? StrUtil.EMPTY : updateObj.getFactory());

            baseDeviceVideoMoveDao.delete(new LambdaQueryWrapper<BaseDeviceVideoMoveDO>().eq(BaseDeviceVideoMoveDO::getDeviceId, updateReqVO.getId()));
            baseDeviceVideoMoveDao.insert(videoMoveEntity);
        }

        //仓内屏  仓外屏  对讲主机，对讲分机
        if (DeviceTypeEnum.INDOOR_TERMINAL.getCode().equals(updateObj.getDeviceTypeId())
                || DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode().equals(updateObj.getDeviceTypeId())
                || DeviceTypeEnum.INTERCOM_EXTENSION.getCode().equals(updateObj.getDeviceTypeId())
                || DeviceTypeEnum.INTERCOM_HOST.getCode().equals(updateObj.getDeviceTypeId())
                || DeviceTypeEnum.ANTI_MISOPERATION.getCode().equals(updateObj.getDeviceTypeId())) {
            baseDeviceInscreenService.saveOrUpdateDeviceInfo(updateReqVO, updateObj);
        }

    }

    @Override
    public void deleteBaseDevice(String id) {
        // 校验存在
        BaseDeviceDO baseDeviceDO = baseDeviceDao.selectById(id);
        if (ObjectUtil.isEmpty(baseDeviceDO)) {
            throw new ServerException("实战平台-监管管理-设备信息数据不存在");
        }
        if (DeviceTypeEnum.CAMERA.getCode().equals(baseDeviceDO.getDeviceTypeId())) {
            baseDeviceCameraDao.delete(new LambdaQueryWrapper<BaseDeviceCameraDO>().eq(BaseDeviceCameraDO::getDeviceId, baseDeviceDO.getId()));
        }
        if (DeviceTypeEnum.FACE_CAMERA.getCode().equals(baseDeviceDO.getDeviceTypeId())) {
            baseDeviceFaceCameraDao.delete(new LambdaQueryWrapper<BaseDeviceFaceCameraDO>().eq(BaseDeviceFaceCameraDO::getDeviceId, baseDeviceDO.getId()));
        }
        if (DeviceTypeEnum.VIDEO_ANOMALY.getCode().equals(baseDeviceDO.getDeviceTypeId())) {
            baseDeviceVideoMoveDao.delete(new LambdaQueryWrapper<BaseDeviceVideoMoveDO>().eq(BaseDeviceVideoMoveDO::getDeviceId, baseDeviceDO.getId()));
        }
        if (DeviceTypeEnum.INDOOR_TERMINAL.getCode().equals(baseDeviceDO.getDeviceTypeId())
                || DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode().equals(baseDeviceDO.getDeviceTypeId())
                || DeviceTypeEnum.INTERCOM_EXTENSION.getCode().equals(baseDeviceDO.getDeviceTypeId())
                || DeviceTypeEnum.INTERCOM_HOST.getCode().equals(baseDeviceDO.getDeviceTypeId())) {
            baseDeviceInscreenService.remove(new LambdaQueryWrapper<BaseDeviceInscreenDO>().eq(BaseDeviceInscreenDO::getDeviceId, baseDeviceDO.getId()));
        }
        // 删除
        baseDeviceDao.deleteById(id);
    }

    private void validateBaseDeviceExists(String id) {
        if (baseDeviceDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-设备信息数据不存在");
        }
    }

    @Override
    public BaseDeviceDO getBaseDevice(String id) {
        BaseDeviceDO baseDeviceDO = baseDeviceDao.selectById(id);
        return baseDeviceDO;
    }

    @Override
    public PageResult<BaseDeviceDO> getBaseDevicePage(BaseDevicePageReqVO pageReqVO) {
        return baseDeviceDao.selectPage(pageReqVO);
    }

    @Override
    public List<BaseDeviceDO> getBaseDeviceList(BaseDeviceListReqVO listReqVO) {
        return baseDeviceDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importDeviceData(List<DeviceData> dataList) {
        Map<String, DeviceData> deviceDataMap = new HashMap<>();

        dataList.forEach(e -> {
            //字典转换
            e.setDeviceType(DeviceTypeEnum.getCodeByName(e.getDeviceType()));
            e.setDeviceStatus(DeviceStatusEnum.getCodeByName(e.getDeviceStatus()));
            deviceDataMap.put(e.getDeviceCode() + e.getDeviceName(), e);
        });
        List<BaseDeviceDO> deviceDOList = BeanUtils.toBean(dataList, BaseDeviceDO.class);


        // 20250802 设备类型字段 两个实体之间没有对应，转换之后就没有值了
        for (BaseDeviceDO baseDevice : deviceDOList) {
            baseDevice.setId(StringUtil.getGuid32());
            DeviceData deviceData = deviceDataMap.get(baseDevice.getDeviceCode() + baseDevice.getDeviceName());
            if (ObjectUtil.isNotEmpty(deviceData)) {
                baseDevice.setDeviceTypeId(deviceData.getDeviceType());
            }

            if (StrUtil.isNotBlank(baseDevice.getAreaId())) {
                AreaPrisonRoomDO areaPrisonRoomDO = areaPrisonRoomService.getAreaPrisonRoom(baseDevice.getAreaId());
                if (areaPrisonRoomDO != null) {
                    baseDevice.setRoomId(areaPrisonRoomDO.getRoomCode());
                    baseDevice.setAreaId(areaPrisonRoomDO.getAreaId());
                }
            }

            //摄像头
            if (DeviceTypeEnum.CAMERA.getCode().equals(baseDevice.getDeviceTypeId())) {
                BaseDeviceCameraDO cameraEntity = new BaseDeviceCameraDO();
                BeanUtils.copyProperties(baseDevice, cameraEntity);
                cameraEntity.setDeviceId(baseDevice.getId());
                cameraEntity.setDeviceIp(StrUtil.isBlank(baseDevice.getIpAddress()) ? StrUtil.EMPTY : baseDevice.getIpAddress());
                baseDeviceCameraDao.insert(cameraEntity);
            }

            //人脸摄像头
            if (DeviceTypeEnum.FACE_CAMERA.getCode().equals(baseDevice.getDeviceTypeId())) {
                BaseDeviceFaceCameraDO baseDeviceFaceCameraDO = new BaseDeviceFaceCameraDO();
                BeanUtils.copyProperties(baseDevice, baseDeviceFaceCameraDO);
                baseDeviceFaceCameraDO.setDeviceId(baseDevice.getId());
                baseDeviceFaceCameraDO.setDeviceIp(StrUtil.isBlank(baseDevice.getIpAddress()) ? StrUtil.EMPTY : baseDevice.getIpAddress());
                baseDeviceFaceCameraDao.insert(baseDeviceFaceCameraDO);
            }

            //视频异动
            if (DeviceTypeEnum.VIDEO_ANOMALY.getCode().equals(baseDevice.getDeviceTypeId())) {
                //视频异动
                BaseDeviceVideoMoveDO videoMoveEntity = new BaseDeviceVideoMoveDO();
                BeanUtils.copyProperties(baseDevice, videoMoveEntity);
                videoMoveEntity.setDeviceId(baseDevice.getId());
                videoMoveEntity.setDeviceIp(StrUtil.isBlank(baseDevice.getIpAddress()) ? StrUtil.EMPTY : baseDevice.getIpAddress());
                videoMoveEntity.setFactoryDeviceId(StrUtil.isBlank(baseDevice.getFactory()) ? StrUtil.EMPTY : baseDevice.getFactory());
                baseDeviceVideoMoveDao.insert(videoMoveEntity);
            }

            //仓内屏  仓外屏  对讲主机，对讲分机
            if (DeviceTypeEnum.INDOOR_TERMINAL.getCode().equals(baseDevice.getDeviceTypeId())
                    || DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode().equals(baseDevice.getDeviceTypeId())
                    || DeviceTypeEnum.INTERCOM_EXTENSION.getCode().equals(baseDevice.getDeviceTypeId())
                    || DeviceTypeEnum.INTERCOM_HOST.getCode().equals(baseDevice.getDeviceTypeId())) {

                BaseDeviceSaveReqVO baseDeviceSaveReqVO = BeanUtils.toBean(baseDevice, BaseDeviceSaveReqVO.class);
                baseDeviceInscreenService.saveOrUpdateDeviceInfo(baseDeviceSaveReqVO, baseDevice);
            }
        }
        //Dict
        super.saveBatch(deviceDOList);

    }


    @Override
    public List<BaseDeviceRespVO> getCameraListByRoomId(String roomId) {
        //室内终端（仓内屏） 008  仓外屏 0015  摄像头0002
        List<BaseDeviceDO> baseDeviceRespVOList = baseDeviceDao.selectList(new LambdaQueryWrapper<BaseDeviceDO>().eq(BaseDeviceDO::getAreaId, roomId).in(BaseDeviceDO::getDeviceTypeId,
                DeviceTypeEnum.INDOOR_TERMINAL.getCode(), DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode()));
        if (CollectionUtil.isNotEmpty(baseDeviceRespVOList)) {
            List<BaseDeviceRespVO> deviceRespVOList = BeanUtils.toBean(baseDeviceRespVOList, BaseDeviceRespVO.class);
            for (BaseDeviceRespVO baseDeviceRespVO : deviceRespVOList) {
                if (StrUtil.isNotBlank(baseDeviceRespVO.getRefDeviceId())) {
                    List<String> refDeviceIdList = Arrays.asList(baseDeviceRespVO.getRefDeviceId().split(","));
                    List<BaseDeviceDO> tempList = baseDeviceDao.selectList(new LambdaQueryWrapper<BaseDeviceDO>().in(BaseDeviceDO::getId, refDeviceIdList).eq(BaseDeviceDO::getDeviceTypeId, DeviceTypeEnum.CAMERA.getCode()));
                    if (CollectionUtil.isNotEmpty(tempList)) {
                        baseDeviceRespVO.setCameraList(BeanUtils.toBean(tempList, BaseDeviceRespVO.class));
                    }
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public JSONObject sysVideoTreeByAreaName(String areaName, String orgCode) throws Exception {
        log.info("同步视频联网对应区域名字设备>>>>>>>>>>>>>>>开始");
        JSONObject jsonObject = sysVideoTree(areaName, orgCode);
        log.info("同步视频联网对应区域名字设备>>>>>>>>>>>>>>>结束");
        videoRouteService.initNew(orgCode);
        return jsonObject;
    }


    /**
     * 同步视频联网对应区域名字设备
     *
     * @param areaName 区域名
     * @param orgCode  机构代码
     * @return 操作结果
     */
    @Override
    public JSONObject sysVideoTree(String areaName, String orgCode) throws Exception {
        log.info("同步视频联网对应区域名字设备开始，orgCode: {}, areaName: {}", orgCode, areaName);

        JSONObject result = new JSONObject();

        try {
            // 清空原有数据
            splwTreeDao.clearTable(orgCode);

            // 获取根节点信息
            String rootCode = videoRouteService.getRootCode();
            JSONObject rootTree = videoRouteService.getSplwAreaByTreeCode(rootCode.equals("001") ? "" : "001");
            JSONArray rootNodes = Optional.ofNullable(rootTree)
                    .map(j -> j.getJSONArray("treeNodeList"))
                    .filter(arr -> arr.size() > 0)
                    .orElseThrow(() -> new ServerException("根节点数据为空"));
            log.info("获取根节点信息{}", rootNodes);
            // 处理根节点
            Integer currentId = handleRootNode(rootNodes.getJSONObject(0), orgCode);
            log.info("handleRootNode 成功 currentId={}", currentId);
            // 构建查询参数
            BaseDevicePageReqVO reqVO = new BaseDevicePageReqVO();
            reqVO.setOrgCode(orgCode);

            // 获取并处理区域树
            List<TreeNode> areaTree = getAllAreaTree(reqVO);

            // 处理每个子节点
            processAreaTree(areaTree, rootCode, orgCode, currentId + 1, areaName);

            log.info("同步视频联网对应区域名字设备结束");
            videoRouteService.initNew(orgCode);

            return result;
        } catch (Exception e) {
            log.error("同步视频树失败，orgCode: {}, areaName: {}", orgCode, areaName, e);
            throw e;
        }
    }

    /**
     * 处理区域树
     */
    private void processAreaTree(List<TreeNode> areaTree, String rootCode, String orgCode, Integer initialId, String areaName) throws Exception {
        log.info("处理区域树 processAreaTree param{},{},{}", areaTree, rootCode, areaName);
        for (TreeNode treeNode : areaTree) {
            if (orgCode.equals(treeNode.getOrgCode())) {
                JSONObject splwTree = videoRouteService.getSplwAreaByTreeCode("001001");
                JSONArray treeNodeList = Optional.ofNullable(splwTree)
                        .map(j -> j.getJSONArray("treeNodeList"))
                        .orElseThrow(() -> new ServerException("平台父节点数据为空"));
                log.info("=================>处理区域树 processAreaTree param{},{}", treeNodeList, treeNodeList.size());
                for (Object obj : treeNodeList) {
                    JSONObject node = (JSONObject) obj;
                    if (node.getString("name").equals(areaName)) {
                        // 处理区域节点
                        Integer currentId = processAreaNode(node, rootCode, orgCode, initialId);

                        // 获取通道信息列表
                        JSONObject areaByTreeCode = videoRouteService.getSplwAreaByTreeCode(node.getString("treeCode"));
                        JSONArray chanInfoList = Optional.ofNullable(areaByTreeCode)
                                .map(j -> j.getJSONArray("chanInfoList"))
                                .orElse(new JSONArray());

                        log.info("=================>开始循环同步设备，共 {} 个通道", chanInfoList.size());

                        // 批量保存通道设备信息
                        batchSaveChannelDevices(chanInfoList, treeNode, orgCode, currentId, node.getString("treeCode"));

                        // 递归处理子节点
                        getChildren2(treeNode.getChildren(), currentId, node.getString("treeCode"));
                    }
                }
            }
        }
    }

    /**
     * 处理区域节点
     */
    private Integer processAreaNode(JSONObject node, String rootCode, String orgCode, Integer initialId) {
        // 获取路径信息
        Map<String, String> pathInfo = getTreePathInfo(node, rootCode);
        if (pathInfo == null) {
            return initialId;
        }
        Integer checkNum = splwTreeDao.getCheckNum(node.getString("name"), node.getString("treeCode"));
        // if(checkNum == 0){
        // 构建SplwTreeDO对象
        SplwTreeDO treeDO = buildSplwTreeDO(node, initialId, pathInfo, orgCode);

        // 插入数据库
        splwTreeDao.insert(treeDO);
        //  }
        return initialId + 1;
    }

    /**
     * 构建SplwTreeDO对象
     */
    private SplwTreeDO buildSplwTreeDO(JSONObject node, Integer id, Map<String, String> pathInfo, String orgCode) {
        SplwTreeDO treeDO = new SplwTreeDO();
        treeDO.setId(id);
        treeDO.setOrderNo(id);
        treeDO.setTreeCode(node.getString("treeCode"));
        treeDO.setType(1);
        treeDO.setPrisonId(orgCode);

        // 设置数值型属性
        setNumericProperties(treeDO, node);

        // 设置路径相关信息
        treeDO.setTreeLevel(Integer.parseInt(pathInfo.get("level")));
        treeDO.setParentPathId(pathInfo.get("pathId"));
        treeDO.setPathName(pathInfo.get("pathName"));

        String pathId = pathInfo.get("pathId");
        treeDO.setPid(Integer.valueOf(pathId.substring(pathId.lastIndexOf("/") + 1)));
        treeDO.setName(node.getString("name"));

        return treeDO;
    }

    /**
     * 设置SplwTreeDO的数值型属性
     */
    private void setNumericProperties(SplwTreeDO treeDO, JSONObject node) {
        treeDO.setAllNum(node.getIntValue("allNum"));
        treeDO.setIsLocked(node.getIntValue("isLocked"));
        treeDO.setOnlineNum(node.getIntValue("onlineNum"));
        treeDO.setPlatformId(node.getIntValue("platformId"));
        treeDO.setTotalNum(node.getIntValue("totalNum"));
    }

    /**
     * 批量保存通道设备信息
     */
    private void batchSaveChannelDevices(JSONArray chanInfoList, TreeNode treeNode, String orgCode, Integer currentId, String code) {
        if (chanInfoList == null || chanInfoList.isEmpty()) {
            return;
        }

        List<SplwTreeDO> splwTreeList = new ArrayList<>();
        List<BaseDeviceSaveReqVO> deviceList = new ArrayList<>();

        for (int i = 0; i < chanInfoList.size(); i++) {
            JSONObject item = chanInfoList.getJSONObject(i);

            // 更新设备状态
            updateDeviceStatus(item, orgCode);

            // 创建SplwTreeDO对象
            SplwTreeDO splwTreeDO = createSplwTreeDO(item, currentId, code, treeNode, orgCode);
            if (splwTreeDO != null) {
                splwTreeList.add(splwTreeDO);
                splwTreeDao.insert(splwTreeDO);
                // 创建设备信息
                BaseDeviceSaveReqVO deviceDto = createBaseDeviceSaveReqVO(item, treeNode, orgCode);
                deviceList.add(deviceDto);

                // 清空旧数据
                clearOldData(item, orgCode);

                currentId = splwTreeDao.getMaxCode(orgCode) + 1;
            }
        }

        // 批量插入SplwTree数据
        if (!splwTreeList.isEmpty()) {
            //splwTreeDao.insertBatch(splwTreeList);
        }

        // 批量新增设备
        for (BaseDeviceSaveReqVO device : deviceList) {
            addDevice(device);
        }
    }

    /**
     * 更新设备状态
     */
    private void updateDeviceStatus(JSONObject item, String orgCode) {
        String chanId = item.getString("chanId");
        String status = item.getString("status").equals("0") ? "001" : "002";
        String gb28181Code = item.getString("gb28181Code");

        baseDeviceDao.updateDeviceByChanId(chanId, status, gb28181Code);
        baseDeviceCameraService.updateChanDeviceStatus(chanId, Integer.valueOf(item.getString("facade")));
    }

    /**
     * 创建SplwTreeDO对象
     */
    private SplwTreeDO createSplwTreeDO(JSONObject item, Integer currentId, String code,
                                        TreeNode treeNode, String orgCode) {
        String rootCode = videoRouteService.getRootCode();
        Map<String, String> pathInfo = getPathInfoForChan(item, code, rootCode);

        if (pathInfo == null) {
            return null;
        }

        SplwTreeDO treeVO = new SplwTreeDO();
        treeVO.setId(currentId);
        treeVO.setOrderNo(currentId);
        treeVO.setTreeCode(item.getString("treeCode"));
        treeVO.setType(2);
        treeVO.setTreeLevel(Integer.parseInt(pathInfo.get("level")));
        treeVO.setParentPathId(pathInfo.get("pathId"));
        treeVO.setPathName(pathInfo.get("pathName"));

        String pathId = pathInfo.get("pathId");
        treeVO.setPid(Integer.valueOf(pathId.substring(pathId.lastIndexOf("/") + 1)));
        treeVO.setName(item.getString("chanName"));
        treeVO.setChanId(Long.valueOf(item.getString("chanId")));

        // 设置其他属性
        setChanItemProperties(treeVO, item);

        return treeVO;
    }

    /**
     * 设置通道项属性
     */
    private void setChanItemProperties(SplwTreeDO treeVO, JSONObject item) {
        treeVO.setChanTypeId(Integer.valueOf(item.getString("chanTypeId")));
        treeVO.setChnAbility(item.getString("chnAbility"));
        treeVO.setControlType(Integer.valueOf(item.getString("controlType")));
        treeVO.setDefaultStreamType(Integer.valueOf(item.getString("defaultStreamType")));
        treeVO.setDevId(Long.valueOf(item.getString("devId")));
        treeVO.setDevLockStatus(Integer.valueOf(item.getString("devLockStatus")));
        treeVO.setFacade(Integer.valueOf(item.getString("facade")));
        treeVO.setPtzLockStatus(Integer.valueOf(item.getString("ptzLockStatus")));
        treeVO.setGb28181Code(item.getString("gb28181Code"));
        treeVO.setIsFocus(Integer.valueOf(item.getString("isFocus")));
        treeVO.setPlatformId(Integer.valueOf(item.getString("platformId")));
        treeVO.setStatus(Integer.valueOf(item.getString("status")));
        treeVO.setStorageType(Integer.valueOf(item.getString("storageType")));
        treeVO.setUsageType(Integer.valueOf(item.getString("usageType")));
        treeVO.setPrisonId(item.getString("prisonId"));
    }

    /**
     * 获取通道项的路径信息
     */
    private Map<String, String> getPathInfoForChan(JSONObject item, String code, String rootCode) {
        String treeCode = item.getString("treeCode");
        Map<String, String> stringMap;
        Integer level;
        if ("001".equals(rootCode)) {
            level = videoRouteService.getTreeLevel(treeCode, 2);
            stringMap = videoRouteService.getTreePathIds(treeCode, level);
        } else {
            level = videoRouteService.getTreeLevel(treeCode, 1);
            stringMap = videoRouteService.getTreePathIds3(treeCode, level);
        }

        if (stringMap == null) {
            log.warn("获取树路径信息失败，treeCode: {}", treeCode);
            return null;
        }
        stringMap.put("level", level.toString());
        return stringMap;
    }

    /**
     * 清除旧数据
     */
    private void clearOldData(JSONObject item, String orgCode) {
        String chanId = item.getString("chanId");
        clearDeviceTable(chanId, orgCode);
        baseDeviceCameraService.clearDeviceCameraTable(chanId, orgCode);
    }

    /**
     * 递归处理子节点
     */
    public List<TreeNode> getChildren2(List<TreeNode> children, Integer id, String treeCode) throws Exception {
        if (CollectionUtil.isEmpty(children)) {
            return Collections.emptyList();
        }

        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        String rootCode = videoRouteService.getRootCode();

        List<SplwTreeDO> splwTreeList = new ArrayList<>();
        List<BaseDeviceSaveReqVO> deviceList = new ArrayList<>();

        for (TreeNode treeNode : children) {
            if (treeNode != null && "area".equals(treeNode.getType())) {
                JSONObject splwTree = videoRouteService.getSplwAreaByTreeCode(treeCode);
                JSONArray treeNodeList = splwTree.getJSONArray("treeNodeList");

                if (treeNodeList == null || treeNodeList.isEmpty()) {
                    continue;
                }

                for (int i = 0; i < treeNodeList.size(); i++) {
                    JSONObject treeJsonObject = treeNodeList.getJSONObject(i);
                    String code = treeJsonObject.getString("treeCode");
                    // 检查是否需要处理
                    Integer checkNum = splwTreeDao.getCheckNum(treeJsonObject.getString("name"), treeJsonObject.getString("treeCode"));
                    if (StringUtil.isEmpty(treeNode.getAreaName()) || !treeNode.getAreaName().equals(treeJsonObject.getString("name")) || checkNum >= 1) {
                        continue;
                    }

                    id = splwTreeDao.getMaxCode(orgCode) + 1;
                    JSONObject chanTree = videoRouteService.getSplwAreaByTreeCode(treeJsonObject.getString("treeCode"));
                    JSONArray chanInfoList = chanTree.getJSONArray("chanInfoList");

                    // 创建SplwTreeDO对象
                    SplwTreeDO splwTreeDO = buildSplwTreeDO(treeJsonObject, id, rootCode, orgCode);
                    //splwTreeList.add(splwTreeDO);
                    splwTreeDao.insert(splwTreeDO);
                    // 处理通道信息
                    if (!chanInfoList.isEmpty()) {
                        for (int j = 0; j < chanInfoList.size(); j++) {
                            JSONObject chanInfoJsonObject = chanInfoList.getJSONObject(j);

                            // 更新设备状态
                            String status = chanInfoJsonObject.get("status").toString().equals("0") ? "001" : "002";
                            String chanId = chanInfoJsonObject.get("chanId").toString();
                            baseDeviceDao.updateDeviceByChanId(chanId, status, chanInfoJsonObject.get("gb28181Code").toString());
                            baseDeviceCameraService.updateChanDeviceStatus(chanId, Integer.valueOf(chanInfoJsonObject.get("facade").toString()));

                            // 创建设备信息
                            BaseDeviceSaveReqVO baseDeviceDto = createBaseDeviceSaveReqVO(chanInfoJsonObject, treeNode, orgCode);
                            deviceList.add(baseDeviceDto);

                            // 清空之前设备再新增
                            clearDeviceTable(chanInfoJsonObject.get("chanId").toString(), orgCode);
                            baseDeviceCameraService.clearDeviceCameraTable(chanInfoJsonObject.get("chanId").toString(), orgCode);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(treeNode.getChildren())) {
                        getChildren2(treeNode.getChildren(), id, code);
                    }
                }
            }
        }

        // 批量插入SplwTree数据
        if (!splwTreeList.isEmpty()) {
            //splwTreeDao.insertBatch(splwTreeList);
        }

        // 批量新增设备
        for (BaseDeviceSaveReqVO device : deviceList) {
            addDevice(device);
        }
        return children;
    }

    /**
     * 构建SplwTreeDO对象
     */
    private SplwTreeDO buildSplwTreeDO(JSONObject jsonObject, Integer id, String rootCode, String orgCode) {
        SplwTreeDO splwTreeDO = new SplwTreeDO();
        splwTreeDO.setId(id);
        splwTreeDO.setOrderNo(id);
        splwTreeDO.setTreeCode(jsonObject.getString("treeCode"));
        splwTreeDO.setAllNum(Integer.valueOf(jsonObject.get("allNum").toString()));
        splwTreeDO.setGb28181Code(jsonObject.get("gb28181Code") != null ? jsonObject.get("gb28181Code").toString() : "");
        splwTreeDO.setIsLocked(Integer.valueOf(jsonObject.get("isLocked").toString()));
        splwTreeDO.setOnlineNum(Integer.valueOf(jsonObject.get("onlineNum").toString()));
        splwTreeDO.setPlatformId(Integer.valueOf(jsonObject.get("platformId").toString()));
        splwTreeDO.setTotalNum(Integer.valueOf(jsonObject.get("totalNum").toString()));
        splwTreeDO.setType(1);
        splwTreeDO.setPrisonId(orgCode);

        // 获取路径信息
        Map<String, String> map1 = new HashMap<>();
        if ("001".equals(rootCode)) {
            Integer level = videoRouteService.getTreeLevel(jsonObject.getString("treeCode").toString(), 1);
            map1 = videoRouteService.getTreePathIds(jsonObject.getString("treeCode").toString(), level);
        } else {
            Integer level = videoRouteService.getTreeLevel(jsonObject.getString("treeCode").toString(), 3);
            map1 = videoRouteService.getTreePathIds3(jsonObject.getString("treeCode").toString(), level);
            if (map1 == null) {
                return null;
            }
        }

        splwTreeDO.setTreeLevel(videoRouteService.getTreeLevel(jsonObject.getString("treeCode").toString(), 1));
        splwTreeDO.setParentPathId(map1.get("pathId"));
        splwTreeDO.setPathName(map1.get("pathName"));
        splwTreeDO.setPid(Integer.valueOf(map1.get("pathId").substring(map1.get("pathId").lastIndexOf("/") + 1)));
        splwTreeDO.setName(jsonObject.getString("name"));

        return splwTreeDO;
    }

    /**
     * 创建BaseDeviceSaveReqVO对象
     */
    private BaseDeviceSaveReqVO createBaseDeviceSaveReqVO(JSONObject item, TreeNode treeNode, String orgCode) {
        BaseDeviceSaveReqVO baseDeviceDto = new BaseDeviceSaveReqVO();
        String bdAreaCode = splwTreeDao.getBDAreaCode(treeNode.getId());
        baseDeviceDto.setAreaId(StringUtil.isNotEmpty(bdAreaCode) ? bdAreaCode : treeNode.getId());
        baseDeviceDto.setGbCode(item.getString("gb28181Code"));
        baseDeviceDto.setDeviceName(item.getString("chanName"));
        baseDeviceDto.setChannelId(item.getString("chanId"));
        baseDeviceDto.setChannelName(item.getString("chanName"));
        baseDeviceDto.setDeviceStatus(item.getString("status").equals("0") ? "001" : "002");
        baseDeviceDto.setDeviceTypeId("0001");
        baseDeviceDto.setPointName(item.getString("chanName"));
        baseDeviceDto.setOrgCode(orgCode);
        baseDeviceDto.setDeviceInscreen(new BaseDeviceInscreenDO());
        return baseDeviceDto;
    }

    /**
     * 获取树路径信息
     */
    private Map<String, String> getTreePathInfo(JSONObject node, String rootCode) {
        String treeCode = node.getString("treeCode");
        Map<String, String> map = new HashMap<>();
        Integer level = 1;
        if ("001".equals(rootCode)) {
            level = videoRouteService.getTreeLevel(treeCode, 1);
            map = videoRouteService.getTreePathIds(treeCode, level);
        } else {
            level = videoRouteService.getTreeLevel(treeCode, 3);
            map = videoRouteService.getTreePathIds3(treeCode, level);
        }

        if (map == null) {
            log.warn("获取树路径信息失败，treeCode: {}", treeCode);
            return null;
        }

        map.put("level", level.toString());
        return map;
    }

    /**
     * 处理根节点
     */
    private Integer handleRootNode(JSONObject rootNode, String orgCode) {
        List<String> hasCode = splwTreeDao.isHasCode();
        Integer id;

        if (hasCode.isEmpty()) {
            id = 2; // 初始ID为2

            SplwTreeDO baseSplwTreeEntity = new SplwTreeDO();
            baseSplwTreeEntity.setId(1); // 根节点ID为1
            baseSplwTreeEntity.setOrderNo(1);
            baseSplwTreeEntity.setTreeCode(rootNode.getString("treeCode"));
            baseSplwTreeEntity.setType(1);
            baseSplwTreeEntity.setTreeLevel(0);
            baseSplwTreeEntity.setPid(-1);
            baseSplwTreeEntity.setPrisonId(orgCode);
            baseSplwTreeEntity.setParentPathId("1");
            baseSplwTreeEntity.setName(rootNode.getString("name"));
            baseSplwTreeEntity.setAllNum(rootNode.getIntValue("allNum"));
            baseSplwTreeEntity.setIsLocked(rootNode.getIntValue("isLocked"));
            baseSplwTreeEntity.setOnlineNum(rootNode.getIntValue("onlineNum"));
            baseSplwTreeEntity.setPlatformId(rootNode.getIntValue("platformId"));
            baseSplwTreeEntity.setTotalNum(rootNode.getIntValue("totalNum"));
            baseSplwTreeEntity.setGb28181Code(rootNode.getString("gb28181Code"));
            splwTreeDao.insert(baseSplwTreeEntity);
        } else {
            Integer maxCode = splwTreeDao.getMaxCodeAll();
            id = maxCode + 1;
        }

        return id;
    }

    /**
     * 获取树路径信息
     */
    private Map<String, String> getTreePathInfo(String treeCode, String rootCode) {
        Integer level;
        Map<String, String> stringMap = new HashMap<>();

        if ("001".equals(rootCode)) {
            level = videoRouteService.getTreeLevel(treeCode, 2);
            stringMap = videoRouteService.getTreePathIds(treeCode, level);
        } else {
            level = videoRouteService.getTreeLevel(treeCode, 1);
            stringMap = videoRouteService.getTreePathIds3(treeCode, level);
        }

        if (stringMap == null) {
            log.warn("获取树路径信息失败，treeCode: {}, rootCode: {}", treeCode, rootCode);
            return null;
        }

        stringMap.put("level", level.toString());
        return stringMap;
    }


    @Override
    public List<TreeNode> getAllAreaTree(BaseDevicePageReqVO treeDto) {
        List<TreeNode> areaList = baseDeviceDao.areaTree(treeDto);
        List<TreeNode> deviceList;
        if (treeDto.getSearchType() != null && treeDto.getSearchType() == 1) {
            deviceList = baseDeviceDao.meetRoomDeviceTree(treeDto);//只查看会见室审讯室设备
        } else {
            deviceList = baseDeviceDao.deviceTree(treeDto);//所有设备
        }
        for (TreeNode treeNode : deviceList) {
            //设备
            treeNode.setNodeType(1);
            if (treeNode.getParentId() != null && !"".equals(treeNode.getParentId())) {
                String roomName = baseDeviceDao.getRoomName(treeNode.getParentId());
                if (roomName != null && !"".equals(roomName)) {
                    treeNode.setRoomName(roomName);
                }
            }
        }
        for (TreeNode area : areaList) {
            area.setNodeType(0);
            List<TreeNode> children = new ArrayList<TreeNode>();
            for (TreeNode device : deviceList) {
                if (device.getParentId().equals(area.getId())) {
                    children.add(device);
                }
            }
            area.setChildren(children);
        }
        List<TreeNode> treeNodes = TreeUtil.buildByRecursive(areaList, "");
        return treeNodes;
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public Integer addDevice(BaseDeviceSaveReqVO saveReqVO) {
        if (StringUtil.isNotEmpty(saveReqVO.getChannelId())) {
            String stringWithoutSpaces = saveReqVO.getChannelId().replaceAll("\\s+", "");
            saveReqVO.setChannelId(stringWithoutSpaces);
        }
        try {
            String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
            saveReqVO.setOrgCode(orgCode);
            saveReqVO.setIpAddress(StringUtil.isNotEmpty(saveReqVO.getIpAddress()) ? saveReqVO.getIpAddress().trim() : null);
            saveReqVO.setMacAddress(StringUtil.isNotEmpty(saveReqVO.getMacAddress()) ? saveReqVO.getMacAddress().trim() : null);
            BaseDeviceDO entity = new BaseDeviceDO();
            org.springframework.beans.BeanUtils.copyProperties(saveReqVO, entity);
            String baseDeviceIdPrefix = orgCode + BASE_DEVICE_TYPE + saveReqVO.getDeviceTypeId();
            String baseDeviceMaxId = baseDeviceDao.getMaxIdByType(baseDeviceIdPrefix);
            if (StringUtil.isEmpty(baseDeviceMaxId)) {
                entity.setId(baseDeviceIdPrefix + BASE_DEVICE_START);
            } else {
                entity.setId(String.valueOf(Long.parseLong(baseDeviceMaxId) + 1));
            }
            entity.setOrgCode(orgCode);
            entity.setDeviceCode(entity.getId());
            if (entity.getDeviceTypeId().equals(DeviceTypeEnum.CAMERA.getCode()) || entity.getDeviceTypeId().equals(DeviceTypeEnum.FACE_CAMERA.getCode()) || entity.getDeviceTypeId().equals(DeviceTypeEnum.VIDEO_ANOMALY.getCode())) {
                addDeviceRelation(entity);
            }
            //仓内外屏。对讲分机

            if (entity.getDeviceTypeId().equals(DeviceTypeEnum.INDOOR_TERMINAL.getCode())
                    || entity.getDeviceTypeId().equals(DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode())
                    || entity.getDeviceTypeId().equals(DeviceTypeEnum.INTERCOM_EXTENSION.getCode())
                    || entity.getDeviceTypeId().equals(DeviceTypeEnum.INTERCOM_HOST.getCode())
            ) {
                if (saveReqVO.getDeviceInscreen() != null) {
                    //addTerminal(entity, saveReqVO.getDeviceInscreen());
                }
            }
            baseDeviceDao.insert(entity);
            /*if (DeviceTypeEnum.DEVICE_TYPE_ZJHJZD.getCode().equals(entity.getDeviceTypeId()) || DeviceTypeEnum.DEVICE_TYPE_WLRYZD.getCode().equals(entity.getDeviceTypeId())) {
                terminalServiceHandler.saveConfigInfo(entity.getId(),entity.getIpAddress(),entity.getDeviceTypeId());
            }*/
            return 1;
        } catch (Exception e) {
            log.error("新增设备失败:", e);
            return 0;
        }

    }

    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public void addDeviceRelation(BaseDeviceDO entity) {
        if (entity.getDeviceTypeId().equals(DeviceTypeEnum.CAMERA.getCode())) {
            //摄像机
            BaseDeviceCameraDO cameraEntity = new BaseDeviceCameraDO();
            org.springframework.beans.BeanUtils.copyProperties(entity, cameraEntity);
            cameraEntity.setDeviceId(entity.getId());
            cameraEntity.setDeviceIp(StringUtil.isEmpty(entity.getIpAddress()) ? "" : entity.getIpAddress());
            baseDeviceCameraDao.insert(cameraEntity);
        } else if (entity.getDeviceTypeId().equals(DeviceTypeEnum.FACE_CAMERA.getCode())) {
            //人脸摄像机
            /*BaseDeviceFaceCameraDO faceCameraEntity = new BaseDeviceFaceCameraDO();
            org.springframework.beans.BeanUtils.copyProperties(entity,faceCameraEntity);
            faceCameraEntity.setId(entity.getId());
            faceCameraEntity.setDeviceId(entity.getId());
            faceCameraEntity.setDeviceIp(StringUtil.isEmpty(entity.getIpAddress())?"":entity.getIpAddress());
            faceCameraDao.insert(faceCameraEntity);*/
        } else if (entity.getDeviceTypeId().equals(DeviceTypeEnum.VIDEO_ANOMALY.getCode())) {
            //视频异动
            BaseDeviceVideoMoveDO videoMoveEntity = new BaseDeviceVideoMoveDO();
            org.springframework.beans.BeanUtils.copyProperties(entity, videoMoveEntity);
            videoMoveEntity.setDeviceId(entity.getId());
            videoMoveEntity.setDeviceIp(StringUtil.isEmpty(entity.getIpAddress()) ? "" : entity.getIpAddress());
            //videoMoveEntity.setFactoryDeviceId(StringUtil.isEmpty(entity.getFactoryId())?"":entity.getFactoryId());
            videoMoveDao.insert(videoMoveEntity);
        }
    }

    public Integer clearDeviceTable(String channelId, String orgCode) {
        LambdaQueryWrapper<BaseDeviceDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseDeviceDO::getChannelId, channelId)
                .eq(BaseDeviceDO::getOrgCode, orgCode);

        return baseDeviceDao.delete(wrapper);

    }

    @Override
    @Async
    public void sysDeviceTree(String orgCode) {
        log.info("设备管理开始推送设备>>视频联网>>>>>>>>>>>>>开始");
        videoTree(orgCode);
        log.info("设备管理开始推送设备>>视频联网>>>>>>>>>>>>>结束");
        try {
            log.info("开始同步视频联网数据");
            videoRouteService.init();
            log.info("结束同步视频联网数据");
        } catch (Exception e) {
            log.info(e.toString());
        }
    }

    private static ConcurrentHashMap<String, String> concurrentHashMap = new ConcurrentHashMap<String, String>();

    public List<TreeNode> getAreaTree(BaseDevicePageReqVO treeDto) {
        List<TreeNode> areaList = baseDeviceDao.areaTree(treeDto);
        List<TreeNode> deviceList;
        if (treeDto.getSearchType() != null && treeDto.getSearchType() == 1) {
            deviceList = baseDeviceDao.meetRoomDeviceTree(treeDto);//只查看会见室审讯室设备
        } else {
            deviceList = baseDeviceDao.deviceTree(treeDto);//所有设备
        }
        for (TreeNode treeNode : deviceList) {
            //设备
            treeNode.setNodeType(1);
            if (treeNode.getParentId() != null && !"".equals(treeNode.getParentId())) {
                String roomName = baseDeviceDao.getRoomName(treeNode.getParentId());
                if (roomName != null && !"".equals(roomName)) {
                    treeNode.setRoomName(roomName);
                }
            }
        }
        for (TreeNode area : areaList) {
            area.setNodeType(0);
            List<TreeNode> children = new ArrayList<TreeNode>();
            for (TreeNode device : deviceList) {
                if (device.getParentId().equals(area.getId())) {
                    children.add(device);
                }
            }
            area.setChildren(children);
        }
        List<TreeNode> treeNodes = TreeUtil.buildByRecursive(areaList, "");
        treeNodes.forEach(treeNode -> {
            forEachTree(treeNode);
        });
        treeNodes.forEach(treeNode -> {
            forEachTree(treeNode);
        });
        treeNodes.forEach(treeNode -> {
            forEachTree(treeNode);
        });

        return treeNodes;
    }

    private TreeNode forEachTree(TreeNode treeNode) {
        List<TreeNode> list = treeNode.getChildren();
        for (int i = list.size() - 1; i >= 0; i--) {
            TreeNode vo = list.get(i);
            vo.setExpand(false);
            if (vo.getChildren() == null || vo.getChildren().size() == 0) {
                if (vo.getType() == null || !vo.getType().equals("camera")) {
                    list.remove(i);
                }
            } else {
                forEachTree(vo);
            }
        }
        return treeNode;
    }

    public Object videoTree(String orgCode) {
        BaseDevicePageReqVO baseDevicePageDto = new BaseDevicePageReqVO();
        baseDevicePageDto.setOrgCode(orgCode);
        List<TreeNode> areaTree = getAreaTree(baseDevicePageDto);
        for (TreeNode treeNode : areaTree) {
            if (orgCode.equals(treeNode.getOrgCode())) {
                //增加设备区域前先删除之前的区域
                AddTreeNodeRequestVO vo1 = new AddTreeNodeRequestVO();
                vo1.setName(treeNode.getTitle());
                videoRouteService.clearTreeNodeRequest(vo1);
                concurrentHashMap.clear();
                baseVidVideoDao.clearTable(orgCode);
                //增加父节点
                List<TreeNode> children = treeNode.getChildren();
                AddTreeNodeRequestVO vo = new AddTreeNodeRequestVO();
                vo.setName(treeNode.getTitle());
                vo.setGb28181Code(treeNode.getId());
                log.info("开始查找父节点");
                vo.setParentTreeCode(videoRouteService.getParentTreeCode());
                vo.setRemark(treeNode.getId());
                vo.setPrisonId(orgCode);
                log.info("视频联网设备树新增监室树父节点-----------------》");
                String s = videoRouteService.addTreeNodeRequest(vo);
                log.info("视频联网设备树新增监室树父节点完毕-----------------》" + s);
                JSONObject jsonObject = JSONObject.parseObject(s);
                JSONObject message = jsonObject.getJSONObject("message");
                String treeCode = message.getString("treeCode");
                concurrentHashMap.put(treeNode.getId(), treeCode);
                BaseVidVideoDO baseVidVideo = new BaseVidVideoDO();
                baseVidVideo.setId(StringUtil.getGuid());
                baseVidVideo.setDeviceCode(treeNode.getId());
                baseVidVideo.setTreeCode(treeCode);
                baseVidVideo.setPrisonId(orgCode);
                baseVidVideoDao.insert(baseVidVideo);
                getChildren(children);
            }
        }
        return null;
    }

    public List<TreeNode> getChildren(List<TreeNode> children) {
        for (TreeNode treeNode : children) {
            if (treeNode.getChildren() != null && treeNode.getChildren().size() > 0 && "area".equals(treeNode.getType())) {
                AddTreeNodeRequestVO vo = new AddTreeNodeRequestVO();
                vo.setName(treeNode.getTitle());
                vo.setGb28181Code(treeNode.getId());
                vo.setParentTreeCode(concurrentHashMap.get(treeNode.getParentId()));
                vo.setRemark(treeNode.getId());
                log.info("视频联网设备树新增监室树子节点-----------------》");
                String s = videoRouteService.addTreeNodeRequest(vo);
                log.info("视频联网设备树新增监室树子节点完毕-----------------》" + s);
                JSONObject jsonObject = JSONObject.parseObject(s);
                JSONObject message = jsonObject.getJSONObject("message");
                String treeCode = message.getString("treeCode");
                concurrentHashMap.put(treeNode.getId(), treeCode);
                BaseVidVideoDO baseVidVideo = new BaseVidVideoDO();
                baseVidVideo.setId(StringUtil.getGuid());
                baseVidVideo.setDeviceCode(treeNode.getId());
                baseVidVideo.setTreeCode(treeCode);
                baseVidVideo.setPrisonId(treeNode.getOrgCode());
                baseVidVideoDao.insert(baseVidVideo);
                getChildren(treeNode.getChildren());
            } else {
                if (StringUtil.isNotEmpty(treeNode.getData())) {
                    String s = videoRouteService.govDevChnnInfo(treeNode.getData());
                    JSONObject jsonObject = JSONObject.parseObject(s);
                    if (jsonObject.getInteger("returnCode") == 0) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        Map<String, Object> map = new HashMap<>();
                        map.put("channelName", data.getString("chnName"));
//                    String code = videoRouteService.getTreeCode(getChannelByPage,treeNode.getData(),map);
                        String code = videoRouteService.getChannelIds(treeNode.getData());
                        String dstTreeCode = baseVidVideoDao.getTreeCodeByDeviceCode(treeNode.getParentId());
                        AddTreeNodeRequestVO vo = new AddTreeNodeRequestVO();
                        vo.setDstTreeCode(dstTreeCode);
                        vo.setParentTreeCode(code);
                        vo.setGb28181Code(treeNode.getData());
                        vo.setName(treeNode.getTitle());
                        videoRouteService.setDevChnnRequest(treeNode.getData(), treeNode.getTitle(), data.getString("treeCode"));
                        videoRouteService.moveTreeNodeLeafRequest(vo);
                    }
                }
            }
        }
        return children;
    }
}
