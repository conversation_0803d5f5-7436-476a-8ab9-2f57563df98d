package com.rs.module.base.service.dch.job;

import com.bsp.common.util.StringUtil;
import com.rs.framework.common.cons.RedisConstants;
import com.rs.module.base.service.dch.DataChangeEventLogService;
import com.rs.module.base.service.dch.processor.DataChangeEventProcessor;
import com.rs.module.base.util.RedisDistributedLockUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据变更事件定时任务 (XXL-JOB)
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
public class DataChangeEventJob {

    @Autowired
    private DataChangeEventProcessor eventProcessor;

    @Autowired
    private DataChangeEventLogService eventLogService;

    @Autowired
    private RedisDistributedLockUtil redisLockUtil;

    /**
     * 处理待处理的事件
     */
    @XxlJob("processPendingEvents")
    @Transactional
    public void processPendingEvents() {
        String lockKey = RedisConstants.DATA_CHANGE_EVENT_PROCESSING_PENDING_LOCK;

        // 尝试获取分布式锁，锁过期时间设置为30分钟（防止任务执行时间过长）
        if (redisLockUtil.tryLock(lockKey, 1800)) {
            try {
                String uuid = StringUtil.getGuid32();
                String jobParam = XxlJobHelper.getJobParam();
                int batchSize = 500;

                // 支持通过任务参数传递批处理大小
                if (jobParam != null && !jobParam.trim().isEmpty()) {
                    try {
                        batchSize = Integer.parseInt(jobParam.trim());
                    } catch (NumberFormatException e) {
                        log.warn("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, batchSize);
                        XxlJobHelper.log("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, batchSize);
                    }
                }

                log.debug("[{}]开始处理待处理的数据变更事件，批处理大小: {}", uuid, batchSize);
                XxlJobHelper.log("[{}]开始处理待处理的数据变更事件，批处理大小: {}", uuid, batchSize);
                long startTime = System.currentTimeMillis();

                int processedCount = eventProcessor.processPendingEvents(batchSize);

                double duration = (System.currentTimeMillis() - startTime) / 1000.0;
                if (processedCount > 0) {
                    log.info("[{}]处理待处理事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                    XxlJobHelper.log("[{}]处理待处理事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                } else {
                    log.debug("[{}]处理待处理事件完成: 无待处理事件, 用时: {}s", uuid, duration);
                    XxlJobHelper.log("[{}]处理待处理事件完成: 无待处理事件, 用时: {}s", uuid, duration);
                }
            } catch (Exception e) {
                log.error("处理待处理事件异常", e);
                XxlJobHelper.handleFail("处理待处理事件异常: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 释放分布式锁
                redisLockUtil.releaseLock(lockKey);
            }
        } else {
            log.info("处理待处理事件任务正在执行中，跳过本次执行");
            XxlJobHelper.log("处理待处理事件任务正在执行中，跳过本次执行");
        }
    }

    /**
     * 处理需要重试的事件
     */
    @XxlJob("processRetryEvents")
    @Transactional
    public void processRetryEvents() {
        String lockKey = RedisConstants.DATA_CHANGE_EVENT_PROCESSING_RETRY_LOCK;

        // 尝试获取分布式锁，锁过期时间设置为30分钟（防止任务执行时间过长）
        if (redisLockUtil.tryLock(lockKey, 1800)) {
            try {
                String uuid = StringUtil.getGuid32();
                String jobParam = XxlJobHelper.getJobParam();
                int batchSize = 500;

                // 支持通过任务参数传递批处理大小
                if (jobParam != null && !jobParam.trim().isEmpty()) {
                    try {
                        batchSize = Integer.parseInt(jobParam.trim());
                    } catch (NumberFormatException e) {
                        log.warn("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, batchSize);
                        XxlJobHelper.log("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, batchSize);
                    }
                }

                log.debug("[{}]开始处理需要重试的数据变更事件，批处理大小: {}", uuid, batchSize);
                XxlJobHelper.log("[{}]开始处理需要重试的数据变更事件，批处理大小: {}", uuid, batchSize);
                long startTime = System.currentTimeMillis();

                int processedCount = eventProcessor.processRetryEvents(batchSize);

                double duration = (System.currentTimeMillis() - startTime) / 1000.0;
                if (processedCount > 0) {
                    log.info("[{}]处理重试事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                    XxlJobHelper.log("[{}]处理重试事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                } else {
                    log.debug("[{}]处理重试事件完成: 无重试事件, 用时: {}s", uuid, duration);
                    XxlJobHelper.log("[{}]处理重试事件完成: 无重试事件, 用时: {}s", uuid, duration);
                }
            } catch (Exception e) {
                log.error("处理重试事件异常", e);
                XxlJobHelper.handleFail("处理重试事件异常: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 释放分布式锁
                redisLockUtil.releaseLock(lockKey);
            }
        } else {
            log.info("处理重试事件任务正在执行中，跳过本次执行");
            XxlJobHelper.log("处理重试事件任务正在执行中，跳过本次执行");
        }
    }

    /**
     * 清理过期的事件
     */
    @XxlJob("cleanupExpiredEvents")
    @Transactional
    public void cleanupExpiredEvents() {
        String lockKey = RedisConstants.DATA_CHANGE_EVENT_CLEANING_EXPIRED_LOCK;

        // 尝试获取分布式锁，锁过期时间设置为30分钟（防止任务执行时间过长）
        if (redisLockUtil.tryLock(lockKey, 1800)) {
            try {
                String uuid = StringUtil.getGuid32();
                String jobParam = XxlJobHelper.getJobParam();
                int retentionDays = 7;

                // 支持通过任务参数传递保留天数
                if (jobParam != null && !jobParam.trim().isEmpty()) {
                    try {
                        retentionDays = Integer.parseInt(jobParam.trim());
                    } catch (NumberFormatException e) {
                        log.warn("[{}]任务参数格式错误，使用默认保留天数: {}", uuid, retentionDays);
                        XxlJobHelper.log("[{}]任务参数格式错误，使用默认保留天数: {}", uuid, retentionDays);
                    }
                }

                log.debug("[{}]开始清理过期的数据变更事件，保留天数: {}", uuid, retentionDays);
                XxlJobHelper.log("[{}]开始清理过期的数据变更事件，保留天数: {}", uuid, retentionDays);
                long startTime = System.currentTimeMillis();

                // 清理过期的成功事件
                int deletedCount = eventLogService.deleteExpiredSuccessEvents(retentionDays);

                double duration = (System.currentTimeMillis() - startTime) / 1000.0;
                if (deletedCount > 0) {
                    log.info("[{}]清理过期事件完成: 清理数量={}, 用时: {}s", uuid, deletedCount, duration);
                    XxlJobHelper.log("[{}]清理过期事件完成: 清理数量={}, 用时: {}s", uuid, deletedCount, duration);
                } else {
                    log.debug("[{}]清理过期事件完成: 无过期事件, 用时: {}s", uuid, duration);
                    XxlJobHelper.log("[{}]清理过期事件完成: 无过期事件, 用时: {}s", uuid, duration);
                }
            } catch (Exception e) {
                log.error("清理过期事件异常", e);
                XxlJobHelper.handleFail("清理过期事件异常: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 释放分布式锁
                redisLockUtil.releaseLock(lockKey);
            }
        } else {
            log.info("清理过期事件任务正在执行中，跳过本次执行");
            XxlJobHelper.log("清理过期事件任务正在执行中，跳过本次执行");
        }
    }

    /**
     * 处理指定业务类型的事件
     * 任务参数格式: businessType,limit 例如: PRISONER,100
     */
    @XxlJob("processEventsByBusinessType")
    @Transactional
    public void processEventsByBusinessType() {
        String uuid = StringUtil.getGuid32();
        String jobParam = XxlJobHelper.getJobParam();

        if (jobParam == null || jobParam.trim().isEmpty()) {
            String errorMsg = String.format("[%s]任务参数不能为空，格式: businessType,limit 例如: PRISONER,100", uuid);
            log.error(errorMsg);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.handleFail(errorMsg);
            return;
        }

        String[] params = jobParam.trim().split(",");
        if (params.length < 1 || params.length > 2) {
            String errorMsg = String.format("[%s]任务参数格式错误，格式: businessType,limit 例如: PRISONER,100", uuid);
            log.error(errorMsg);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.handleFail(errorMsg);
            return;
        }

        String businessType = params[0].trim();
        int limit = 500;

        if (params.length == 2) {
            try {
                limit = Integer.parseInt(params[1].trim());
            } catch (NumberFormatException e) {
                log.warn("[{}]限制数量参数格式错误，使用默认值: {}", uuid, 500);
                XxlJobHelper.log("[{}]限制数量参数格式错误，使用默认值: {}", uuid, limit);
            }
        }

        processEventsByBusinessTypeWithUuid(businessType, limit, uuid);
    }

    /**
     * 处理指定业务类型的事件（内部方法，支持手动调用）
     *
     * @param businessType 业务类型
     * @param limit        限制数量
     * @return 处理数量
     */
    public int processEventsByBusinessTypeInternal(String businessType, int limit) {
        String uuid = StringUtil.getGuid32();
        try {
            log.info("[{}]开始处理业务类型 {} 的事件，限制数量: {}", uuid, businessType, limit);
            long startTime = System.currentTimeMillis();

            int processedCount = eventProcessor.processEventsByBusinessType(businessType, limit);

            double duration = (System.currentTimeMillis() - startTime) / 1000.0;
            log.info("[{}]处理业务类型 {} 的事件完成: 处理数量={}, 用时: {}s", uuid, businessType, processedCount, duration);

            return processedCount;
        } catch (Exception e) {
            log.error("[{}]处理业务类型 {} 的事件异常", uuid, businessType, e);
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 处理指定业务类型的事件（带UUID的内部方法）
     *
     * @param businessType 业务类型
     * @param limit        限制数量
     * @param uuid         任务UUID
     * @return 处理数量
     */
    private int processEventsByBusinessTypeWithUuid(String businessType, int limit, String uuid) {

        try {
            log.info("[{}]开始处理业务类型 {} 的事件，限制数量: {}", uuid, businessType, limit);
            XxlJobHelper.log("[{}]开始处理业务类型 {} 的事件，限制数量: {}", uuid, businessType, limit);
            long startTime = System.currentTimeMillis();

            int processedCount = eventProcessor.processEventsByBusinessType(businessType, limit);

            double duration = (System.currentTimeMillis() - startTime) / 1000.0;
            log.info("[{}]处理业务类型 {} 的事件完成: 处理数量={}, 用时: {}s", uuid, businessType, processedCount, duration);
            XxlJobHelper.log("[{}]处理业务类型 {} 的事件完成: 处理数量={}, 用时: {}s", uuid, businessType, processedCount, duration);

            return processedCount;
        } catch (Exception e) {
            log.error("[{}]处理业务类型 {} 的事件异常", uuid, businessType, e);
            XxlJobHelper.log("[{}]处理业务类型 {} 的事件异常: {}", uuid, businessType, e.getMessage());
            XxlJobHelper.handleFail("处理业务类型事件异常: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 获取任务运行状态
     *
     * @return 状态信息
     */
    public String getJobStatus() {
        StringBuilder status = new StringBuilder();
        status.append("数据变更事件处理任务状态:\n");

        // 检查各个任务的锁状态
        boolean pendingLocked = redisLockUtil.isLocked(RedisConstants.DATA_CHANGE_EVENT_PROCESSING_PENDING_LOCK);
        boolean retryLocked = redisLockUtil.isLocked(RedisConstants.DATA_CHANGE_EVENT_PROCESSING_RETRY_LOCK);
        boolean cleaningLocked = redisLockUtil.isLocked(RedisConstants.DATA_CHANGE_EVENT_CLEANING_EXPIRED_LOCK);

        status.append("- 待处理事件任务运行中: ").append(pendingLocked).append("\n");
        status.append("- 重试事件任务运行中: ").append(retryLocked).append("\n");
        status.append("- 清理过期事件任务运行中: ").append(cleaningLocked).append("\n");

        // 添加锁的剩余时间信息
        if (pendingLocked) {
            long ttl = redisLockUtil.getLockTtl(RedisConstants.DATA_CHANGE_EVENT_PROCESSING_PENDING_LOCK);
            status.append("  - 待处理事件任务锁剩余时间: ").append(ttl).append("秒\n");
        }
        if (retryLocked) {
            long ttl = redisLockUtil.getLockTtl(RedisConstants.DATA_CHANGE_EVENT_PROCESSING_RETRY_LOCK);
            status.append("  - 重试事件任务锁剩余时间: ").append(ttl).append("秒\n");
        }
        if (cleaningLocked) {
            long ttl = redisLockUtil.getLockTtl(RedisConstants.DATA_CHANGE_EVENT_CLEANING_EXPIRED_LOCK);
            status.append("  - 清理过期事件任务锁剩余时间: ").append(ttl).append("秒\n");
        }

        return status.toString();
    }
}
