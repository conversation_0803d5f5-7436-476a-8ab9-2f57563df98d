package com.rs.module.base.dao.pm.device;

import java.util.*;
import com.rs.module.base.controller.admin.video.vo.VideoTreeVO;
import com.rs.module.base.entity.pm.device.SplwTreeDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* 实战平台-监管管理-同步高云视频联网的设备树 Dao
*
* <AUTHOR>
*/
@Mapper
public interface SplwTreeDao extends IBaseDao<SplwTreeDO> {

    @Select("SELECT a.order_no as id,a.name from acp_pm_splw_tree a WHERE tree_code = #{treeCode} LIMIT 1")
    VideoTreeVO getPathNameByTreeCode(@Param("treeCode") String treeCode);

    @Select("SELECT count(1) from acp_pm_splw_tree a WHERE name = #{name} and tree_code = #{treeCode}")
    Integer getCheckNum(@Param("name") String name,@Param("treeCode") String treeCode);

    @Delete("DELETE FROM acp_pm_splw_tree WHERE prison_id = #{prisonId}")
    Integer clearTable(@Param("prisonId") String prisonId);

    @Delete("DELETE FROM base_device WHERE channel_id = #{channelId} and prison_id = #{prisonId}")
    Integer clearDeviceTable(@Param("channelId") String channelId,@Param("prisonId") String prisonId);

    @Delete("DELETE FROM base_device_camera WHERE channel_id = (SELECT id from base_device a where a.channel_id = #{channelId} and prison_id = #{prisonId})")
    Integer clearDeviceCameraTable(@Param("channelId") String channelId,@Param("prisonId") String prisonId);

    @Select("SELECT max(order_no) from acp_pm_splw_tree a where a.prison_id = #{prisonId}")
    Integer getMaxCode(@Param("prisonId") String prisonId);

    @Select("SELECT max(order_no) from acp_pm_splw_tree a")
    Integer getMaxCodeAll();

    @Select("SELECT 1 from acp_pm_splw_tree a where a.tree_level = 0")
    List<String> isHasCode();

    @Select("SELECT bd_area_code from acp_pm_area_mapping a where a.sp_area_code = #{spAreaCode}")
    String getBDAreaCode(@Param("spAreaCode") String spAreaCode);
    List<VideoTreeVO> getTreeList(@Param("prisonId") String prisonId);



}
