package com.rs.module.base.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName RyVO
 * <AUTHOR>
 * @Date 2025/4/8 11:29
 * @Version 1.0
 */
@Data
public class RyxxVO extends BaseVO {

    @ApiModelProperty("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date csrq;
    @ApiModelProperty("风险等级")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JGRY_FXDJ")
    private String fxdj;
    @ApiModelProperty("国籍")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ")
    private String gj;
    @ApiModelProperty("工作单位")
    private String gzdw;
    @ApiModelProperty("户籍地")
    @Trans(type = TransType.DICTIONARY, key = "ZD_HJ")
    private String hjd;
    @ApiModelProperty("户籍地详址")
    private String hjdxz;
    @ApiModelProperty("婚姻状况")
    @Trans(type = TransType.DICTIONARY, key = "ZD_HYZK")
    private String hyzk;
    @ApiModelProperty("籍贯")
    @Trans(type = TransType.DICTIONARY, key = "ZD_HJ")
    private String jg;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("民族")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MZ")
    private String mz;
    @ApiModelProperty("人员状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_RYZT")
    private String ryzt;
    @ApiModelProperty("识别服号")
    private String sbfh;
    @ApiModelProperty("识别服标识")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SBFBS")
    private String sbfbs;
    @ApiModelProperty("身份")
    private String sf;
    @ApiModelProperty("身份核实")
    private String sfhs;
    @ApiModelProperty("身高")
    private String sg;
    @ApiModelProperty("文化程度")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WHCD")
    private String whcd;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("姓名拼音")
    private String xmpy;
    @ApiModelProperty("现住址")
    private String xzz;
    @ApiModelProperty("现住址详址")
    private String xzzxz;
    @ApiModelProperty("足长")
    private String zc;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZJLX")
    private String zjlx;
    @ApiModelProperty("正面照片")
    private String frontPhoto;
    @ApiModelProperty("左侧照片")
    private String leftPhoto;
    @ApiModelProperty("右侧照片")
    private String rightPhoto;
    @ApiModelProperty("档案编号")
    private String dabh;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("监区id")
    private String areaId;
    @ApiModelProperty("监区名称")
    private String areaName;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB", ref = "xbName")
    private String xb;
    private String xbName;
    @ApiModelProperty("被监管人员类型（01：在押人员，02：被拘人员，03：戒毒人员，99：其他）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BJGRYLX")
    private String bjgrylx;
    @ApiModelProperty("年龄")
    private Integer age;
    @ApiModelProperty("主管、协管民警")
    private List<PrisonRoomWarderRespVO> zgzjList;
    @ApiModelProperty("主管民警名称")
    private String zgmjName;
    @ApiModelProperty("主管民警身份证号")
    private String zgmjSfzh;
    @ApiModelProperty("协管民警名称")
    private String xgmjName;
}
