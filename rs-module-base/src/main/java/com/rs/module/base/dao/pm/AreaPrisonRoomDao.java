package com.rs.module.base.dao.pm;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomListReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageWithViolationReqVO;
import com.rs.module.base.controller.admin.pm.vo.terminal.AreaPrisonRoomExportReqVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 实战平台-监管管理-区域监室 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AreaPrisonRoomDao extends IBaseDao<AreaPrisonRoomDO> {


    default PageResult<AreaPrisonRoomDO> selectPage(AreaPrisonRoomPageReqVO reqVO) {
        Page<AreaPrisonRoomDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<AreaPrisonRoomDO> wrapper = new LambdaQueryWrapperX<AreaPrisonRoomDO>()
            .eqIfPresent(AreaPrisonRoomDO::getIsDel, reqVO.getIsDel())
            .betweenIfPresent(AreaPrisonRoomDO::getAddTime, reqVO.getAddTime())
            .eqIfPresent(AreaPrisonRoomDO::getAddUser, reqVO.getAddUser())
            .eqIfPresent(AreaPrisonRoomDO::getUpdateUser, reqVO.getUpdateUser())
            .eqIfPresent(AreaPrisonRoomDO::getCityCode, reqVO.getCityCode())
            .likeIfPresent(AreaPrisonRoomDO::getCityName, reqVO.getCityName())
            .eqIfPresent(AreaPrisonRoomDO::getRegCode, reqVO.getRegCode())
            .likeIfPresent(AreaPrisonRoomDO::getRegName, reqVO.getRegName())
            .eqIfPresent(AreaPrisonRoomDO::getOrgCode, reqVO.getOrgCode())
            .likeIfPresent(AreaPrisonRoomDO::getOrgName, reqVO.getOrgName())
            .likeIfPresent(AreaPrisonRoomDO::getRoomName, reqVO.getRoomName())
            .eqIfPresent(AreaPrisonRoomDO::getStatus, reqVO.getStatus())
            .eqIfPresent(AreaPrisonRoomDO::getImprisonmentAmount, reqVO.getImprisonmentAmount())
            .eqIfPresent(AreaPrisonRoomDO::getSquadronId, reqVO.getSquadronId())
            .eqIfPresent(AreaPrisonRoomDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(AreaPrisonRoomDO::getRoomCode, reqVO.getRoomCode())
            .eqIfPresent(AreaPrisonRoomDO::getRoomType, reqVO.getRoomType())
            .eqIfPresent(AreaPrisonRoomDO::getRoomSex, reqVO.getRoomSex())
            .eqIfPresent(AreaPrisonRoomDO::getPlanImprisonmentAmount, reqVO.getPlanImprisonmentAmount())
            .eqIfPresent(AreaPrisonRoomDO::getAreaId, reqVO.getAreaId())
            .likeIfPresent(AreaPrisonRoomDO::getAreaName, reqVO.getAreaName())
            .eqIfPresent(AreaPrisonRoomDO::getRoomArea, reqVO.getRoomArea())
            .eqIfPresent(AreaPrisonRoomDO::getAvgBedsArea, reqVO.getAvgBedsArea())
            .eqIfPresent(AreaPrisonRoomDO::getIsLevelRisk, reqVO.getIsLevelRisk())
            .eqIfPresent(AreaPrisonRoomDO::getSelfAreaId, reqVO.getSelfAreaId())
            .eqIfPresent(AreaPrisonRoomDO::getFxdj, reqVO.getFxdj())
            .eqIfPresent(AreaPrisonRoomDO::getYjsj, reqVO.getYjsj())
            .eqIfPresent(AreaPrisonRoomDO::getAutoFlag, reqVO.getAutoFlag())
            .eqIfPresent(AreaPrisonRoomDO::getRoomLevel, reqVO.getRoomLevel());

        if(CollUtil.isNotEmpty(reqVO.getRoomCodes())){
            wrapper.in(AreaPrisonRoomDO::getRoomCode, reqVO.getRoomCodes());
        }

        if(CollUtil.isNotEmpty(reqVO.getNotRoomCodes())){
            wrapper.notIn(AreaPrisonRoomDO::getRoomCode, reqVO.getNotRoomCodes());
        }

        // 排除空监室
        if(reqVO.getAirMonitoringRoom() != null && reqVO.getAirMonitoringRoom()){
            wrapper.ge(AreaPrisonRoomDO::getImprisonmentAmount, 0);
        }

        //排除单人监室
        if(reqVO.getSingleCellRoom() != null && reqVO.getSingleCellRoom()){
            //19 单独关押
            wrapper.notIn(AreaPrisonRoomDO::getRoomType, "19");
        }

        //排除过渡监室
        if(reqVO.getTransitionRoom() != null && reqVO.getTransitionRoom()){
            wrapper.notIn(AreaPrisonRoomDO::getRoomType, "7");
        }

        //排除不同性别
        if(reqVO.getExcludeSex() != null && reqVO.getExcludeSex() && CollUtil.isNotEmpty(reqVO.getExcludeSexList())){
            wrapper.in(AreaPrisonRoomDO::getRoomSex, reqVO.getExcludeSexList());
        }

        //根据监区去查询
        if(CollUtil.isNotEmpty(reqVO.getAreaIds())){
            wrapper.in(AreaPrisonRoomDO::getAreaId, reqVO.getAreaIds());
        }

        //监室号 关联表
        if(reqVO.getIsSql()){
            wrapper.notInSql(AreaPrisonRoomDO::getRoomCode, reqVO.getJshSql());
        }

        //@ApiModelProperty("排除历史同监室的")
        //private Boolean excludeHistoryRoom;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(AreaPrisonRoomDO::getAddTime);
        }
        Page<AreaPrisonRoomDO> areaPrisonRoomPage = selectPage(page, wrapper);
        return new PageResult<>(areaPrisonRoomPage.getRecords(), areaPrisonRoomPage.getTotal());
    }

    default List<AreaPrisonRoomDO> selectList(AreaPrisonRoomListReqVO reqVO) {
        LambdaQueryWrapperX<AreaPrisonRoomDO> wrapper = new LambdaQueryWrapperX<AreaPrisonRoomDO>()
                .eqIfPresent(AreaPrisonRoomDO::getIsDel, reqVO.getIsDel())
                .betweenIfPresent(AreaPrisonRoomDO::getAddTime, reqVO.getAddTime())
                .eqIfPresent(AreaPrisonRoomDO::getAddUser, reqVO.getAddUser())
                .eqIfPresent(AreaPrisonRoomDO::getUpdateUser, reqVO.getUpdateUser())
                .eqIfPresent(AreaPrisonRoomDO::getCityCode, reqVO.getCityCode())
                .likeIfPresent(AreaPrisonRoomDO::getCityName, reqVO.getCityName())
                .eqIfPresent(AreaPrisonRoomDO::getRegCode, reqVO.getRegCode())
                .likeIfPresent(AreaPrisonRoomDO::getRegName, reqVO.getRegName())
                .eqIfPresent(AreaPrisonRoomDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(AreaPrisonRoomDO::getOrgName, reqVO.getOrgName())
                .likeIfPresent(AreaPrisonRoomDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(AreaPrisonRoomDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AreaPrisonRoomDO::getImprisonmentAmount, reqVO.getImprisonmentAmount())
                .eqIfPresent(AreaPrisonRoomDO::getSquadronId, reqVO.getSquadronId())
                .eqIfPresent(AreaPrisonRoomDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(AreaPrisonRoomDO::getRoomCode, reqVO.getRoomCode())
                .eqIfPresent(AreaPrisonRoomDO::getRoomType, reqVO.getRoomType())
                .eqIfPresent(AreaPrisonRoomDO::getRoomSex, reqVO.getRoomSex())
                .eqIfPresent(AreaPrisonRoomDO::getPlanImprisonmentAmount, reqVO.getPlanImprisonmentAmount())
                .eqIfPresent(AreaPrisonRoomDO::getAreaId, reqVO.getAreaId())
                .likeIfPresent(AreaPrisonRoomDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(AreaPrisonRoomDO::getRoomArea, reqVO.getRoomArea())
                .eqIfPresent(AreaPrisonRoomDO::getAvgBedsArea, reqVO.getAvgBedsArea())
                .eqIfPresent(AreaPrisonRoomDO::getIsLevelRisk, reqVO.getIsLevelRisk())
                .eqIfPresent(AreaPrisonRoomDO::getSelfAreaId, reqVO.getSelfAreaId())
                .eqIfPresent(AreaPrisonRoomDO::getFxdj, reqVO.getFxdj())
                .eqIfPresent(AreaPrisonRoomDO::getYjsj, reqVO.getYjsj())
                .eqIfPresent(AreaPrisonRoomDO::getAutoFlag, reqVO.getAutoFlag())
                .eqIfPresent(AreaPrisonRoomDO::getRoomLevel, reqVO.getRoomLevel());
        return selectList(wrapper);
    }


    void deleteAreaPrisonRoomByRoomCode(String id);

    /**
     * 排除同案同监室后，获取出的监室号
     * @param orgCode
     * @return
     */
    List<String> getNotCaseRoomInfo(@Param("orgCode") String orgCode);

    /**
     * 获取历史监室
     * @param jgrybmList
     * @return
     */
    List<String> getHistoryRoomList(@Param("jgrybmList") List<String> jgrybmList);

    List<AreaPrisonRoomDO> selectListByRoomCode(String roomCode);

    default List<AreaPrisonRoomDO> selectListByRoomIds(List<String> roomIds) {
        return selectList(new LambdaQueryWrapper<AreaPrisonRoomDO>()
                .in(AreaPrisonRoomDO::getRoomCode, roomIds));
    }

    default List<AreaPrisonRoomDO> selectByOrgCode(String orgCode) {
        return selectList(new LambdaQueryWrapperX<AreaPrisonRoomDO>()
                .eq(AreaPrisonRoomDO::getOrgCode, orgCode));
    }

    default AreaPrisonRoomDO selectByRoomCode(String roomCode) {
        return selectOne(new LambdaQueryWrapperX<AreaPrisonRoomDO>()
                .eq(AreaPrisonRoomDO::getRoomCode, roomCode));
    }

    default AreaPrisonRoomDO selectByRoomCode(String orgCode, String roomCode) {
        return selectOne(new LambdaQueryWrapperX<AreaPrisonRoomDO>()
                .eq(AreaPrisonRoomDO::getOrgCode, orgCode)
                .eq(AreaPrisonRoomDO::getRoomCode, roomCode));
    }

    default List<AreaPrisonRoomDO> findExportListBy(AreaPrisonRoomExportReqVO exportReqVO) {
        return selectList(new LambdaQueryWrapperX<AreaPrisonRoomDO>()
                .inIfPresent(AreaPrisonRoomDO::getId, exportReqVO.getIds())
                .eqIfPresent(AreaPrisonRoomDO::getOrgCode, exportReqVO.getOrgCode())
                .eqIfPresent(AreaPrisonRoomDO::getRoomCode, exportReqVO.getRoomCode())
                .eqIfPresent(AreaPrisonRoomDO::getRoomName, exportReqVO.getRoomName())
                .eqIfPresent(AreaPrisonRoomDO::getStatus, exportReqVO.getStatus())
                .eqIfPresent(AreaPrisonRoomDO::getRoomType, exportReqVO.getRoomType())
                .eqIfPresent(AreaPrisonRoomDO::getAreaId, exportReqVO.getAreaId()));
    }

    String getMaxIdByRoomType(String str);


    Page<AreaPrisonRoomDO> getRoomWithViolationPage(Page<AreaPrisonRoomDO> page, @Param("req") AreaPrisonRoomPageWithViolationReqVO req);

    List<JSONObject> getRoomPeopleCount(@Param("roomCodeList") List<String> roomCodeList);

}
