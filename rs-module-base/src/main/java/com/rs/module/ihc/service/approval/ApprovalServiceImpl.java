package com.rs.module.ihc.service.approval;

import cn.hutool.core.map.MapBuilder;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.GjApprovalTraceVO;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.enums.ApplyStatusEnum;
import com.rs.module.ihc.util.TraceUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.util.List;

/**
 * @ClassName ApprovalServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/7 14:18
 * @Version 1.0
 */
@Log4j2
public abstract class ApprovalServiceImpl<DAO extends IBaseDao<DO>, DO> extends BaseServiceImpl<DAO, DO> implements ApprovalService<DO> {

    @Autowired
    private DAO baseDao;

    @Autowired
    private BpmApi bpmApi;


    /**
     * 消息url封装
     *
     * @param entityDO
     * @return
     */
    public abstract String getUrl(DO entityDO);

    /**
     * 流程定义key
     *
     * @return
     */

    public abstract String getDefKey();

    /**
     * 消息标题
     *
     * @param entity
     * @return
     */
    public abstract String getMsgTitle(DO entity);

    /**
     * 开始成功
     *
     * @param entity
     * @param result
     */

    public abstract void startSuccess(DO entity, JSONObject result);

    /**
     * 开始失败
     *
     * @param entity
     * @param result
     */
    public abstract void startFail(DO entity, JSONObject result);


    /**
     * 审批成功回调
     *
     * @param entity
     * @param approveReqVO
     */

    public abstract void approvePassedSuccess(DO entity, SimpleApproveReqVO approveReqVO, JSONObject result);

    public abstract void approveNotPassedEndSuccess(DO entity, SimpleApproveReqVO approveReqVO, JSONObject result);


    /**
     * 审批失败回调
     *
     * @param entity
     * @param approveReqVO
     */

    public abstract void approvePassedFail(DO entity, SimpleApproveReqVO approveReqVO, JSONObject result);

    public abstract void approveNotPassedEndFail(DO entity, SimpleApproveReqVO approveReqVO, JSONObject result);

    /**
     * 流程完结回调
     *
     * @param entity
     * @param approveReqVO
     */
    public abstract void finishNotPassedEnd(DO entity, SimpleApproveReqVO approveReqVO);

    public abstract void finishPassed(DO entity, SimpleApproveReqVO approveReqVO);


    @Override
    public JSONObject startProcess(MapBuilder<String, Object> variables, DO entity) {
        JSONObject result = BspApprovalUtil.commonStartProcess(getDefKey(), getId(entity), getMsgTitle(entity), getUrl(entity), variables.build(), HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") != HttpStatus.OK.value()) {
            startFail(entity, result);
            throw new ServerException("流程启动失败");
        }
        startSuccess(entity, result);
        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");

        // 通过反射设置entity字段
        try {
            // 使用专门的方法设置流程相关字段
            setActInstId(entity, bpmTrail.getString("actInstId"));
            setTaskId(entity, bpmTrail.getString("taskId"));
            // 更新实体
            baseDao.updateById(entity);

        } catch (Exception e) {
            log.error("通过反射设置entity字段失败", e);
            throw new ServerException("设置流程字段失败");
        }
        return result;
    }

    @Override
    public void approve(SimpleApproveReqVO approveReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        DO entity = baseDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, "记录不存在！");
        String currentStatus = getStatus(entity);

        if (!ApplyStatusEnum.DSP.getCode().equals(currentStatus)) {
            throw new ServerException("该状态[" + ApplyStatusEnum.getByCode(currentStatus) + "]不允许审批！");
        }
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(getTaskId(entity), sessionUser.getIdCard());
        Assert.isTrue(isApproval, "当前人无审批权限");

        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf(BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
        }
        JSONObject result = BspApprovalUtil.approvalProcessAcp(getDefKey(),
                getActInstId(entity),
                getTaskId(entity),
                getId(entity),
                bspApproceStatusEnum,
                approveReqVO.getApprovalComments());

        log.info("=======result:{}", result);
        if (result.getIntValue("code") != HttpStatus.OK.value()) {
            if (String.valueOf(BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
                approvePassedFail(entity, approveReqVO, result);
            } else {
                approveNotPassedEndFail(entity, approveReqVO, result);
            }
            throw new ServerException("流程审批失败");
        }
        if (String.valueOf(BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
            approvePassedSuccess(entity, approveReqVO, result);
        } else {
            approveNotPassedEndSuccess(entity, approveReqVO, result);
        }
        Boolean finishProcinst = bpmApi.isFinishProcinst(getActInstId(entity));
        if (finishProcinst != null && !finishProcinst) {
            setStatus(entity, ApplyStatusEnum.DSP.getCode());
        } else {
            if (String.valueOf(BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
                finishPassed(entity, approveReqVO);
            } else {
                finishNotPassedEnd(entity, approveReqVO);
            }
        }

        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        setTaskId(entity, bpmTrail.getString("taskId"));
        baseDao.updateById(entity);
    }

    @Override
    public JSONObject getCurrentApproveUser(String taskId) {
        return bpmApi.currentApproveUser(taskId);
    }

    @Override
    public List<GjApprovalTraceVO> converBspApprovalTrack(String actInstId) {
        return TraceUtil.converBspApprovalTrack(actInstId);
    }

    /**
     * 通过反射设置对象字段值
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @param value     字段值
     */
    private void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            Class<?> clazz = obj.getClass();
            Field field = null;

            // 查找字段，包括父类中的字段
            while (clazz != null && field == null) {
                try {
                    field = clazz.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    clazz = clazz.getSuperclass();
                }
            }

            if (field != null) {
                field.setAccessible(true);
                field.set(obj, value);
                log.debug("成功设置字段 {} 的值为: {}", fieldName, value);
            } else {
                log.warn("未找到字段: {}", fieldName);
            }
        } catch (Exception e) {
            log.error("设置字段 {} 失败", fieldName, e);
            throw new RuntimeException("设置字段失败: " + fieldName, e);
        }
    }

    /**
     * 通过反射获取对象字段值
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private Object getFieldValue(Object obj, String fieldName) {
        try {
            Class<?> clazz = obj.getClass();
            Field field = null;

            // 查找字段，包括父类中的字段
            while (clazz != null && field == null) {
                try {
                    field = clazz.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    clazz = clazz.getSuperclass();
                }
            }

            if (field != null) {
                field.setAccessible(true);
                Object value = field.get(obj);
                log.debug("成功获取字段 {} 的值: {}", fieldName, value);
                return value;
            } else {
                log.warn("未找到字段: {}", fieldName);
                return null;
            }
        } catch (Exception e) {
            log.error("获取字段 {} 失败", fieldName, e);
            throw new RuntimeException("获取字段失败: " + fieldName, e);
        }
    }

    /**
     * 通过反射获取对象字段值（泛型版本）
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @param clazz     期望的返回类型
     * @return 字段值
     */
    @SuppressWarnings("unchecked")
    private <T> T getFieldValue(Object obj, String fieldName, Class<T> clazz) {
        Object value = getFieldValue(obj, fieldName);
        if (value == null) {
            return null;
        }

        try {
            return (T) value;
        } catch (ClassCastException e) {
            log.error("字段 {} 的值类型转换失败，期望类型: {}，实际类型: {}",
                    fieldName, clazz.getSimpleName(), value.getClass().getSimpleName());
            throw new RuntimeException("字段类型转换失败: " + fieldName, e);
        }
    }

    /**
     * 通过反射获取实体的流程实例ID
     *
     * @param entity 实体对象
     * @return 流程实例ID
     */
    protected String getActInstId(DO entity) {
        return getFieldValue(entity, "actInstId", String.class);
    }

    protected String getId(DO entity) {
        return getFieldValue(entity, "id", String.class);
    }

    /**
     * 通过反射获取实体的任务ID
     *
     * @param entity 实体对象
     * @return 任务ID
     */
    protected String getTaskId(DO entity) {
        return getFieldValue(entity, "taskId", String.class);
    }

    /**
     * 通过反射获取实体的状态
     *
     * @param entity 实体对象
     * @return 状态值
     */
    protected String getStatus(DO entity) {
        return getFieldValue(entity, "status", String.class);
    }

    /**
     * 通过反射批量获取实体的流程相关字段
     *
     * @param entity 实体对象
     * @return 包含actInstId、taskId、status的对象
     */
    protected ProcessFieldsInfo getProcessFields(DO entity) {
        String actInstId = getActInstId(entity);
        String taskId = getTaskId(entity);
        String status = getStatus(entity);

        return new ProcessFieldsInfo(actInstId, taskId, status);
    }

    /**
     * 通过反射设置实体的流程实例ID
     *
     * @param entity    实体对象
     * @param actInstId 流程实例ID
     */
    protected void setActInstId(DO entity, String actInstId) {
        setFieldValue(entity, "actInstId", actInstId);
    }

    /**
     * 通过反射设置实体的任务ID
     *
     * @param entity 实体对象
     * @param taskId 任务ID
     */
    protected void setTaskId(DO entity, String taskId) {
        setFieldValue(entity, "taskId", taskId);
    }

    /**
     * 通过反射设置实体的状态
     *
     * @param entity 实体对象
     * @param status 状态值
     */
    protected void setStatus(DO entity, String status) {
        setFieldValue(entity, "status", status);
    }

    /**
     * 通过反射批量设置实体的流程相关字段
     *
     * @param entity    实体对象
     * @param actInstId 流程实例ID
     * @param taskId    任务ID
     * @param status    状态值
     */
    protected void setProcessFields(DO entity, String actInstId, String taskId, String status) {
        if (actInstId != null) {
            setActInstId(entity, actInstId);
        }
        if (taskId != null) {
            setTaskId(entity, taskId);
        }
        if (status != null) {
            setStatus(entity, status);
        }
    }

    /**
     * 通过反射批量设置实体的流程相关字段（使用ProcessFieldsInfo对象）
     *
     * @param entity        实体对象
     * @param processFields 流程字段信息对象
     */
    protected void setProcessFields(DO entity, ProcessFieldsInfo processFields) {
        if (processFields != null) {
            setProcessFields(entity, processFields.getActInstId(), processFields.getTaskId(), processFields.getStatus());
        }
    }

    /**
     * 流程字段信息封装类
     */
    protected static class ProcessFieldsInfo {
        private final String actInstId;
        private final String taskId;
        private final String status;

        public ProcessFieldsInfo(String actInstId, String taskId, String status) {
            this.actInstId = actInstId;
            this.taskId = taskId;
            this.status = status;
        }

        public String getActInstId() {
            return actInstId;
        }

        public String getTaskId() {
            return taskId;
        }

        public String getStatus() {
            return status;
        }

        @Override
        public String toString() {
            return "ProcessFieldsInfo{" +
                    "actInstId='" + actInstId + '\'' +
                    ", taskId='" + taskId + '\'' +
                    ", status='" + status + '\'' +
                    '}';
        }
    }


}
