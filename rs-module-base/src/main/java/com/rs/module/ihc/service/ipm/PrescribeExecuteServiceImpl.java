package com.rs.module.ihc.service.ipm;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.dao.ipm.PrescribeDao;
import com.rs.module.ihc.dao.ipm.PrescribeExecuteDao;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.PrescribeExecuteDO;
import com.rs.module.pdf.enums.PdfTemplateEnum;
import com.rs.util.PDFUtil;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;


/**
 * 所内就医-医嘱/处方执行记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrescribeExecuteServiceImpl extends BaseServiceImpl<PrescribeExecuteDao, PrescribeExecuteDO> implements PrescribeExecuteService {

    @Resource
    private PrescribeExecuteDao prescribeExecuteDao;

    @Resource
    private PrescribeDao prescribeDao;

    @Resource
    PrisonerService prisonerService;

    @Autowired
    private FileStorageService fileStorageService;

    @Override
    public String createPrescribeExecute(PrescribeExecuteSaveReqVO createReqVO) {
        // 插入
        PrescribeExecuteDO prescribeExecute = BeanUtils.toBean(createReqVO, PrescribeExecuteDO.class);
        prescribeExecuteDao.insert(prescribeExecute);
        // 返回
        return prescribeExecute.getId();
    }

    @Override
    public void updatePrescribeExecute(PrescribeExecuteSaveReqVO updateReqVO) {
        // 校验存在
        validatePrescribeExecuteExists(updateReqVO.getId());
        // 更新
        PrescribeExecuteDO updateObj = BeanUtils.toBean(updateReqVO, PrescribeExecuteDO.class);
        prescribeExecuteDao.updateById(updateObj);
    }

    @Override
    public void deletePrescribeExecute(String id) {
        // 校验存在
        validatePrescribeExecuteExists(id);
        // 删除
        prescribeExecuteDao.deleteById(id);
    }

    private void validatePrescribeExecuteExists(String id) {
        if (prescribeExecuteDao.selectById(id) == null) {
            throw new ServerException("所内就医-医嘱/处方执行记录数据不存在");
        }
    }

    @Override
    public PrescribeExecuteDO getPrescribeExecute(String id) {
        return prescribeExecuteDao.selectById(id);
    }

    @Override
    public PageResult<PrescribeExecuteDO> getPrescribeExecutePage(PrescribeExecutePageReqVO pageReqVO) {
        return prescribeExecuteDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrescribeExecuteDO> getPrescribeExecuteList(PrescribeExecuteListReqVO listReqVO) {
        return prescribeExecuteDao.selectList(listReqVO);
    }

    @Override
    public List<PrescribeExecuteFytzRespVO> fyList(PrescribeExecuteFytzReqVO prescribeExecuteListReqVO) {
        return prescribeExecuteDao.selectFytz(prescribeExecuteListReqVO);
    }

    @Override
    public List<PrescribeExecuteFytzRespVO> selectFxgzsTodo(PrescribeExecuteFxgzTodoReqVO prescribeExecuteListReqVO) {
        return prescribeExecuteDao.selectFxgzsTodo(prescribeExecuteListReqVO);
    }

    @Override
    public List<PrescribeExecuteFytzRespVO> selectFxgzsSuccess(PrescribeExecuteFxgzTodoReqVO prescribeExecuteListReqVO) {
        return prescribeExecuteDao.selectFxgzsSuccess(prescribeExecuteListReqVO);
    }

    @Override
    public byte[] getRiskDisclosurePdf(String executionId) {
        PrescribeExecuteDO prescribeExecuteDO = prescribeExecuteDao.selectById(executionId);
        PrescribeDO prescribeDO = prescribeDao.selectById(prescribeExecuteDO.getPrescribeId());
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(prescribeDO.getJgrybm());
        PrescribeExecutePrintVO prescribeExecuteRespVO = BeanUtils.toBean(prescribeExecuteDO, PrescribeExecutePrintVO.class);
        prescribeExecuteRespVO.setJgryxx(prisoner);
        prescribeExecuteRespVO.setPrescribe(prescribeDO);
        return PDFUtil.getPdf(PdfTemplateEnum.JJHFYXZLFXGZS, prescribeExecuteRespVO);
    }

    @Override
    public void signRiskDisclosurePdf(SignRiskDisclosurePdfVO signRiskDisclosurePdfVO) {
        PrescribeExecuteDO prescribeExecuteDO = prescribeExecuteDao.selectById(signRiskDisclosurePdfVO.getExecutionId());
        prescribeExecuteDO.setHzyj(signRiskDisclosurePdfVO.getPatientOpinion());
        prescribeExecuteDO.setNyTime(new Date());
        prescribeExecuteDO.setNyUrl(signRiskDisclosurePdfVO.getFingerprintUrl());
        prescribeExecuteDO.setSignPicUrl(signRiskDisclosurePdfVO.getSignPicUrl());
        prescribeExecuteDao.updateById(prescribeExecuteDO);
        byte[] riskDisclosurePdf = getRiskDisclosurePdf(signRiskDisclosurePdfVO.getExecutionId());

        String userDir = System.getProperty("user.dir");
        Path targetPath = Paths.get(userDir, "risk_disclosure_" + signRiskDisclosurePdfVO.getExecutionId() + ".pdf");
        try {
            Files.write(targetPath, riskDisclosurePdf);
            FileInfo fileInfo = fileStorageService.of(targetPath.toFile())
                    .setHashCalculatorMd5()
                    .upload();
            prescribeExecuteDO.setFxgzsUrl(fileInfo.getUrl());
            prescribeExecuteDao.updateById(prescribeExecuteDO);
            targetPath.toFile().delete();
        } catch (IOException e) {
            log.error(e.getMessage());
        }

    }


}
