package com.rs.module.ihc.enums;

/**
 * 审批状态
 * <AUTHOR>
 * @date 2025/7/7 15:12
 */
public enum ApplyStatusEnum {
    DSP("01", "待审批"),
    YZZ("02","已终止"),
    YWC("03","已完成");

    private String code;
    private String name;

    ApplyStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }


    public static ApplyStatusEnum getByCode(String code) {
        for (ApplyStatusEnum value : ApplyStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
