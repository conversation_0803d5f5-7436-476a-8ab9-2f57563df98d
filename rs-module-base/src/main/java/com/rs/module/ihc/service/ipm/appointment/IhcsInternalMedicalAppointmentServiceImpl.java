package com.rs.module.ihc.service.ipm.appointment;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.BizException;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.ihc.constant.*;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.AuditMedicalAppointmentDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.BatchAuditNoRequireProcessDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.SaveMedicalAppointmentDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.GetMedicalAppointmentByIdVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentListReqVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentPageReqVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.IhcsInternalMedicalAppointmentSaveReqVO;
import com.rs.module.ihc.dao.ipm.OutpatientDao;
import com.rs.module.ihc.dao.ipm.VisitDao;
import com.rs.module.ihc.dao.ipm.appointment.IhcsInternalMedicalAppointmentDao;
import com.rs.module.ihc.entity.ipm.OutpatientDO;
import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;
import com.rs.module.ihc.entity.pm.sick.SeverelySickManageDO;
import com.rs.module.ihc.enums.InternalMedicalAppointmentDiseaseLevelEnum;
import com.rs.module.ihc.enums.InternalMedicalAppointmentProcessMethodEnum;
import com.rs.module.ihc.enums.SpecialPatientStatusEnum;
import com.rs.module.ihc.service.pm.sick.SeverelySickManageService;
import com.rs.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 所内就医-预约登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IhcsInternalMedicalAppointmentServiceImpl extends BaseServiceImpl<IhcsInternalMedicalAppointmentDao, IhcsInternalMedicalAppointmentDO> implements IhcsInternalMedicalAppointmentService {

    @Resource
    private IhcsInternalMedicalAppointmentDao sInternalMedicalAppointmentDao;


    @Resource
    private PrisonerService prisonerService;
    @Resource
    private VisitDao visitDao;
    @Resource
    private OutpatientDao outpatientDao;
    @Resource
    private SeverelySickManageService severelySickManageService;

//    @Override
//    public String createsInternalMedicalAppointment(IhcsInternalMedicalAppointmentSaveReqVO createReqVO) {
//        // 插入
//        IhcsInternalMedicalAppointmentDO sInternalMedicalAppointment = BeanUtils.toBean(createReqVO, IhcsInternalMedicalAppointmentDO.class);
//        sInternalMedicalAppointmentDao.insert(sInternalMedicalAppointment);
//        // 返回
//        return sInternalMedicalAppointment.getId();
//    }

    @Override
    public String createsInternalMedicalAppointment(SaveMedicalAppointmentDTO saveMedicalAppointmentDTO) {
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(saveMedicalAppointmentDTO.getSupervisedUserCode());
        Integer sickType = this.getPrisonerIsSickType(saveMedicalAppointmentDTO);
        LocalDateTime nowDateTime = LocalDateTime.now();
        String appointmentNumber = this.createAppointmentNumber(nowDateTime);
        IhcsInternalMedicalAppointmentDO ihcsInternalMedicalAppointment = IhcsInternalMedicalAppointmentDO.builder()
                .diseaseTime(new Date())
                .sickType(sickType)
                .processMethod(InternalMedicalAppointmentProcessMethodEnum.NO_PROCESS.getCode())
                .supervisedUserCode(saveMedicalAppointmentDTO.getSupervisedUserCode())
                .diseaseReason(saveMedicalAppointmentDTO.getDiseaseReason())
                .remoteDiagnoseStatus(RemoteDiagnoseStatusConstant.WAIT_HANDLE)
                .appointmentNum(appointmentNumber)
                .prisonId(prisoner.getOrgCode())
                .build();
        try {
            this.save(ihcsInternalMedicalAppointment);
        } catch (DuplicateKeyException e) {
            throw new BizException("与他人同时预约冲突，请重试");
        }
        return ihcsInternalMedicalAppointment.getId();

    }

    @Override
    public void updatesInternalMedicalAppointment(IhcsInternalMedicalAppointmentSaveReqVO updateReqVO) {
        // 校验存在
        validatesInternalMedicalAppointmentExists(updateReqVO.getId());
        // 更新
        IhcsInternalMedicalAppointmentDO updateObj = BeanUtils.toBean(updateReqVO, IhcsInternalMedicalAppointmentDO.class);
        sInternalMedicalAppointmentDao.updateById(updateObj);
    }

    @Override
    public void deletesInternalMedicalAppointment(String id) {
        // 校验存在
        validatesInternalMedicalAppointmentExists(id);
        // 删除
        sInternalMedicalAppointmentDao.deleteById(id);
    }

    private void validatesInternalMedicalAppointmentExists(String id) {
        if (sInternalMedicalAppointmentDao.selectById(id) == null) {
            throw new ServerException("所内就医-预约登记数据不存在");
        }
    }

    @Override
    public IhcsInternalMedicalAppointmentDO getsInternalMedicalAppointment(String id) {
        return sInternalMedicalAppointmentDao.selectById(id);
    }

    @Override
    public PageResult<IhcsInternalMedicalAppointmentDO> getsInternalMedicalAppointmentPage(IhcsInternalMedicalAppointmentPageReqVO pageReqVO) {
        return sInternalMedicalAppointmentDao.selectPage(pageReqVO);
    }

    @Override
    public List<IhcsInternalMedicalAppointmentDO> getsInternalMedicalAppointmentList(IhcsInternalMedicalAppointmentListReqVO listReqVO) {
        return sInternalMedicalAppointmentDao.selectList(listReqVO);
    }

    @Override
    public void auditMedicalAppointment(AuditMedicalAppointmentDTO auditMedicalAppointmentDTO) {

        SessionUser user = SessionUserUtil.getSessionUser();
        String id = auditMedicalAppointmentDTO.getId();
        IhcsInternalMedicalAppointmentDO ihcsInternalMedicalAppointment = this.getById(id);
        BizAssert.notNull(ihcsInternalMedicalAppointment, "预约信息不存在");
        BizAssert.isTrue(Objects.equals(ihcsInternalMedicalAppointment.getProcessMethod(),
                InternalMedicalAppointmentProcessMethodEnum.NO_PROCESS.getCode()), "预约信息已审核，不能重复审核");
        String processMethod = auditMedicalAppointmentDTO.getProcessMethod();
        IhcsInternalMedicalAppointmentDO updateInternalMedicalAppointment = IhcsInternalMedicalAppointmentDO.builder()
                .id(id)
                .processMethod(processMethod)
                .processUserName(user.getName())
                .processUserid(user.getId())
                .primaryDiagnosis(auditMedicalAppointmentDTO.getPrimaryDiagnosis())
                .diseaseLevel(auditMedicalAppointmentDTO.getDiseaseLevel())
                .processTime(new Date())
                .build();
        this.updateById(updateInternalMedicalAppointment);
        // 现场诊断需要加一份信息到现场诊断表中
        if (Objects.equals(processMethod, InternalMedicalAppointmentProcessMethodEnum.ON_SITE_DIAGNOSE.getCode())) {
            VisitDO ihcsInternalMedicalVisit = new VisitDO();
            ihcsInternalMedicalVisit.setSource(InternalMedicalVisitSourceConstant.APPOINTMENT_SOURCE);
            ihcsInternalMedicalVisit.setAppointmentId(ihcsInternalMedicalAppointment.getId());
            ihcsInternalMedicalVisit.setJgrybm(ihcsInternalMedicalAppointment.getSupervisedUserCode());
            ihcsInternalMedicalVisit.setSickType(ihcsInternalMedicalAppointment.getSickType());
            ihcsInternalMedicalVisit.setDiseaseTime(ihcsInternalMedicalAppointment.getDiseaseTime());
            ihcsInternalMedicalVisit.setDiseaseReason(ihcsInternalMedicalAppointment.getDiseaseReason());
            ihcsInternalMedicalVisit.setVisitProcessMethod(InternalMedicalVisitProcessMethodConstant.NO_PROCESS);
            ihcsInternalMedicalVisit.setPrisonId(ihcsInternalMedicalAppointment.getPrisonId());
            ihcsInternalMedicalVisit.setVisitUserid(SessionUserUtil.getSessionUser().getIdCard());
            ihcsInternalMedicalVisit.setVisitUserName(SessionUserUtil.getSessionUser().getName());
            visitDao.insert(ihcsInternalMedicalVisit);
        }
        // 所内就诊需要加一份信息到所内就诊表中
        if (Objects.equals(processMethod, InternalMedicalAppointmentProcessMethodEnum.INSIDE_THE_PRISON_DIAGNOSE.getCode())) {
            OutpatientDO ihcsInternalMedicalOutpatient = new OutpatientDO();
            ihcsInternalMedicalOutpatient.setSource(InternalMedicalOutpatientSourceConstant.APPOINTMENT_SOURCE);
            ihcsInternalMedicalOutpatient.setAppointmentId(ihcsInternalMedicalAppointment.getId());
            ihcsInternalMedicalOutpatient.setJgrybm(ihcsInternalMedicalAppointment.getSupervisedUserCode());
            ihcsInternalMedicalOutpatient.setSickType(ihcsInternalMedicalAppointment.getSickType());
            ihcsInternalMedicalOutpatient.setDiseaseTime(ihcsInternalMedicalAppointment.getDiseaseTime());
            ihcsInternalMedicalOutpatient.setDiseaseReason(ihcsInternalMedicalAppointment.getDiseaseReason());
            ihcsInternalMedicalOutpatient.setStatus(InternalMedicalOutpatientStatusConstant.NO_DIAGNOSIS);
            ihcsInternalMedicalOutpatient.setPrisonId(ihcsInternalMedicalAppointment.getPrisonId());
            outpatientDao.insert(ihcsInternalMedicalOutpatient);
        }
    }

    @Override
    public void batchAuditNoRequireProcess(BatchAuditNoRequireProcessDTO batchAuditNoRequireProcessDTO) {
        SessionUser user = SessionUserUtil.getSessionUser();
        List<String> ids = Optional.ofNullable(batchAuditNoRequireProcessDTO.getIds())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        BizAssert.notEmpty(ids, "预约信息的id集合不能为空");
        List<IhcsInternalMedicalAppointmentDO> ihcsInternalMedicalAppointments = this.getBaseMapper().selectBatchIds(ids);
        if (CollectionUtils.isEmpty(ihcsInternalMedicalAppointments)) {
            return;
        }
        String errorProcessMethodJoin = ihcsInternalMedicalAppointments.stream()
                .filter(ihcsInternalMedicalAppointment ->
                        // 找到所有的非未审核状态的数据
                        !Objects.equals(ihcsInternalMedicalAppointment.getProcessMethod(),
                                InternalMedicalAppointmentProcessMethodEnum.NO_PROCESS.getCode()))
                .map(IhcsInternalMedicalAppointmentDO::getAppointmentNum)
                .collect(Collectors.joining(","));
        BizAssert.isBlank(errorProcessMethodJoin, "预约号[" + errorProcessMethodJoin + "]已经审核，无法变更无需处理");
        this.lambdaUpdate()
                .set(IhcsInternalMedicalAppointmentDO::getProcessTime, LocalDateTime.now())
                .set(IhcsInternalMedicalAppointmentDO::getProcessMethod,
                        InternalMedicalAppointmentProcessMethodEnum.NO_REQUIRE_PROCESS.getCode())
                .set(IhcsInternalMedicalAppointmentDO::getDiseaseLevel,
                        InternalMedicalAppointmentDiseaseLevelEnum.NORMAL.getCode())
                .set(IhcsInternalMedicalAppointmentDO::getProcessUserName, user.getName())
                .set(IhcsInternalMedicalAppointmentDO::getProcessUserid, user.getId())
                .in(IhcsInternalMedicalAppointmentDO::getId, ids)
                .update();
    }

    /**
     * 判断是否重病号
     * <p>
     * 根据实战平台的判断逻辑
     */
    private Integer getPrisonerIsSickType(SaveMedicalAppointmentDTO saveMedicalAppointmentDTO) {
        String supervisedUserCode = saveMedicalAppointmentDTO.getSupervisedUserCode();
        return severelySickManageService.count(new LambdaQueryWrapper<SeverelySickManageDO>()
                .eq(SeverelySickManageDO::getJgrybm, supervisedUserCode)
                .eq(SeverelySickManageDO::getBusinessStatus, SpecialPatientStatusEnum.LISTED.getCode())
        );
    }

    /**
     * 生成预约编号
     * <p>
     * 预约编号规则：年月日加4位数字递增，例如：202407150001
     * 多预约同时时保证编号唯一的问题目前由唯一索引保证
     */
    private String createAppointmentNumber(LocalDateTime nowDateTime) {
        LocalDate nowDate = nowDateTime.toLocalDate();
        String maxAppointmentNumber = this.getBaseMapper().getMaxAppointmentNumber(LocalDateTime.of(nowDate, LocalTime.MIN), LocalDateTime.of(nowDate, LocalTime.MAX), null);
        int appointmentNumber = 0;
        int length = DateUtil.DATE_PATTERN.length();
        if (StringUtils.isNotBlank(maxAppointmentNumber)) {
            appointmentNumber = Integer.parseInt(maxAppointmentNumber.substring(length));
        }
        return String.format("%s%04d", DateUtil.DATE_FORMATTER.format(nowDate), appointmentNumber + 1);
    }

    @Override
    public GetMedicalAppointmentByIdVO getMedicalAppointmentById(String id) {
        GetMedicalAppointmentByIdVO getMedicalAppointmentByIdVO = this.getBaseMapper().getMedicalAppointmentById(id);
        BizAssert.notNull(getMedicalAppointmentByIdVO, "预约信息不存在");
        if (Objects.nonNull(getMedicalAppointmentByIdVO.getBirthday())) {
            getMedicalAppointmentByIdVO.setAge(ChronoUnit.YEARS.between(getMedicalAppointmentByIdVO.getBirthday(), LocalDateTime.now()));
        }
        return getMedicalAppointmentByIdVO;
    }


}
