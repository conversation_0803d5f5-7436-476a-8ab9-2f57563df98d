package com.rs.module.ihc.service.hc;

import cn.hutool.core.util.StrUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.ihc.constant.IhcsHealthCheckupConstant;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupAdditionalRecordVO;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupRespVO;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupSaveReqVO;
import com.rs.module.ihc.dao.hc.HealthCheckupDao;
import com.rs.module.ihc.entity.hc.HealthCheckupDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 五项体检 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HealthCheckupServiceImpl extends BaseServiceImpl<HealthCheckupDao, HealthCheckupDO> implements HealthCheckupService {

    @Resource
    private HealthCheckupDao healthCheckupDao;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private HealthCheckupFileService healthCheckupFileService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createHealthCheckup(HealthCheckupAdditionalRecordVO createReqVO) {
        // 插入
        HealthCheckupDO healthCheckup = BeanUtils.toBean(createReqVO, HealthCheckupDO.class);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        healthCheckup.setCheckupDoctor(sessionUser.getIdCard());
        healthCheckup.setCheckupDoctorName(sessionUser.getName());
        healthCheckup.setCheckupStatus(IhcsHealthCheckupConstant.CHECKUP_STATUS_DONE);
        healthCheckup.setEnterTime(new Date());
        healthCheckupDao.insert(healthCheckup);
        return healthCheckup.getId();
    }

    @Override
    public void updateHealthCheckup(HealthCheckupSaveReqVO updateReqVO) {
        // 校验存在
        HealthCheckupDO healthCheckupDO = healthCheckupDao.selectById(updateReqVO.getId());
        if (healthCheckupDO == null) {
            throw new RuntimeException("记录不存在！");
        }

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        if (StrUtil.isBlank(healthCheckupDO.getCheckupDoctor())) {
            healthCheckupDO.setCheckupDoctor(sessionUser.getIdCard());
            healthCheckupDO.setCheckupDoctorName(sessionUser.getName());
            healthCheckupDO.setCheckupStatus(IhcsHealthCheckupConstant.CHECKUP_STATUS_DONE);
            healthCheckupDO.setEnterTime(new Date());
        }
        BeanUtils.copyProperties(updateReqVO, healthCheckupDO);
        healthCheckupDao.updateById(healthCheckupDO);
    }

    @Override
    public HealthCheckupRespVO getHealthCheckup(String id) {
        HealthCheckupDO healthCheckupDO = healthCheckupDao.selectById(id);
        HealthCheckupRespVO checkupRespVO = BeanUtils.toBean(healthCheckupDO, HealthCheckupRespVO.class);
        return checkupRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void apply(String ids) {
        if (StrUtil.isNotBlank(ids)) {
            for (String id : ids.split(",")) {
                HealthCheckupDO healthCheckup = this.getById(id);
                healthCheckup.setCheckupStatus(IhcsHealthCheckupConstant.CHECKUP_STATUS_DOING);
                healthCheckupDao.updateById(healthCheckup);
            }
        }
    }

}
