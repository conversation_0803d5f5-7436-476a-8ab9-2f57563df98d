package com.rs.module.ihc.enums;

/**
 * 药品顾送状态
 *
 * <AUTHOR>
 * @date 2025/7/7 15:12
 */
public enum MedicineDeliveryApplyStatusEnum {
    DGS("004", "待顾送"),
    YWC("005", "已完成"),
    YZZ("006", "已终止");
    private String code;
    private String name;

    MedicineDeliveryApplyStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static MedicineDeliveryApplyStatusEnum getByCode(String code) {
        for (MedicineDeliveryApplyStatusEnum value : MedicineDeliveryApplyStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
