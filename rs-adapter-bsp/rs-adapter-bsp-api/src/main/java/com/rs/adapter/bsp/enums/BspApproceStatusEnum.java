package com.rs.adapter.bsp.enums;

public enum BspApproceStatusEnum {

    PASSED(Short.valueOf((short) 1), "同意"),
    NOT_PASSED(Short.valueOf((short) 2), "不同意"),
    REJECT(Short.valueOf((short) 3), "退回"),
    RECOVER(Short.valueOf((short) 4), "委派"),
    PASSED_END(Short.valueOf((short) 5), "同意,并结束流程"),
    NOT_PASSED_END(Short.valueOf((short) 6), "不同意，并结束流程");

    BspApproceStatusEnum(short code, String name) {
        this.code = code;
        this.name = name;
    }

    private short code;
    private String name;

    public short getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public static BspApproceStatusEnum getByCode(short code) {
        for (BspApproceStatusEnum bspApproceStatusEnum : BspApproceStatusEnum.values()) {
            if (bspApproceStatusEnum.getCode() == code) {
                return bspApproceStatusEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(short code) {
        for (BspApproceStatusEnum bspApproceStatusEnum : BspApproceStatusEnum.values()) {
            if (bspApproceStatusEnum.getCode() == code) {return bspApproceStatusEnum.getName();
            }
        }
        return null;
    }

}
