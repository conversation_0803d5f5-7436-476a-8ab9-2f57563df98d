package com.rs.adapter.bsp.service;

import java.util.List;
import java.util.Set;

import com.bsp.security.util.SessionUserUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.adapter.bsp.dao.UacUserDao;
import com.rs.adapter.bsp.entity.UacUserDO;

/**
 * 用户服务实现类
 * <AUTHOR>
 * @date 2025年3月24日
 */
@Service
public class UacUserServiceImpl extends BaseServiceImpl<UacUserDao, UacUserDO> implements UacUserService{

	/**
	 * 根据身份证号码查询用户照片
	 * @param idCards Set<String> 身份证号码
	 * @return List<UserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	public List<UacUserDO> getUserPhotoByIdCard(@Param("idCards") Set<String> idCards){
		return baseMapper.getUserPhotoByIdCard(idCards);
	}

	/**
	 * 根据机构代码与角色编号查询用户信息
	 * @param orgCode 机构代码
	 * @param roleCode 角色编号
	 * @return List<UserDO>
	 * <AUTHOR>
	 * @date 2025年3月24日
	 */
	@Override
	public List<UacUserDO> getUserByOrgAndRole(String orgCode, String roleCode) {
		return baseMapper.getUserByOrgAndRole(orgCode, roleCode);
	}

	/**
	 * 根据机构代码与岗位字典编号查询用户信息
	 * @param orgCode 机构代码
	 * @param postCode 岗位字典编码  ZD_POST 编码(01 窗口岗,02 巡控岗,03 管教岗,04 医务岗,05 综合岗,06 所领导,07 押解岗,08 后勤岗,09 收押岗,10 看守岗,11 教育康复岗,12 收戒岗,13 医疗岗,14 康复岗)
	 * @return
	 */
	@Override
	public List<UacUserDO> getUserByOrgAndPost(String orgCode, String postCode) {
		return baseMapper.getUserByOrgAndPost(orgCode, postCode);
	}

	/**
	 * 查询当前用户机构代码的指定岗位编码用户信息
	 * @param postCode
	 * @return
	 */
	@Override
	public List<UacUserDO> getUserByNowOrgAndPost(String postCode) {
		return baseMapper.getUserByOrgAndPost(SessionUserUtil.getSessionUser().getOrgCode(), postCode);
	}

	/**
	 * 根据机构代码查询用户信息
	 * @param orgCode
	 * @return
	 */
	@Override
	public List<UacUserDO> getUserByOrgCode(String orgCode) {
		return baseMapper.getUserByOrgCode(orgCode);
	}
}
