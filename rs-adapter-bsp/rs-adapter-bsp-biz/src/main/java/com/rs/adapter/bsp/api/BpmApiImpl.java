package com.rs.adapter.bsp.api;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.rs.adapter.bsp.api.dto.bpm.ProcessCmdDTO;
import com.rs.adapter.bsp.util.BspApiUtil;
import com.rs.adapter.bsp.util.BspHttpUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * RPC服务-API接口实现类，提供RESTful API接口，给Feign调用
 *
 * <AUTHOR>
 * @date 2025年4月14日
 */
@RestController
@Validated
@Api(tags = "bpm - api服务")
public class BpmApiImpl implements BpmApi {

    @Value("${bsp.bpmUrl}")
    private String baseUrl;

    @ApiOperation("获取流程启动用户")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "defKey", value = "流程定义Key", required = true),
		@ApiImplicitParam(name = "startUserId", value = "流程启动用户", required = true)
	})
    @Override
    public JSONObject getStartApproveUser(String defKey, String startUserId,String formProcessVar) {
        String uri = "/bpm/approveProcess/getStartApproveUser";
        Map<String, Object> map = new HashMap<>();
        map.put("defKey", BspApiUtil.buildDefKey(defKey));
        map.put("startUserId", startUserId);
        map.put("formProcessVar", formProcessVar);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result;
    }

    @ApiOperation("获取审批用户")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "actInstId", value = "流程实例Id", required = true),
		@ApiImplicitParam(name = "userId", value = "用户Id", required = true)
	})
    @Override
    public JSONObject getApproveUser(String actInstId, String userId, String formProcessVar) {
        String uri = "/bpm/approveProcess/getApproveUser";
        Map<String, Object> map = new HashMap<>();
        map.put("actInstId", actInstId);
        map.put("userId", userId);
        map.put("formProcessVar", formProcessVar);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result;
    }

    @ApiOperation("启动流程")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processCmd", value = "流程启动对象")
	})
    @Override
    public JSONObject startProcess(@RequestBody ProcessCmdDTO processCmd) {
        System.out.println("=============================================");
        String uri = "/bpm/approveProcess/startProcess";
        processCmd.setActDefKey(BspApiUtil.buildDefKey(processCmd.getActDefKey()));
        Map<String, Object> map = new HashMap<>();
        map.put("processCmd", JSONObject.parseObject(JSONObject.toJSONString(processCmd)));
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result;
    }

    @ApiOperation("流程审批")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "processCmd", value = "流程启动对象")
	})
    @Override
    public JSONObject approvalProcess(@RequestBody ProcessCmdDTO processCmd) {
        System.out.println("=============================================");
        System.out.println(processCmd);
        String uri = "/bpm/approveProcess/approvalProcess";
        processCmd.setActDefKey(BspApiUtil.buildDefKey(processCmd.getActDefKey()));
        Map<String, Object> map = new HashMap<>();
        map.put("processCmd", JSONObject.parseObject(JSONObject.toJSONString(processCmd)));
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result;
    }

    @ApiOperation("判断是否有审批权限")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "taskId", value = "任务Id", required = true),
		@ApiImplicitParam(name = "userId", value = "用户身份证号", required = true)
	})
    @Override
    public Boolean checkIsApproveAuthority(String taskId, String userId) {
        String uri = "/bpm/approveProcess/checkIsApproveAuthority";
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        map.put("userId", userId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result.getBoolean("hasApproveAuth");
    }

    @ApiOperation("获取流程审批轨迹")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "actInstId", value = "流程实例Id")
	})
    @Override
    public JSONObject approveTrack(String actInstId) {
        System.out.println("=============================================");
        String uri = "/bpm/approveProcess/approveTrack";
        Map<String, Object> map = new HashMap<>();
        map.put("actInstId", actInstId);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result;
    }

    @ApiOperation("判断流程是否结束")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "actInstId", value = "流程实例Id")
	})
    @Override
    public Boolean isFinishProcinst(String actInstId) {
        String uri = "/bpm/approveProcess/isFinishProcinst";
        Map<String, Object> map = new HashMap<>();
        map.put("actInstId", actInstId);
        String s = BspHttpUtil.HttpRequestGet(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result.getBoolean("isFinish");
    }

    @Override
    public JSONObject taskIdentityLinks(String actInstId, String param) {
        String uri = "/bpm/approveProcess/taskIdentityLinks";
        Map<String, Object> map = new HashMap<>();
        map.put("actInstId", actInstId);
        map.put("param", param);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result;
    }
    
    @Override
    public JSONObject currentApproveUser(String taskId) {
    	String uri = "/bpm/approveProcess/currentApproveUser";
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        String s = BspHttpUtil.HttpRequestPost(baseUrl + uri, map);
        JSONObject result = JSONObject.parseObject(s);
        return result;
    }
}
