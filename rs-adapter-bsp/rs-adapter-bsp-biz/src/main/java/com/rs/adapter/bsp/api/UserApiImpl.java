package com.rs.adapter.bsp.api;

import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.adapter.bsp.entity.UacUserDO;
import com.rs.adapter.bsp.service.UacUserService;
import com.rs.framework.common.util.object.BeanUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * RPC服务-用户实现类，提供RESTful API接口，给Feign调用
 * <AUTHOR>
 * @date 2025年4月13日
 */
@RestController
@Validated
@Api(tags = "bsp - 用户服务")
public class UserApiImpl implements UserApi {

	@Resource
	UacUserService userService;

	@Override
	@ApiOperation("获取用户照片信息")
	@ApiImplicitParam(name = "idCards", value = "身份证号码", example = "[540123200012304104]", required = true)
	public List<UserRespDTO> getUserPhotoByIdCard(Set<String> idCards){
		List<UacUserDO> userList = userService.getUserPhotoByIdCard(idCards);
		return BeanUtils.toBean(userList, UserRespDTO.class);
	}

	@Override
	@ApiOperation("根据机构代码与角色编号查询用户信息")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "orgCode", value = "机构代码", example = "789111000", required = true),
		@ApiImplicitParam(name = "roleCode", value = "角色编号", example = "900001", required = true)
	})
	public List<UserRespDTO> getUserByOrgAndRole(String orgCode, String roleCode){
		List<UacUserDO> userList = userService.getUserByOrgAndRole(orgCode, roleCode);
		return BeanUtils.toBean(userList, UserRespDTO.class);
	}
	@Override
	@ApiOperation("根据机构代码与岗位字典编码查询用户信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orgCode", value = "机构代码", example = "789111000", required = true),
			@ApiImplicitParam(name = "postCode", value = "岗位字典 ZD_POST 编码(01 窗口岗,02 巡控岗,03 管教岗,04 医务岗,05 综合岗,06 所领导,07 押解岗,08 后勤岗,09 收押岗,10 看守岗,11 教育康复岗,12 收戒岗,13 医疗岗,14 康复岗)", example = "", required = true)
	})
	public List<UserRespDTO> getUserByOrgAndPost(String orgCode, String postCode){
		List<UacUserDO> userList = userService.getUserByOrgAndPost(orgCode, postCode);
		return BeanUtils.toBean(userList, UserRespDTO.class);
	}
	@Override
	@ApiOperation("查询当前用户机构代码指定岗位字典编码用户信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "postCode", value = "岗位字典 ZD_POST 编码(01 窗口岗,02 巡控岗,03 管教岗,04 医务岗,05 综合岗,06 所领导,07 押解岗,08 后勤岗,09 收押岗,10 看守岗,11 教育康复岗,12 收戒岗,13 医疗岗,14 康复岗)", example = "", required = true)
	})
	public List<UserRespDTO> getUserByNowUserOrgAndPost(String postCode){
		List<UacUserDO> userList = userService.getUserByNowOrgAndPost(postCode);
		return BeanUtils.toBean(userList, UserRespDTO.class);
	}
	@Override
	@ApiOperation("根据机构代码查询用户信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orgCode", value = "机构代码", example = "789111000", required = true),
	})
	public List<UserRespDTO> getUserByOrgCode(String orgCode){
		List<UacUserDO> userList = userService.getUserByOrgCode(orgCode);
		return BeanUtils.toBean(userList, UserRespDTO.class);
	}
}
