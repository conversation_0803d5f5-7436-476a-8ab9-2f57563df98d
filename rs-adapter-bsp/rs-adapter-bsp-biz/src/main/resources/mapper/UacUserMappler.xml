<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.adapter.bsp.dao.UacUserDao">

    <!-- 根据身份证号码查询用户照片 -->
    <select id="getUserPhotoByIdCard" resultType="com.rs.adapter.bsp.entity.UacUserDO">
		SELECT u.ID as id, u.ID_CARD as idCard, u.NAME as name, u.ORG_CODE as orgCode, u.ORG_NAME as orgName,
			t.PHOTO as photo FROM uac_user_photo t, uac_user u
			where t.ID = u.ID and u.ID_CARD in
			<foreach collection="idCards" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
	</select>

	<!-- 根据机构代码和角色代码获取用户 -->
	<select id="getUserByOrgAndRole" resultType="com.rs.adapter.bsp.entity.UacUserDO">
		SELECT u.ID as id, u.ID_CARD as idCard,u.NAME as name,u.ORG_CODE as orgCode,u.ORG_NAME as orgName
		FROM uac_user u, uac_user_role ur, uac_role r
		where u.ID = ur.USER_ID
		  and ur.ROLE_ID = r.ID
		  and u.ORG_CODE = #{orgCode}
		  and r.CODE = #{roleCode}
	</select>

	<!-- 根据机构代码与岗位字典编号查询用户信息 -->
	<select id="getUserByOrgAndPost" resultType="com.rs.adapter.bsp.entity.UacUserDO">
		SELECT u.ID as id, u.ID_CARD as idCard,u.NAME as name,u.ORG_CODE as orgCode,u.ORG_NAME as orgName
		FROM uac_user u
		where u.ORG_CODE = #{orgCode}
			and u.post like concat('%',#{postCode},'%')
	</select>

	<!-- 根据机构代码查询用户信息 -->
	<select id="getUserByOrgCode" resultType="com.rs.adapter.bsp.entity.UacUserDO">
		SELECT u.ID as id, u.ID_CARD as idCard,u.NAME as name,u.ORG_CODE as orgCode,u.ORG_NAME as orgName
		FROM uac_user u
		where u.ORG_CODE = #{orgCode}
	</select>
</mapper>
