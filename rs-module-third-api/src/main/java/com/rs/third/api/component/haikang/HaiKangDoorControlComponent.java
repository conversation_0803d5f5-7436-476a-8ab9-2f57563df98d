package com.rs.third.api.component.haikang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.rs.third.api.config.HaiKangProperties;
import com.rs.third.api.dto.haikang.DoorControlDTO;
import com.rs.third.api.dto.haikang.DoorControlEventDTO;
import com.rs.third.api.dto.haikang.DoorStatusDTO;
import com.rs.third.api.enums.DoorControlTypeEnum;
import com.rs.third.api.model.DoorControlEventPageReq;
import com.rs.third.api.model.DoorControlPointPageReq;
import com.rs.third.api.model.PageApiResult;
import com.rs.third.api.model.SubscribeDoorEventsReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 海康门禁组件
 *
 * <AUTHOR>
 * @Date 2025/7/23 16:00
 */
@Slf4j
public class HaiKangDoorControlComponent extends HaiKangBaseComponent {


    public HaiKangDoorControlComponent(HaiKangProperties properties) {
        super(properties);
        init();
    }

    private void init() {
        // 初始化订阅事件
        HaiKangProperties properties = getProperties();
        String doorControlCallbackUrl = properties.getDoorControlCallbackUrl();
        String events = properties.getEvents();
        if (StringUtils.isNotEmpty(doorControlCallbackUrl) && StringUtils.isNotEmpty(events)) {
            // 先查询是否已经订阅过
            List<String> eventsList = Arrays.asList(events.split(","));
            List<String> subscribed = isSubscribed(eventsList);
            List<String> notSubscribed = new ArrayList<>();
            for (String event : eventsList) {
                if (!subscribed.contains(event)) {
                    notSubscribed.add(event);
                }
            }
            if (notSubscribed.size() > 0) {
                // 未订阅的事件，进行订阅
                SubscribeDoorEventsReq req = new SubscribeDoorEventsReq();
                req.setEventDest(doorControlCallbackUrl);
                req.setSubWay(1);
                req.setEventTypes(notSubscribed);
                JSONObject jsonObject = subscribeDoorEvents(req);
                if (jsonObject != null && !"0".equals(jsonObject.getString("code"))) {
                    log.error("订阅事件失败: {}", jsonObject.get("msg"));
                } else {
                    log.info("订阅事件成功: {}", notSubscribed);
                }
            }
        }
    }

    /**
     * v1.4 查询门禁点列表接口
     */
    public JSONObject queryDoorControlPoints(DoorControlPointPageReq pageReq) {
        String url = "/api/resource/v2/door/search";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("name", pageReq.getName());
        paramMap.put("regionIndexCodes", pageReq.getRegionIndexCodes());
        paramMap.put("isSubRegion", pageReq.getIsSubRegion());
        paramMap.put("pageNo", pageReq.getPageNo());
        paramMap.put("pageSize", pageReq.getPageSize());
        paramMap.put("orderBy", pageReq.getOrderBy());
        paramMap.put("orderType", pageReq.getOrderType());
        String request = null;
        try {
            request = sendRequest(paramMap, url);
        } catch (Exception e) {
            log.error("查询门禁点列表失败：{}", e);
        }
        if (request != null) {
            JSONObject jsonObject = JSON.parseObject(request);
            return jsonObject;
        }
        return null;
    }

    /**
     * v1.4	查询门禁点状态
     * 该接口支持门常开、门常闭、门开和门闭四种操作引起的门状态获取。门常开操作，
     * 门会一直处于开状态，不会自动关闭，执行门闭操作，门才会关上；
     * 门常闭操作，门会一直处于关毕状态，普通卡刷卡门不会被打开，执行门开操作，门会打开；门开操作，执行门打开动作，超过门打开时间，
     * 门会自动关上；门闭操作，执行关门动作，会立即把门关上。调用该接口，首先要通过获取门禁点资源列表的接口，
     * 获取到门禁点唯一编号，然后根据门禁点唯一编号进行门禁点状态状态查询。
     * 需要注意的是门通道必须接上门磁才能正常发送门状态变化通知，如果未接门磁，
     * 平台无法通过门状态变更通知来更新门状态。
     */
    public List<DoorStatusDTO> doorStatusQuery(List<String> doorIndexCodes) {
        String url = "/api/acs/v1/door/states";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("doorIndexCodes", doorIndexCodes);
        List<DoorStatusDTO> dtosList = new ArrayList<>();
        String response = null;
        try {
            response = sendRequest(paramMap, url);
        } catch (Exception e) {
            log.error("查询门禁点状态失败：{}", e);
        }
        if (response != null) {
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject == null || !"0".equals(jsonObject.getString("code"))) {
                log.error("查询门禁点状态失败: {}", jsonObject.get("msg"));
                throw new RuntimeException("查询门禁点状态失败: " + jsonObject.get("msg"));
            }
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray jsonArray = data.getJSONArray("authDoorList");
            for (int i = 0; i < jsonArray.size(); i++) {
                // 将 JSONArray 中的每个元素转换为 DoorControlPointDTO 对象
                DoorStatusDTO dto = jsonArray.getJSONObject(i).toJavaObject(DoorStatusDTO.class);
                dtosList.add(dto);
            }
        }
        return dtosList;
    }

    /**
     * v1.4	门禁点反控
     * 单个编号的门禁点反控
     *
     * @param doorIndexCode
     * @param controlType
     * @return
     */
    public JSONObject controlDoor(String doorIndexCode, DoorControlTypeEnum controlType) {
        List<DoorControlDTO> doorControlDTOS = controlDoor(new ArrayList<String>() {{
            add(doorIndexCode);
        }}, controlType);
        JSONObject jsonObject = new JSONObject();
        if (doorControlDTOS != null && doorControlDTOS.size() > 0) {
            DoorControlDTO doorControlDTO = doorControlDTOS.get(0);
            jsonObject.put("doorIndexCode", doorControlDTO.getDoorIndexCode());
            jsonObject.put("controlResultCode", doorControlDTO.getControlResultCode());
            jsonObject.put("controlResultDesc", doorControlDTO.getControlResultDesc());
        }
        return jsonObject;
    }

    /**
     * v1.4	门禁点反控
     * 该接口支持门常开、门常闭、门开和门闭四种操作。门常开操作，门会一直处于开状态，不会自动关闭，执行门闭操作，门才会关上；
     * 门常闭操作，门会一直处于关毕状态，普通卡刷卡门不会被打开，执行门开操作，门会打开；
     * 门开操作，执行门打开动作，超过门打开时间，门会自动关上；门闭操作，执行关门动作，会立即把门关上。
     * 调用该接口，首先要通过获取门禁点资源列表的接口，获取到门禁点唯一编号，然后根据门禁点唯一编号进行反控操作，该接口支持单个和多个门禁点操作，
     * 如果所有门禁点反控操作成功，则返回成功，其他情况都返回失败，在失败的情况下，会按每个门禁点返回对应的错误。
     */
    public List<DoorControlDTO> controlDoor(List<String> doorIndexCodes, DoorControlTypeEnum controlType) {
        String url = "/api/acs/v1/door/doControl";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("doorIndexCodes", doorIndexCodes);
        paramMap.put("controlType", controlType.getCode());
        List<DoorControlDTO> list = new ArrayList<>();
        String response = null;
        try {
            response = sendRequest(paramMap, url);
        } catch (Exception e) {
            log.error("查询门禁点反控失败：{}", e);
        }
        if (response != null) {
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject == null || !"0".equals(jsonObject.getString("code"))) {
                log.error("门禁点反控失败: {}", jsonObject.get("msg"));
                throw new RuntimeException("门禁点反控失败: " + jsonObject.get("msg"));
            }
            list = jsonObject.getObject("data", new TypeReference<List<DoorControlDTO>>() {
            });
        }
        return list;
    }

    /**
     * 1.4	查询门禁点事件v2
     * 功能描述：该接口可以查询发生在门禁点上的人员出入事件，支持多个维度来查询，支持按时间、人员、门禁点、事件类型四个维度来查询；
     * 其中按事件类型来查询的方式，如果查询不到事件，存在两种情况，一种是该类型的事件没有发生过，所以查询不到，还有一种情况，
     * 该类型的事件发生过，但是由于门禁管理组件对该事件类型订阅配置处于关闭状态，
     * 所以不会存储该类型的事件，导致查询不到，对于这种情况，需要到门禁管理组件中，将该事件类型的订阅配置打开。
     */
    public PageApiResult<DoorControlEventDTO> queryDoorEvents(DoorControlEventPageReq pageReq) {
        String url = "/api/acs/v2/door/events";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("pageNo", pageReq.getPageNo());
        paramMap.put("pageSize", pageReq.getPageSize());
        paramMap.put("startTime", formatISO8601WithTimezone(pageReq.getStartTime()));
        paramMap.put("endTime", formatISO8601WithTimezone(pageReq.getEndTime()));
        paramMap.put("receiveStartTime", formatISO8601WithTimezone(pageReq.getReceiveStartTime()));
        paramMap.put("receiveEndTime", formatISO8601WithTimezone(pageReq.getReceiveEndTime()));
        paramMap.put("doorIndexCodes", pageReq.getDoorIndexCodes());
        paramMap.put("eventTypes", pageReq.getEventTypes());
        paramMap.put("sort", pageReq.getSort());
        paramMap.put("order", pageReq.getOrder());
        String response = null;
        try {
            response = sendRequest(paramMap, url);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        PageApiResult pageApiResult = new PageApiResult();
        if (response != null) {
            JSONObject jsonObject = JSON.parseObject(response);
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("查询门禁点事件失败: {}", jsonObject.get("msg"));
                throw new RuntimeException("查询门禁点事件失败: " + jsonObject.get("msg"));
            }
            JSONObject data = jsonObject.getJSONObject("data");
            Long total = data.getLong("total");
            pageApiResult.setTotal(total);
            JSONArray jsonArray = data.getJSONArray("list");
            List<DoorControlEventDTO> dtos = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                // 将 JSONArray 中的每个元素转换为 DoorControlEventDTO 对象
                DoorControlEventDTO dto = jsonArray.getJSONObject(i).toJavaObject(DoorControlEventDTO.class);
                dtos.add(dto);
            }
            pageApiResult.setList(dtos);
            return pageApiResult;
        } else {
            pageApiResult.setList(new ArrayList());
            pageApiResult.setTotal(0L);
        }
        return pageApiResult;
    }

    /**
     * v1.4	支持按MQTT协议连接rmq或事件回调方式订阅实时事件，该接口用于满足应用方按事件类型码订阅事件。
     * 回调地址收到事件后，需要返回http响应码200，否则平台会认为订阅方没有收到此事件
     */
    public JSONObject subscribeDoorEvents(SubscribeDoorEventsReq eventsReq) {
        final String API_PATH = "/api/eventService/v1/eventSubscriptionByEventTypes";
        Map<String, Object> paramMap = new HashMap<>(3);
        paramMap.put("eventTypes", eventsReq.getEventTypes());
        paramMap.put("subWay", 1);
        paramMap.put("eventDest", eventsReq.getEventDest());
        try {
            String response = sendRequest(paramMap, API_PATH);
            return JSON.parseObject(response);
        } catch (Exception e) {
            log.error("订阅失败：{}", e);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 1);
        jsonObject.put("msg", "FAIL");
        return jsonObject;
    }

    /**
     * 查询事件订阅详情（支持MQTT/回调方式）
     *
     * @param subWay 包含subWay参数的查询请求对象
     * @return 标准化响应格式（包含data详情）
     */
    public JSONObject getSubscriptionDetail(int subWay) {
        final String API_PATH = "/api/eventService/v1/eventSubscriptionView";
        // 构造符合规范的请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("subWay", 1); // 默认值处理
        String response = null;
        try {
            // 发送带版本标识的请求
            response = sendRequest(requestBody, API_PATH);
            return JSON.parseObject(response);
        } catch (Exception e) {
            log.error("响应异常: {}", e.getMessage());
        }
        // 构造失败响应
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 1);
        jsonObject.put("msg", "FAIL");
        return jsonObject;
    }

    /**
     * 查询事件订阅详情（支持回调方式）
     *
     * @param eventTypes
     * @return
     */
    public List<String> isSubscribed(List<String> eventTypes) {
        JSONObject jsonObject = getSubscriptionDetail(1);
        if (jsonObject.getIntValue("code") == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null) {
                JSONArray jsonArray = data.getJSONArray("detail");
                if (jsonArray != null && jsonArray.size() > 0) {
                    List<String> subscribed = new ArrayList<>();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject arrayJSONObject = jsonArray.getJSONObject(i);
                        List<String> types = arrayJSONObject.getObject("eventTypes", new TypeReference<List<String>>() {
                        });
                        subscribed.addAll(types);
                    }
                    return subscribed;
                }
            }
        }
        return new ArrayList<>(0);
    }

    /**
     * 按事件类型取消订阅
     *
     * @param eventTypes 包含事件类型
     * @return 标准化取消操作结果
     */
    public JSONObject unsubscribeByEventTypes(List<String> eventTypes) {
        final String API_PATH = "/api/eventService/v1/eventUnSubscriptionByEventTypes";
        // 构建符合规范的请求体
        Map<String, Object> requestBody = new HashMap<>(2);
        requestBody.put("eventTypes", eventTypes);
        String response = null;
        try {
            response = sendRequest(requestBody, API_PATH);
            return JSON.parseObject(response);
        } catch (Exception e) {
            log.error("服务通信异常: {}", e.getMessage());
        }
        // 构造失败响应
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 1);
        jsonObject.put("msg", "FAIL");
        return jsonObject;
    }


}
